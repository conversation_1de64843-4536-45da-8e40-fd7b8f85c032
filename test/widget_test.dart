// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:irecharge_pro/core/services/storage_service.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/main.dart';
import 'package:mockito/mockito.dart';

// Mock StorageService for testing
class MockStorageService extends Mock implements StorageService {}

void main() {
  final mockStorageService = MockStorageService();

  testWidgets('App initializes correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          storageServiceProvider.overrideWithValue(mockStorageService),
        ],
        child: const MyApp(),
      ),
    );

    // Verify the app initializes without errors
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}

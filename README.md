# iRecharge Pro - Bangladesh Digital Services App

A Flutter-based mobile application that provides essential telecom, payment, and e-commerce services for users in Bangladesh.

## Features

### 1. Mobile Recharge & Offer Package
- Support for all major operators (Grameenphone, Robi, Airtel, Banglalink, Teletalk)
- Custom and predefined recharge amounts
- Offer package list (data, minutes, combo)
- Package activation
- Recharge history and invoices

### 2. Mobile Banking
- Support for popular mobile banking services (bKash, Nagad, Rocket, etc.)
- Send Money functionality
- Mobile number-based transfers (Personal, Agent, Merchant)
- Transaction verification

### 3. Utility Bill Payment
- Electricity, Gas, Water bill payments
- Internet & Cable TV bills
- Government exam/admission fees
- Bill fetch and payment history

### 4. E-Commerce
- Product browsing and shopping
- Cart and checkout functionality
- Order tracking
- Promotions and offers

### 5. Add Money
- Add funds from linked MFS wallets
- Debit/Credit card support
- Transaction history

## Technical Stack

- **Frontend Framework**: Flutter (Dart)
- **State Management**: Riverpod
- **Backend Communication**: REST API
- **Local Storage**: Hive / Shared Preferences
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Language Support**: Bangla and English

## Getting Started

### Prerequisites
- Flutter SDK (latest stable version)
- Android Studio / VS Code
- Android SDK / Xcode (for iOS development)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/irecharge_pro.git
```

2. Navigate to the project directory:
```bash
cd irecharge_pro
```

3. Install dependencies:
```bash
flutter pub get
```

4. Run the app:
```bash
flutter run
```

## Project Structure

```
lib/
├── core/                 # Core functionality
│   ├── constants/        # App constants
│   ├── services/         # Core services
│   ├── theme/            # App theme
│   ├── utils/            # Utility functions
│   └── widgets/          # Common widgets
├── data/                 # Data layer
│   ├── models/           # Data models
│   ├── providers/        # Riverpod providers
│   ├── repositories/     # Data repositories
│   └── services/         # API services
├── features/             # App features
│   ├── auth/             # Authentication
│   ├── banking/          # Mobile banking
│   ├── bills/            # Bill payments
│   ├── ecommerce/        # E-commerce
│   ├── home/             # Home screen
│   ├── recharge/         # Mobile recharge
│   └── wallet/           # Wallet management
├── l10n/                 # Localization
├── routes/               # App routing
└── main.dart             # App entry point
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Flutter team for the amazing framework
- All the package authors used in this project

import 'package:irecharge_pro/core/utils/logger_util.dart';
import 'package:irecharge_pro/data/models/offer_package_model.dart';
import 'package:irecharge_pro/data/models/operator_model.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';

/// Repository for handling mobile recharge operations
class RechargeRepository {
  /// Get all operators
  Future<List<Operator>> getOperators() async {
    try {
      // In a real app, this would fetch from an API
      return BangladeshOperators.getAll();
    } catch (e) {
      LoggerUtil.e('Error fetching operators', e);
      return [];
    }
  }

  /// Get operator by ID
  Future<Operator?> getOperatorById(String operatorId) async {
    try {
      final operators = await getOperators();
      return operators.firstWhere(
        (op) => op.id == operatorId,
        orElse: () => throw Exception('Operator not found'),
      );
    } catch (e) {
      LoggerUtil.e('Error fetching operator by ID', e);
      return null;
    }
  }

  /// Detect operator from phone number
  Future<Operator?> detectOperator(String phoneNumber) async {
    try {
      return BangladeshOperators.detectFromPhoneNumber(phoneNumber);
    } catch (e) {
      LoggerUtil.e('Error detecting operator', e);
      return null;
    }
  }

  /// Get all offer packages
  Future<List<OfferPackage>> getOfferPackages() async {
    try {
      // In a real app, this would fetch from an API
      return SampleOfferPackages.getAll();
    } catch (e) {
      LoggerUtil.e('Error fetching offer packages', e);
      return [];
    }
  }

  /// Get offer packages by operator
  Future<List<OfferPackage>> getOfferPackagesByOperator(String operatorId) async {
    try {
      return SampleOfferPackages.getByOperator(operatorId);
    } catch (e) {
      LoggerUtil.e('Error fetching offer packages by operator', e);
      return [];
    }
  }

  /// Get featured offer packages
  Future<List<OfferPackage>> getFeaturedOfferPackages() async {
    try {
      return SampleOfferPackages.getFeatured();
    } catch (e) {
      LoggerUtil.e('Error fetching featured offer packages', e);
      return [];
    }
  }

  /// Get offer package by ID
  Future<OfferPackage?> getOfferPackageById(String packageId) async {
    try {
      final packages = await getOfferPackages();
      return packages.firstWhere(
        (pkg) => pkg.id == packageId,
        orElse: () => throw Exception('Package not found'),
      );
    } catch (e) {
      LoggerUtil.e('Error fetching package by ID', e);
      return null;
    }
  }

  /// Get recharge transactions
  Future<List<RechargeTransaction>> getRechargeTransactions() async {
    try {
      // In a real app, this would fetch from an API
      return SampleRechargeTransactions.getAll();
    } catch (e) {
      LoggerUtil.e('Error fetching recharge transactions', e);
      return [];
    }
  }

  /// Get recent recharge transactions
  Future<List<RechargeTransaction>> getRecentRechargeTransactions(int count) async {
    try {
      return SampleRechargeTransactions.getRecent(count);
    } catch (e) {
      LoggerUtil.e('Error fetching recent recharge transactions', e);
      return [];
    }
  }

  /// Get recharge transaction by ID
  Future<RechargeTransaction?> getRechargeTransactionById(String transactionId) async {
    try {
      final transactions = await getRechargeTransactions();
      return transactions.firstWhere(
        (tx) => tx.id == transactionId,
        orElse: () => throw Exception('Transaction not found'),
      );
    } catch (e) {
      LoggerUtil.e('Error fetching transaction by ID', e);
      return null;
    }
  }

  /// Perform mobile recharge
  Future<RechargeTransaction?> performRecharge({
    required String operatorId,
    required String phoneNumber,
    required double amount,
    required PaymentMethod paymentMethod,
    String? packageId,
  }) async {
    try {
      // In a real app, this would call an API
      // For now, we'll simulate a successful recharge
      
      // Generate a unique transaction ID
      final transactionId = 'tx_${DateTime.now().millisecondsSinceEpoch}';
      
      // Create a new transaction
      final transaction = RechargeTransaction(
        id: transactionId,
        operatorId: operatorId,
        phoneNumber: phoneNumber,
        amount: amount,
        timestamp: DateTime.now(),
        status: RechargeStatus.completed, // Simulate success
        paymentMethod: paymentMethod,
        packageId: packageId,
        transactionReference: 'REF${DateTime.now().millisecondsSinceEpoch}',
      );
      
      // In a real app, we would save this transaction to a database
      
      return transaction;
    } catch (e) {
      LoggerUtil.e('Error performing recharge', e);
      return null;
    }
  }

  /// Activate offer package
  Future<bool> activateOfferPackage({
    required String packageId,
    required String phoneNumber,
    required PaymentMethod paymentMethod,
  }) async {
    try {
      // Get the package
      final package = await getOfferPackageById(packageId);
      if (package == null) {
        throw Exception('Package not found');
      }
      
      // Perform recharge with the package
      final transaction = await performRecharge(
        operatorId: package.operatorId,
        phoneNumber: phoneNumber,
        amount: package.price,
        paymentMethod: paymentMethod,
        packageId: packageId,
      );
      
      return transaction != null;
    } catch (e) {
      LoggerUtil.e('Error activating offer package', e);
      return false;
    }
  }
}

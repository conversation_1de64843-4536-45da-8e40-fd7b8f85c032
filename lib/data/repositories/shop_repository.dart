import 'package:irecharge_pro/data/models/product_model.dart';

/// Repository for shop-related data
class ShopRepository {
  /// Get all product categories
  Future<List<ProductCategory>> getProductCategories() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Return demo categories
    return DemoProductCategories.getAll();
  }
  
  /// Get a product category by ID
  Future<ProductCategory?> getProductCategoryById(String categoryId) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Return demo category
    return DemoProductCategories.getById(categoryId);
  }
  
  /// Get all products
  Future<List<Product>> getProducts() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Return demo products
    return DemoProducts.getAll();
  }
  
  /// Get featured products
  Future<List<Product>> getFeaturedProducts() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 600));
    
    // Return featured demo products
    return DemoProducts.getFeatured();
  }
  
  /// Get products by category
  Future<List<Product>> getProductsByCategory(String categoryId) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 700));
    
    // Return demo products by category
    return DemoProducts.getByCategory(categoryId);
  }
  
  /// Get a product by ID
  Future<Product?> getProductById(String productId) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 400));
    
    // Return demo product
    return DemoProducts.getById(productId);
  }
  
  /// Search products by query
  Future<List<Product>> searchProducts(String query) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 900));
    
    if (query.isEmpty) {
      return [];
    }
    
    // Search demo products
    final lowercaseQuery = query.toLowerCase();
    return DemoProducts.getAll().where((product) {
      return product.name.toLowerCase().contains(lowercaseQuery) ||
          product.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/bank_model.dart';

/// Bank list provider
final banksProvider = Provider<List<Bank>>((ref) {
  return Bank.getAllBanks();
});

/// Popular banks provider
final popularBanksProvider = Provider<List<Bank>>((ref) {
  return Bank.getPopularBanks();
});

/// Selected bank provider
final selectedBankProvider = StateProvider<Bank?>((ref) => null);

/// Bank transfers provider
final bankTransfersProvider = StateNotifierProvider<BankTransferNotifier, List<BankTransfer>>((ref) {
  return BankTransferNotifier();
});

/// Bank transfer filter provider
final bankTransferFilterProvider = StateProvider<BankTransferFilter>((ref) {
  return const BankTransferFilter();
});

/// Bank transfer notifier
class BankTransferNotifier extends StateNotifier<List<BankTransfer>> {
  BankTransferNotifier() : super(SampleBankTransfers.getAll());

  /// Add a new bank transfer
  void addBankTransfer(BankTransfer transfer) {
    state = [...state, transfer];
  }

  /// Get bank transfers by status
  List<BankTransfer> getByStatus(BankTransferStatus status) {
    return state.where((transfer) => transfer.status == status).toList();
  }

  /// Get bank transfers by bank ID
  List<BankTransfer> getByBankId(String bankId) {
    return state.where((transfer) => transfer.bankId == bankId).toList();
  }

  /// Get bank transfers by date range
  List<BankTransfer> getByDateRange(DateTime startDate, DateTime endDate) {
    return state.where((transfer) => 
      transfer.date.isAfter(startDate) && 
      transfer.date.isBefore(endDate.add(const Duration(days: 1)))
    ).toList();
  }

  /// Get filtered bank transfers
  List<BankTransfer> getFilteredTransfers(BankTransferFilter filter) {
    return state.where((transfer) {
      // Apply status filter
      if (filter.status != null && transfer.status != filter.status) {
        return false;
      }
      
      // Apply bank filter
      if (filter.bankId != null && transfer.bankId != filter.bankId) {
        return false;
      }
      
      // Apply date range filter
      if (filter.startDate != null && transfer.date.isBefore(filter.startDate!)) {
        return false;
      }
      if (filter.endDate != null && transfer.date.isAfter(filter.endDate!.add(const Duration(days: 1)))) {
        return false;
      }
      
      // Apply search query filter
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        return transfer.accountName.toLowerCase().contains(query) ||
               transfer.accountNumber.toLowerCase().contains(query) ||
               transfer.id.toLowerCase().contains(query) ||
               (transfer.reference?.toLowerCase().contains(query) ?? false) ||
               (transfer.note?.toLowerCase().contains(query) ?? false);
      }
      
      return true;
    }).toList();
  }

  /// Update bank transfer status
  void updateStatus(String id, BankTransferStatus status) {
    state = state.map((transfer) {
      if (transfer.id == id) {
        return BankTransfer(
          id: transfer.id,
          bankId: transfer.bankId,
          accountNumber: transfer.accountNumber,
          accountName: transfer.accountName,
          accountType: transfer.accountType,
          amount: transfer.amount,
          date: transfer.date,
          reference: transfer.reference,
          note: transfer.note,
          status: status,
          transactionId: transfer.transactionId,
        );
      }
      return transfer;
    }).toList();
  }

  /// Generate new bank transfer ID
  String generateId() {
    return SampleBankTransfers.generateId();
  }
}

/// Bank transfer filter
class BankTransferFilter {
  final BankTransferStatus? status;
  final String? bankId;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const BankTransferFilter({
    this.status,
    this.bankId,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  /// Create a copy with updated fields
  BankTransferFilter copyWith({
    BankTransferStatus? status,
    String? bankId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    bool clearStatus = false,
    bool clearBankId = false,
    bool clearStartDate = false,
    bool clearEndDate = false,
    bool clearSearchQuery = false,
  }) {
    return BankTransferFilter(
      status: clearStatus ? null : status ?? this.status,
      bankId: clearBankId ? null : bankId ?? this.bankId,
      startDate: clearStartDate ? null : startDate ?? this.startDate,
      endDate: clearEndDate ? null : endDate ?? this.endDate,
      searchQuery: clearSearchQuery ? null : searchQuery ?? this.searchQuery,
    );
  }
}

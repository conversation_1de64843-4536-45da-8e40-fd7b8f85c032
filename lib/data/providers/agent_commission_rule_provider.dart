import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/agent_commission_rule_model.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/models/service_commission_model.dart';

/// Agent Commission Rules list provider
final agentCommissionRulesProvider = StateNotifierProvider<AgentCommissionRuleNotifier, List<AgentCommissionRule>>((ref) {
  return AgentCommissionRuleNotifier();
});

/// Selected agent commission rule provider
final selectedAgentCommissionRuleProvider = StateProvider<AgentCommissionRule?>((ref) => null);

/// Agent Commission Rule filter provider
final agentCommissionRuleFilterProvider = StateProvider<AgentCommissionRuleFilter>((ref) {
  return const AgentCommissionRuleFilter();
});

/// Agent Commission Rule filter
class AgentCommissionRuleFilter {
  final String? agentId;
  final AgentCustomerType? agentType;
  final ServiceType? serviceType;
  final String? operatorId;
  final bool? isActive;
  final String? searchQuery;

  const AgentCommissionRuleFilter({
    this.agentId,
    this.agentType,
    this.serviceType,
    this.operatorId,
    this.isActive,
    this.searchQuery,
  });

  /// Copy with
  AgentCommissionRuleFilter copyWith({
    String? agentId,
    AgentCustomerType? agentType,
    ServiceType? serviceType,
    String? operatorId,
    bool? isActive,
    String? searchQuery,
  }) {
    return AgentCommissionRuleFilter(
      agentId: agentId ?? this.agentId,
      agentType: agentType ?? this.agentType,
      serviceType: serviceType ?? this.serviceType,
      operatorId: operatorId ?? this.operatorId,
      isActive: isActive ?? this.isActive,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
  
  /// Clear filter
  AgentCommissionRuleFilter clear() {
    return const AgentCommissionRuleFilter();
  }
}

/// Agent Commission Rule notifier
class AgentCommissionRuleNotifier extends StateNotifier<List<AgentCommissionRule>> {
  AgentCommissionRuleNotifier() : super(DemoAgentCommissionRules.agentCommissionRules);
  
  /// Add a new agent commission rule
  void addAgentCommissionRule(AgentCommissionRule rule) {
    state = [...state, rule];
  }
  
  /// Update an existing agent commission rule
  void updateAgentCommissionRule(AgentCommissionRule rule) {
    state = state.map((r) => r.id == rule.id ? rule : r).toList();
  }
  
  /// Delete an agent commission rule
  void deleteAgentCommissionRule(String id) {
    state = state.where((r) => r.id != id).toList();
  }
  
  /// Toggle agent commission rule active status
  void toggleAgentCommissionRuleStatus(String id) {
    state = state.map((r) => r.id == id ? r.copyWith(isActive: !r.isActive, updatedAt: DateTime.now()) : r).toList();
  }
  
  /// Get filtered agent commission rules
  List<AgentCommissionRule> getFilteredAgentCommissionRules(AgentCommissionRuleFilter filter) {
    return state.where((rule) {
      // Apply agent ID filter
      if (filter.agentId != null && rule.agentId != filter.agentId) {
        return false;
      }
      
      // Apply agent type filter
      if (filter.agentType != null && rule.agentType != filter.agentType) {
        return false;
      }
      
      // Apply service type filter
      if (filter.serviceType != null && rule.serviceType != filter.serviceType) {
        return false;
      }
      
      // Apply operator filter
      if (filter.operatorId != null && rule.operatorId != filter.operatorId) {
        return false;
      }
      
      // Apply active status filter
      if (filter.isActive != null && rule.isActive != filter.isActive) {
        return false;
      }
      
      // Apply search query filter
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        return rule.id.toLowerCase().contains(query) ||
               (rule.operatorName?.toLowerCase().contains(query) ?? false);
      }
      
      return true;
    }).toList();
  }
  
  /// Get agent commission rules by agent ID
  List<AgentCommissionRule> getAgentCommissionRulesByAgentId(String agentId) {
    return state.where((rule) => rule.agentId == agentId).toList();
  }
  
  /// Get agent commission rules by service type
  List<AgentCommissionRule> getAgentCommissionRulesByServiceType(ServiceType type) {
    return state.where((rule) => rule.serviceType == type).toList();
  }
  
  /// Get agent commission rules by operator
  List<AgentCommissionRule> getAgentCommissionRulesByOperator(String operatorId) {
    return state.where((rule) => rule.operatorId == operatorId).toList();
  }
  
  /// Get active agent commission rules
  List<AgentCommissionRule> getActiveAgentCommissionRules() {
    return state.where((rule) => rule.isActive).toList();
  }
  
  /// Find applicable commission rule for a transaction
  AgentCommissionRule? findApplicableCommissionRule({
    required String agentId,
    required ServiceType serviceType,
    String? operatorId,
    double? amount,
  }) {
    final rules = state.where((rule) => 
      rule.agentId == agentId && 
      rule.serviceType == serviceType && 
      rule.isActive &&
      (operatorId == null || rule.operatorId == operatorId) &&
      (amount == null || 
        ((rule.minTransactionAmount == null || amount >= rule.minTransactionAmount!) && 
         (rule.maxTransactionAmount == null || amount <= rule.maxTransactionAmount!)))
    ).toList();
    
    if (rules.isEmpty) {
      return null;
    }
    
    // Sort by specificity (operator-specific rules first)
    rules.sort((a, b) {
      // Operator-specific rules take precedence
      if (a.operatorId != null && b.operatorId == null) return -1;
      if (a.operatorId == null && b.operatorId != null) return 1;
      
      // Then by amount range specificity
      final aHasAmountRange = a.minTransactionAmount != null || a.maxTransactionAmount != null;
      final bHasAmountRange = b.minTransactionAmount != null || b.maxTransactionAmount != null;
      if (aHasAmountRange && !bHasAmountRange) return -1;
      if (!aHasAmountRange && bHasAmountRange) return 1;
      
      // Then by creation date (newer rules first)
      return b.createdAt.compareTo(a.createdAt);
    });
    
    return rules.first;
  }
  
  /// Calculate commission for a transaction
  double calculateCommissionForTransaction({
    required String agentId,
    required ServiceType serviceType,
    required double amount,
    String? operatorId,
  }) {
    final rule = findApplicableCommissionRule(
      agentId: agentId,
      serviceType: serviceType,
      operatorId: operatorId,
      amount: amount,
    );
    
    if (rule == null) {
      return 0;
    }
    
    return rule.calculateCommission(amount);
  }
  
  /// Calculate service charge for a transaction
  double calculateServiceChargeForTransaction({
    required String agentId,
    required ServiceType serviceType,
    required double amount,
    String? operatorId,
  }) {
    final rule = findApplicableCommissionRule(
      agentId: agentId,
      serviceType: serviceType,
      operatorId: operatorId,
      amount: amount,
    );
    
    if (rule == null) {
      return 0;
    }
    
    return rule.calculateServiceCharge(amount);
  }
  
  /// Generate a new agent commission rule ID
  String generateAgentCommissionRuleId() {
    return DemoAgentCommissionRules.generateId();
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/shop_transaction_model.dart';

/// Shop transactions provider
final shopTransactionsProvider = Provider<List<ShopTransaction>>((ref) {
  return DemoShopTransactions.transactions;
});

/// Shop transaction filter provider
final shopTransactionFilterProvider = StateProvider<ShopTransactionFilter>((ref) {
  return const ShopTransactionFilter();
});

/// Shop transaction filter
class ShopTransactionFilter {
  final ShopTransactionStatus? status;
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;

  const ShopTransactionFilter({
    this.status,
    this.searchQuery,
    this.startDate,
    this.endDate,
  });

  /// Copy with
  ShopTransactionFilter copyWith({
    ShopTransactionStatus? status,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    return ShopTransactionFilter(
      status: status ?? this.status,
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
    );
  }

  /// Clear filter
  ShopTransactionFilter clear() {
    return const ShopTransactionFilter();
  }
}

/// Get filtered shop transactions
List<ShopTransaction> getFilteredShopTransactions(
  List<ShopTransaction> transactions,
  ShopTransactionFilter filter,
) {
  return transactions.where((transaction) {
    // Filter by status
    if (filter.status != null && transaction.status != filter.status) {
      return false;
    }

    // Filter by search query
    if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
      final query = filter.searchQuery!.toLowerCase();
      final productName = transaction.productName.toLowerCase();
      final id = transaction.id.toLowerCase();
      final customerName = transaction.customerName?.toLowerCase() ?? '';
      final customerPhone = transaction.customerPhone?.toLowerCase() ?? '';

      if (!productName.contains(query) &&
          !id.contains(query) &&
          !customerName.contains(query) &&
          !customerPhone.contains(query)) {
        return false;
      }
    }

    // Filter by date range
    if (filter.startDate != null) {
      final startDate = DateTime(
        filter.startDate!.year,
        filter.startDate!.month,
        filter.startDate!.day,
      );
      final transactionDate = DateTime(
        transaction.timestamp.year,
        transaction.timestamp.month,
        transaction.timestamp.day,
      );
      if (transactionDate.isBefore(startDate)) {
        return false;
      }
    }

    if (filter.endDate != null) {
      final endDate = DateTime(
        filter.endDate!.year,
        filter.endDate!.month,
        filter.endDate!.day,
        23, 59, 59,
      );
      if (transaction.timestamp.isAfter(endDate)) {
        return false;
      }
    }

    return true;
  }).toList();
}

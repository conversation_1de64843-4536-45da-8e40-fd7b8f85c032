import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/mobile_banking_transaction_model.dart';

/// Provider for mobile banking transactions
final mobileBankingTransactionsProvider = Provider<List<MobileBankingTransaction>>((ref) {
  return SampleMobileBankingTransactions.getAll();
});

/// Provider for filtered mobile banking transactions
final filteredMobileBankingTransactionsProvider = Provider.family<List<MobileBankingTransaction>, MobileBankingFilter>((ref, filter) {
  final transactions = ref.watch(mobileBankingTransactionsProvider);
  
  return transactions.where((transaction) {
    // Filter by status
    if (filter.status != null && transaction.status != filter.status) {
      return false;
    }
    
    // Filter by type
    if (filter.type != null && transaction.type != filter.type) {
      return false;
    }
    
    // Filter by operator
    if (filter.operatorId != null && transaction.operatorId != filter.operatorId) {
      return false;
    }
    
    // Filter by search query
    if (filter.searchQuery.isNotEmpty) {
      final query = filter.searchQuery.toLowerCase();
      return transaction.phoneNumber.toLowerCase().contains(query) ||
             transaction.id.toLowerCase().contains(query) ||
             (transaction.recipientName?.toLowerCase().contains(query) ?? false) ||
             (transaction.transactionReference?.toLowerCase().contains(query) ?? false);
    }
    
    return true;
  }).toList();
});

/// Filter for mobile banking transactions
class MobileBankingFilter {
  final MobileBankingStatus? status;
  final MobileBankingType? type;
  final String? operatorId;
  final String searchQuery;
  
  const MobileBankingFilter({
    this.status,
    this.type,
    this.operatorId,
    this.searchQuery = '',
  });
  
  /// Create a copy with updated values
  MobileBankingFilter copyWith({
    MobileBankingStatus? status,
    MobileBankingType? type,
    String? operatorId,
    String? searchQuery,
  }) {
    return MobileBankingFilter(
      status: status ?? this.status,
      type: type ?? this.type,
      operatorId: operatorId ?? this.operatorId,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Provider for mobile banking filter
final mobileBankingFilterProvider = StateProvider<MobileBankingFilter>((ref) {
  return const MobileBankingFilter();
});

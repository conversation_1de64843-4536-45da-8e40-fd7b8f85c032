import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/product_model.dart';
import 'package:irecharge_pro/data/repositories/shop_repository.dart';

/// Provider for the shop repository
final shopRepositoryProvider = Provider<ShopRepository>((ref) {
  return ShopRepository();
});

/// Provider for all product categories
final productCategoriesProvider = FutureProvider<List<ProductCategory>>((ref) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.getProductCategories();
});

/// Provider for a specific product category by ID
final productCategoryByIdProvider = FutureProvider.family<ProductCategory?, String>((ref, categoryId) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.getProductCategoryById(categoryId);
});

/// Provider for all products
final productsProvider = FutureProvider<List<Product>>((ref) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.getProducts();
});

/// Provider for featured products
final featuredProductsProvider = FutureProvider<List<Product>>((ref) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.getFeaturedProducts();
});

/// Provider for products by category
final productsByCategoryProvider = FutureProvider.family<List<Product>, String>((ref, categoryId) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.getProductsByCategory(categoryId);
});

/// Provider for a specific product by ID
final productByIdProvider = FutureProvider.family<Product?, String>((ref, productId) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.getProductById(productId);
});

/// Provider for product search
final productSearchProvider = FutureProvider.family<List<Product>, String>((ref, query) async {
  final repository = ref.watch(shopRepositoryProvider);
  return repository.searchProducts(query);
});

/// Provider for the selected product category (state)
final selectedProductCategoryProvider = StateProvider<ProductCategory?>((ref) => null);

/// Provider for the search query (state)
final searchQueryProvider = StateProvider<String>((ref) => '');

/// Provider for the cart (state)
final cartProvider = StateNotifierProvider<CartNotifier, List<CartItem>>((ref) {
  return CartNotifier();
});

/// Cart item model
class CartItem {
  final Product product;
  final int quantity;

  CartItem({
    required this.product,
    this.quantity = 1,
  });

  /// Calculate total price
  double get totalPrice => product.actualPrice * quantity;

  /// Create a copy with updated fields
  CartItem copyWith({
    Product? product,
    int? quantity,
  }) {
    return CartItem(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
    );
  }
}

/// Cart notifier
class CartNotifier extends StateNotifier<List<CartItem>> {
  CartNotifier() : super([]);

  /// Add a product to the cart
  void addToCart(Product product, {int quantity = 1}) {
    final existingIndex = state.indexWhere((item) => item.product.id == product.id);
    
    if (existingIndex >= 0) {
      // Product already in cart, update quantity
      final existingItem = state[existingIndex];
      final updatedItem = existingItem.copyWith(
        quantity: existingItem.quantity + quantity,
      );
      
      state = [
        ...state.sublist(0, existingIndex),
        updatedItem,
        ...state.sublist(existingIndex + 1),
      ];
    } else {
      // Add new product to cart
      state = [
        ...state,
        CartItem(product: product, quantity: quantity),
      ];
    }
  }

  /// Update the quantity of a product in the cart
  void updateQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }
    
    final existingIndex = state.indexWhere((item) => item.product.id == productId);
    
    if (existingIndex >= 0) {
      final existingItem = state[existingIndex];
      final updatedItem = existingItem.copyWith(quantity: quantity);
      
      state = [
        ...state.sublist(0, existingIndex),
        updatedItem,
        ...state.sublist(existingIndex + 1),
      ];
    }
  }

  /// Remove a product from the cart
  void removeFromCart(String productId) {
    state = state.where((item) => item.product.id != productId).toList();
  }

  /// Clear the cart
  void clearCart() {
    state = [];
  }

  /// Get the total number of items in the cart
  int get totalItems {
    return state.fold(0, (total, item) => total + item.quantity);
  }

  /// Get the total price of all items in the cart
  double get totalPrice {
    return state.fold(0, (total, item) => total + item.totalPrice);
  }
}

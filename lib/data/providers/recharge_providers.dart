import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/offer_package_model.dart';
import 'package:irecharge_pro/data/models/operator_model.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';
import 'package:irecharge_pro/data/repositories/recharge_repository.dart';

/// Provider for the recharge repository
final rechargeRepositoryProvider = Provider<RechargeRepository>((ref) {
  return RechargeRepository();
});

/// Provider for all operators
final operatorsProvider = FutureProvider<List<Operator>>((ref) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getOperators();
});

/// Provider for a specific operator by ID
final operatorByIdProvider = FutureProvider.family<Operator?, String>((ref, operatorId) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getOperatorById(operatorId);
});

/// Provider for detecting operator from phone number
final detectOperatorProvider = FutureProvider.family<Operator?, String>((ref, phoneNumber) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.detectOperator(phoneNumber);
});

/// Provider for all offer packages
final offerPackagesProvider = FutureProvider<List<OfferPackage>>((ref) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getOfferPackages();
});

/// Provider for offer packages by operator
final offerPackagesByOperatorProvider = FutureProvider.family<List<OfferPackage>, String>((ref, operatorId) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getOfferPackagesByOperator(operatorId);
});

/// Provider for featured offer packages
final featuredOfferPackagesProvider = FutureProvider<List<OfferPackage>>((ref) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getFeaturedOfferPackages();
});

/// Provider for a specific offer package by ID
final offerPackageByIdProvider = FutureProvider.family<OfferPackage?, String>((ref, packageId) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getOfferPackageById(packageId);
});

/// Provider for all recharge transactions
final rechargeTransactionsProvider = FutureProvider<List<RechargeTransaction>>((ref) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getRechargeTransactions();
});

/// Provider for recent recharge transactions
final recentRechargeTransactionsProvider = FutureProvider.family<List<RechargeTransaction>, int>((ref, count) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getRecentRechargeTransactions(count);
});

/// Provider for a specific recharge transaction by ID
final rechargeTransactionByIdProvider = FutureProvider.family<RechargeTransaction?, String>((ref, transactionId) async {
  final repository = ref.watch(rechargeRepositoryProvider);
  return repository.getRechargeTransactionById(transactionId);
});

/// Provider for the selected operator (state)
final selectedOperatorProvider = StateProvider<Operator?>((ref) => null);

/// Provider for the phone number (state)
final phoneNumberProvider = StateProvider<String>((ref) => '');

/// Provider for the recharge amount (state)
final rechargeAmountProvider = StateProvider<double>((ref) => 0);

/// Provider for the selected payment method (state)
final selectedPaymentMethodProvider = StateProvider<PaymentMethod>((ref) => PaymentMethod.wallet);

/// Provider for the selected offer package (state)
final selectedOfferPackageProvider = StateProvider<OfferPackage?>((ref) => null);

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/commission_model.dart';

/// Commission list provider
final commissionsProvider = StateNotifierProvider<CommissionNotifier, List<Commission>>((ref) {
  return CommissionNotifier();
});

/// Selected commission provider
final selectedCommissionProvider = StateProvider<Commission?>((ref) => null);

/// Commission filter provider
final commissionFilterProvider = StateProvider<CommissionFilter>((ref) {
  return const CommissionFilter();
});

/// Commission filter model
class CommissionFilter {
  final CommissionStatus? status;
  final CommissionType? type;
  final String? agentId;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? searchQuery;

  const CommissionFilter({
    this.status,
    this.type,
    this.agentId,
    this.startDate,
    this.endDate,
    this.searchQuery,
  });

  /// Create a copy of this filter with given fields replaced with new values
  CommissionFilter copyWith({
    CommissionStatus? status,
    CommissionType? type,
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
    bool clearStatus = false,
    bool clearType = false,
    bool clearAgentId = false,
    bool clearStartDate = false,
    bool clearEndDate = false,
    bool clearSearchQuery = false,
  }) {
    return CommissionFilter(
      status: clearStatus ? null : status ?? this.status,
      type: clearType ? null : type ?? this.type,
      agentId: clearAgentId ? null : agentId ?? this.agentId,
      startDate: clearStartDate ? null : startDate ?? this.startDate,
      endDate: clearEndDate ? null : endDate ?? this.endDate,
      searchQuery: clearSearchQuery ? null : searchQuery ?? this.searchQuery,
    );
  }
}

/// Commission notifier
class CommissionNotifier extends StateNotifier<List<Commission>> {
  CommissionNotifier() : super(DemoCommissions.commissions);
  
  /// Add a new commission
  void addCommission(Commission commission) {
    state = [...state, commission];
  }
  
  /// Update an existing commission
  void updateCommission(Commission commission) {
    state = state.map((c) => c.id == commission.id ? commission : c).toList();
  }
  
  /// Delete a commission
  void deleteCommission(String id) {
    state = state.where((c) => c.id != id).toList();
  }
  
  /// Update commission status
  void updateCommissionStatus(String id, CommissionStatus status) {
    state = state.map((c) => c.id == id ? c.copyWith(status: status) : c).toList();
  }
  
  /// Get filtered commissions
  List<Commission> getFilteredCommissions(CommissionFilter filter) {
    return state.where((commission) {
      // Apply status filter
      if (filter.status != null && commission.status != filter.status) {
        return false;
      }
      
      // Apply type filter
      if (filter.type != null && commission.type != filter.type) {
        return false;
      }
      
      // Apply agent filter
      if (filter.agentId != null && commission.agentId != filter.agentId) {
        return false;
      }
      
      // Apply date range filter
      if (filter.startDate != null && commission.date.isBefore(filter.startDate!)) {
        return false;
      }
      if (filter.endDate != null && commission.date.isAfter(filter.endDate!.add(const Duration(days: 1)))) {
        return false;
      }
      
      // Apply search query filter
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        return commission.description.toLowerCase().contains(query) ||
               commission.id.toLowerCase().contains(query) ||
               commission.transactionId?.toLowerCase().contains(query) == true;
      }
      
      return true;
    }).toList();
  }
  
  /// Get commissions by agent ID
  List<Commission> getCommissionsByAgentId(String agentId) {
    return state.where((commission) => commission.agentId == agentId).toList();
  }
  
  /// Get total commission amount for an agent
  double getTotalCommissionForAgent(String agentId) {
    final agentCommissions = getCommissionsByAgentId(agentId);
    return agentCommissions.fold(0, (sum, commission) => sum + commission.amount);
  }
  
  /// Get pending commission amount for an agent
  double getPendingCommissionForAgent(String agentId) {
    final agentCommissions = getCommissionsByAgentId(agentId).where(
      (commission) => commission.status == CommissionStatus.pending || 
                      commission.status == CommissionStatus.approved
    ).toList();
    return agentCommissions.fold(0, (sum, commission) => sum + commission.amount);
  }
  
  /// Get paid commission amount for an agent
  double getPaidCommissionForAgent(String agentId) {
    final agentCommissions = getCommissionsByAgentId(agentId).where(
      (commission) => commission.status == CommissionStatus.paid
    ).toList();
    return agentCommissions.fold(0, (sum, commission) => sum + commission.amount);
  }
  
  /// Generate a new commission ID
  String generateCommissionId() {
    final lastId = state.isNotEmpty 
        ? int.parse(state.last.id.replaceAll('COM', '')) 
        : 0;
    return 'COM${(lastId + 1).toString().padLeft(3, '0')}';
  }
}

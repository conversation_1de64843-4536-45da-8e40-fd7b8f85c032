import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/bill_payment_transaction_model.dart';

/// Provider for bill payment transactions
final billPaymentTransactionsProvider = Provider<List<BillPaymentTransaction>>((ref) {
  return SampleBillPaymentTransactions.getAll();
});

/// Provider for filtered bill payment transactions
final filteredBillPaymentTransactionsProvider = Provider.family<List<BillPaymentTransaction>, BillPaymentFilter>((ref, filter) {
  final transactions = ref.watch(billPaymentTransactionsProvider);
  
  return transactions.where((transaction) {
    // Filter by status
    if (filter.status != null && transaction.status != filter.status) {
      return false;
    }
    
    // Filter by type
    if (filter.type != null && transaction.type != filter.type) {
      return false;
    }
    
    // Filter by provider
    if (filter.provider != null && transaction.provider != filter.provider) {
      return false;
    }
    
    // Filter by search query
    if (filter.searchQuery.isNotEmpty) {
      final query = filter.searchQuery.toLowerCase();
      return transaction.accountNumber.toLowerCase().contains(query) ||
             transaction.id.toLowerCase().contains(query) ||
             transaction.provider.toLowerCase().contains(query) ||
             (transaction.customerName?.toLowerCase().contains(query) ?? false) ||
             (transaction.billNumber?.toLowerCase().contains(query) ?? false) ||
             (transaction.transactionReference?.toLowerCase().contains(query) ?? false);
    }
    
    return true;
  }).toList();
});

/// Filter for bill payment transactions
class BillPaymentFilter {
  final BillPaymentStatus? status;
  final BillType? type;
  final String? provider;
  final String searchQuery;
  
  const BillPaymentFilter({
    this.status,
    this.type,
    this.provider,
    this.searchQuery = '',
  });
  
  /// Create a copy with updated values
  BillPaymentFilter copyWith({
    BillPaymentStatus? status,
    BillType? type,
    String? provider,
    String? searchQuery,
  }) {
    return BillPaymentFilter(
      status: status ?? this.status,
      type: type ?? this.type,
      provider: provider ?? this.provider,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Provider for bill payment filter
final billPaymentFilterProvider = StateProvider<BillPaymentFilter>((ref) {
  return const BillPaymentFilter();
});

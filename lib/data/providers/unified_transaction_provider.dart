import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/unified_transaction_model.dart';
import 'package:irecharge_pro/data/providers/bank_transfer_provider.dart';
import 'package:irecharge_pro/data/providers/bill_payment_provider.dart';
import 'package:irecharge_pro/data/providers/mobile_banking_provider.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';

/// Provider for unified transactions
final unifiedTransactionsProvider = Provider<List<UnifiedTransaction>>((ref) {
  final rechargeTransactions = ref.watch(rechargeTransactionsProvider);
  final mobileBankingTransactions = ref.watch(mobileBankingTransactionsProvider);
  final billPaymentTransactions = ref.watch(billPaymentTransactionsProvider);
  final bankTransfers = ref.watch(bankTransfersProvider);

  // Convert all transactions to unified format
  final List<UnifiedTransaction> unifiedTransactions = [
    ...rechargeTransactions.when(
      data: (data) => data.map((tx) => UnifiedTransaction.fromRecharge(tx)).toList(),
      loading: () => <UnifiedTransaction>[],
      error: (_, __) => <UnifiedTransaction>[],
    ),
    ...mobileBankingTransactions.map((tx) => UnifiedTransaction.fromMobileBanking(tx)),
    ...billPaymentTransactions.map((tx) => UnifiedTransaction.fromBillPayment(tx)),
    ...bankTransfers.map((tx) => UnifiedTransaction.fromBankTransfer(tx)),
  ];

  // Sort by timestamp (newest first)
  unifiedTransactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

  return unifiedTransactions;
});

/// Provider for filtered unified transactions
final filteredUnifiedTransactionsProvider = Provider.family<List<UnifiedTransaction>, UnifiedTransactionFilter>((ref, filter) {
  final transactions = ref.watch(unifiedTransactionsProvider);

  return transactions.where((transaction) {
    // Filter by category
    if (filter.category != null && transaction.category != filter.category) {
      return false;
    }

    // Filter by status text
    if (filter.statusText != null && transaction.statusText != filter.statusText) {
      return false;
    }

    // Filter by date range
    if (filter.startDate != null && transaction.timestamp.isBefore(filter.startDate!)) {
      return false;
    }
    if (filter.endDate != null && transaction.timestamp.isAfter(filter.endDate!)) {
      return false;
    }

    // Filter by search query
    if (filter.searchQuery.isNotEmpty) {
      final query = filter.searchQuery.toLowerCase();
      return transaction.title.toLowerCase().contains(query) ||
             transaction.subtitle.toLowerCase().contains(query) ||
             transaction.id.toLowerCase().contains(query);
    }

    return true;
  }).toList();
});

/// Filter for unified transactions
class UnifiedTransactionFilter {
  final TransactionCategory? category;
  final String? statusText;
  final DateTime? startDate;
  final DateTime? endDate;
  final String searchQuery;

  const UnifiedTransactionFilter({
    this.category,
    this.statusText,
    this.startDate,
    this.endDate,
    this.searchQuery = '',
  });

  /// Create a copy with updated values
  UnifiedTransactionFilter copyWith({
    TransactionCategory? category,
    String? statusText,
    DateTime? startDate,
    DateTime? endDate,
    String? searchQuery,
  }) {
    return UnifiedTransactionFilter(
      category: category ?? this.category,
      statusText: statusText ?? this.statusText,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Provider for unified transaction filter
final unifiedTransactionFilterProvider = StateProvider<UnifiedTransactionFilter>((ref) {
  return const UnifiedTransactionFilter();
});

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';

/// Agent customer list provider
final agentCustomersProvider = StateNotifierProvider<AgentCustomerNotifier, List<AgentCustomer>>((ref) {
  return AgentCustomerNotifier();
});

/// Selected agent customer provider
final selectedAgentCustomerProvider = StateProvider<AgentCustomer?>((ref) => null);

/// Agent customer filter provider
final agentCustomerFilterProvider = StateProvider<AgentCustomerFilter>((ref) {
  return const AgentCustomerFilter();
});

/// Agent customer notifier
class AgentCustomerNotifier extends StateNotifier<List<AgentCustomer>> {
  AgentCustomerNotifier() : super(DemoAgentCustomers.customers);

  /// Add a new customer
  void addCustomer(AgentCustomer customer) {
    state = [...state, customer];
  }

  /// Update an existing customer
  void updateCustomer(AgentCustomer customer) {
    state = state.map((c) => c.id == customer.id ? customer : c).toList();
  }

  /// Delete a customer
  void deleteCustomer(String id) {
    state = state.where((c) => c.id != id).toList();
  }

  /// Update customer status
  void updateCustomerStatus(String id, AgentCustomerStatus status) {
    state = state.map((c) => c.id == id ? c.copyWith(status: status) : c).toList();
  }

  /// Get filtered customers
  List<AgentCustomer> getFilteredCustomers(AgentCustomerFilter filter) {
    List<AgentCustomer> filteredList = [...state];

    // Apply search filter
    if (filter.searchQuery.isNotEmpty) {
      filteredList = filteredList.where((customer) {
        return customer.name.toLowerCase().contains(filter.searchQuery.toLowerCase()) ||
               customer.phone.toLowerCase().contains(filter.searchQuery.toLowerCase()) ||
               (customer.email?.toLowerCase().contains(filter.searchQuery.toLowerCase()) ?? false) ||
               customer.id.toLowerCase().contains(filter.searchQuery.toLowerCase());
      }).toList();
    }

    // Apply status filter
    if (filter.status != null) {
      filteredList = filteredList.where((customer) => customer.status == filter.status).toList();
    }

    // Apply type filter
    if (filter.type != null) {
      filteredList = filteredList.where((customer) => customer.type == filter.type).toList();
    }

    // Apply sorting
    switch (filter.sortBy) {
      case AgentCustomerSortBy.nameAsc:
        filteredList.sort((a, b) => a.name.compareTo(b.name));
        break;
      case AgentCustomerSortBy.nameDesc:
        filteredList.sort((a, b) => b.name.compareTo(a.name));
        break;
      case AgentCustomerSortBy.balanceAsc:
        filteredList.sort((a, b) => a.balance.compareTo(b.balance));
        break;
      case AgentCustomerSortBy.balanceDesc:
        filteredList.sort((a, b) => b.balance.compareTo(a.balance));
        break;
      case AgentCustomerSortBy.transactionsAsc:
        filteredList.sort((a, b) => a.totalTransactions.compareTo(b.totalTransactions));
        break;
      case AgentCustomerSortBy.transactionsDesc:
        filteredList.sort((a, b) => b.totalTransactions.compareTo(a.totalTransactions));
        break;
      case AgentCustomerSortBy.dateCreatedAsc:
        filteredList.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case AgentCustomerSortBy.dateCreatedDesc:
        filteredList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case AgentCustomerSortBy.lastTransactionAsc:
        filteredList.sort((a, b) {
          if (a.lastTransaction == null) return 1;
          if (b.lastTransaction == null) return -1;
          return a.lastTransaction!.compareTo(b.lastTransaction!);
        });
        break;
      case AgentCustomerSortBy.lastTransactionDesc:
        filteredList.sort((a, b) {
          if (a.lastTransaction == null) return 1;
          if (b.lastTransaction == null) return -1;
          return b.lastTransaction!.compareTo(a.lastTransaction!);
        });
        break;
    }

    return filteredList;
  }

  /// Generate a new customer ID
  String generateCustomerId() {
    // Find the highest existing ID number
    int highestId = 0;
    for (final customer in state) {
      if (customer.id.startsWith('AC')) {
        try {
          final idNumber = int.parse(customer.id.substring(2));
          if (idNumber > highestId) {
            highestId = idNumber;
          }
        } catch (_) {
          // Ignore parsing errors
        }
      }
    }

    // Generate new ID with incremented number
    return 'AC${(highestId + 1).toString().padLeft(3, '0')}';
  }

  /// Get agent by ID
  AgentCustomer? getAgentById(String id) {
    try {
      return state.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }
}

/// Agent customer sort options
enum AgentCustomerSortBy {
  nameAsc,
  nameDesc,
  balanceAsc,
  balanceDesc,
  transactionsAsc,
  transactionsDesc,
  dateCreatedAsc,
  dateCreatedDesc,
  lastTransactionAsc,
  lastTransactionDesc,
}

/// Agent customer filter
class AgentCustomerFilter {
  final String searchQuery;
  final AgentCustomerStatus? status;
  final AgentCustomerType? type;
  final AgentCustomerSortBy sortBy;

  const AgentCustomerFilter({
    this.searchQuery = '',
    this.status,
    this.type,
    this.sortBy = AgentCustomerSortBy.nameAsc,
  });

  /// Create a copy with new values
  AgentCustomerFilter copyWith({
    String? searchQuery,
    AgentCustomerStatus? status,
    AgentCustomerType? type,
    AgentCustomerSortBy? sortBy,
    bool clearStatus = false,
    bool clearType = false,
  }) {
    return AgentCustomerFilter(
      searchQuery: searchQuery ?? this.searchQuery,
      status: clearStatus ? null : (status ?? this.status),
      type: clearType ? null : (type ?? this.type),
      sortBy: sortBy ?? this.sortBy,
    );
  }
}

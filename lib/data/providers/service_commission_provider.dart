import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/models/service_commission_model.dart';

/// Service Commission list provider
final serviceCommissionsProvider = StateNotifierProvider<ServiceCommissionNotifier, List<ServiceCommission>>((ref) {
  return ServiceCommissionNotifier();
});

/// Selected service commission provider
final selectedServiceCommissionProvider = StateProvider<ServiceCommission?>((ref) => null);

/// Service Commission filter provider
final serviceCommissionFilterProvider = StateProvider<ServiceCommissionFilter>((ref) {
  return const ServiceCommissionFilter();
});

/// Service Commission filter
class ServiceCommissionFilter {
  final ServiceType? serviceType;
  final AgentCustomerType? userType;
  final bool? isActive;
  final String? searchQuery;
  final String? operatorId;
  final CommissionType? commissionType;
  final ChargeType? chargeType;

  const ServiceCommissionFilter({
    this.serviceType,
    this.userType,
    this.isActive,
    this.searchQuery,
    this.operatorId,
    this.commissionType,
    this.chargeType,
  });

  /// Copy with
  ServiceCommissionFilter copyWith({
    ServiceType? serviceType,
    AgentCustomerType? userType,
    bool? isActive,
    String? searchQuery,
    String? operatorId,
    CommissionType? commissionType,
    ChargeType? chargeType,
  }) {
    return ServiceCommissionFilter(
      serviceType: serviceType ?? this.serviceType,
      userType: userType ?? this.userType,
      isActive: isActive ?? this.isActive,
      searchQuery: searchQuery ?? this.searchQuery,
      operatorId: operatorId ?? this.operatorId,
      commissionType: commissionType ?? this.commissionType,
      chargeType: chargeType ?? this.chargeType,
    );
  }

  /// Clear filter
  ServiceCommissionFilter clear() {
    return const ServiceCommissionFilter();
  }
}

/// Service Commission notifier
class ServiceCommissionNotifier extends StateNotifier<List<ServiceCommission>> {
  ServiceCommissionNotifier() : super(DemoServiceCommissions.serviceCommissions);

  /// Add a new service commission
  void addServiceCommission(ServiceCommission commission) {
    state = [...state, commission];
  }

  /// Update an existing service commission
  void updateServiceCommission(ServiceCommission commission) {
    state = state.map((c) => c.id == commission.id ? commission : c).toList();
  }

  /// Delete a service commission
  void deleteServiceCommission(String id) {
    state = state.where((c) => c.id != id).toList();
  }

  /// Toggle service commission active status
  void toggleServiceCommissionStatus(String id) {
    state = state.map((c) => c.id == id ? c.copyWith(isActive: !c.isActive, updatedAt: DateTime.now()) : c).toList();
  }

  /// Get filtered service commissions
  List<ServiceCommission> getFilteredServiceCommissions(ServiceCommissionFilter filter) {
    return state.where((commission) {
      // Apply service type filter
      if (filter.serviceType != null && commission.serviceType != filter.serviceType) {
        return false;
      }

      // Apply user type filter
      if (filter.userType != null && commission.userType != filter.userType) {
        return false;
      }

      // Apply active status filter
      if (filter.isActive != null && commission.isActive != filter.isActive) {
        return false;
      }

      // Apply operator filter
      if (filter.operatorId != null && commission.operatorId != filter.operatorId) {
        return false;
      }

      // Apply commission type filter
      if (filter.commissionType != null && commission.commissionType != filter.commissionType) {
        return false;
      }

      // Apply charge type filter
      if (filter.chargeType != null && commission.chargeType != filter.chargeType) {
        return false;
      }

      // Apply search query filter
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        return commission.serviceName.toLowerCase().contains(query) ||
               commission.id.toLowerCase().contains(query) ||
               (commission.operatorName?.toLowerCase().contains(query) ?? false);
      }

      return true;
    }).toList();
  }

  /// Get service commissions by service type
  List<ServiceCommission> getServiceCommissionsByType(ServiceType type) {
    return state.where((commission) => commission.serviceType == type).toList();
  }

  /// Get service commissions by user type
  List<ServiceCommission> getServiceCommissionsByUserType(AgentCustomerType type) {
    return state.where((commission) => commission.userType == type).toList();
  }

  /// Get service commissions by operator
  List<ServiceCommission> getServiceCommissionsByOperator(String operatorId) {
    return state.where((commission) => commission.operatorId == operatorId).toList();
  }

  /// Get active service commissions
  List<ServiceCommission> getActiveServiceCommissions() {
    return state.where((commission) => commission.isActive).toList();
  }

  /// Get service commissions for a specific service and user type
  List<ServiceCommission> getServiceCommissionsForServiceAndUser(
    ServiceType serviceType,
    AgentCustomerType userType,
    {String? operatorId}
  ) {
    return state.where((commission) =>
      commission.serviceType == serviceType &&
      commission.userType == userType &&
      commission.isActive &&
      (operatorId == null || commission.operatorId == operatorId)
    ).toList();
  }

  /// Calculate commission for a transaction
  double calculateCommissionForTransaction(
    ServiceType serviceType,
    AgentCustomerType userType,
    double amount,
    {String? operatorId}
  ) {
    final commissions = getServiceCommissionsForServiceAndUser(
      serviceType,
      userType,
      operatorId: operatorId
    );

    if (commissions.isEmpty) {
      return 0;
    }

    // Use the first matching commission rule
    // In a real app, you might want to implement more complex logic
    return commissions.first.calculateCommission(amount);
  }

  /// Calculate service charge for a transaction
  double calculateServiceChargeForTransaction(
    ServiceType serviceType,
    AgentCustomerType userType,
    double amount,
    {String? operatorId}
  ) {
    final commissions = getServiceCommissionsForServiceAndUser(
      serviceType,
      userType,
      operatorId: operatorId
    );

    if (commissions.isEmpty) {
      return 0;
    }

    // Use the first matching commission rule
    return commissions.first.calculateServiceCharge(amount);
  }

  /// Generate a new service commission ID
  String generateServiceCommissionId() {
    return DemoServiceCommissions.generateId();
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/services/api_service.dart';
import 'package:irecharge_pro/core/services/auth_service.dart';
import 'package:irecharge_pro/core/services/storage_service.dart';
import 'package:irecharge_pro/data/repositories/recharge_repository.dart';

/// Provider for the API service
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

/// Provider for the storage service
final storageServiceProvider = Provider<StorageService>((ref) {
  throw UnimplementedError('Storage service should be provided externally');
});

/// Provider for the auth service
final authServiceProvider = Provider<AuthService>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  final storageService = ref.watch(storageServiceProvider);

  return AuthService(
    apiService: apiService,
    storageService: storageService,
  );
});

/// Provider for the recharge repository
final rechargeRepositoryProvider = Provider<RechargeRepository>((ref) {
  return RechargeRepository();
});

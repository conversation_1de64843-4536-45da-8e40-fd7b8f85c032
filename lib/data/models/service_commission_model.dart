import 'package:flutter/material.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';

/// Service Type enum
enum ServiceType {
  recharge,
  mobileBanking,
  billPayment,
  ticketSale,
  productSale,
  other
}

/// Commission Type enum
enum CommissionType {
  percentage,
  fixedAmount
}

/// Charge Type enum
enum ChargeType {
  percentage,
  fixedAmount,
  none
}

/// Service Commission model
class ServiceCommission {
  final String id;
  final ServiceType serviceType;
  final String serviceName;
  final String? operatorId;
  final String? operatorName;
  final double commissionRate;
  final CommissionType commissionType;
  final double? serviceCharge;
  final ChargeType chargeType;
  final AgentCustomerType userType;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final double? minTransactionAmount;
  final double? maxTransactionAmount;

  const ServiceCommission({
    required this.id,
    required this.serviceType,
    required this.serviceName,
    this.operatorId,
    this.operatorName,
    required this.commissionRate,
    required this.commissionType,
    this.serviceCharge,
    required this.chargeType,
    required this.userType,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.minTransactionAmount,
    this.maxTransactionAmount,
  });

  /// Copy with
  ServiceCommission copyWith({
    String? id,
    ServiceType? serviceType,
    String? serviceName,
    String? operatorId,
    String? operatorName,
    double? commissionRate,
    CommissionType? commissionType,
    double? serviceCharge,
    ChargeType? chargeType,
    AgentCustomerType? userType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? minTransactionAmount,
    double? maxTransactionAmount,
  }) {
    return ServiceCommission(
      id: id ?? this.id,
      serviceType: serviceType ?? this.serviceType,
      serviceName: serviceName ?? this.serviceName,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      commissionRate: commissionRate ?? this.commissionRate,
      commissionType: commissionType ?? this.commissionType,
      serviceCharge: serviceCharge ?? this.serviceCharge,
      chargeType: chargeType ?? this.chargeType,
      userType: userType ?? this.userType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      minTransactionAmount: minTransactionAmount ?? this.minTransactionAmount,
      maxTransactionAmount: maxTransactionAmount ?? this.maxTransactionAmount,
    );
  }

  /// Get formatted commission rate
  String get formattedCommissionRate {
    if (commissionType == CommissionType.percentage) {
      return '$commissionRate%';
    } else {
      return '৳$commissionRate';
    }
  }

  /// Get formatted service charge
  String get formattedServiceCharge {
    if (serviceCharge == null || chargeType == ChargeType.none) {
      return 'No Charge';
    } else if (chargeType == ChargeType.percentage) {
      return '$serviceCharge%';
    } else {
      return '৳$serviceCharge';
    }
  }

  /// Calculate commission amount for a transaction
  double calculateCommission(double transactionAmount) {
    if (commissionType == CommissionType.percentage) {
      return transactionAmount * (commissionRate / 100);
    } else {
      return commissionRate;
    }
  }

  /// Calculate service charge amount for a transaction
  double calculateServiceCharge(double transactionAmount) {
    if (serviceCharge == null || chargeType == ChargeType.none) {
      return 0;
    } else if (chargeType == ChargeType.percentage) {
      return transactionAmount * (serviceCharge! / 100);
    } else {
      return serviceCharge!;
    }
  }

  /// Get service type text
  static String getServiceTypeText(ServiceType type) {
    switch (type) {
      case ServiceType.recharge:
        return 'Mobile Recharge';
      case ServiceType.mobileBanking:
        return 'Mobile Banking';
      case ServiceType.billPayment:
        return 'Bill Payment';
      case ServiceType.ticketSale:
        return 'Ticket Sale';
      case ServiceType.productSale:
        return 'Product Sale';
      case ServiceType.other:
        return 'Other';
    }
  }

  /// Get commission type text
  static String getCommissionTypeText(CommissionType type) {
    switch (type) {
      case CommissionType.percentage:
        return 'Percentage';
      case CommissionType.fixedAmount:
        return 'Fixed Amount';
    }
  }

  /// Get charge type text
  static String getChargeTypeText(ChargeType type) {
    switch (type) {
      case ChargeType.percentage:
        return 'Percentage';
      case ChargeType.fixedAmount:
        return 'Fixed Amount';
      case ChargeType.none:
        return 'No Charge';
    }
  }

  /// Get service type color
  static Color getServiceTypeColor(ServiceType type) {
    switch (type) {
      case ServiceType.recharge:
        return Colors.blue;
      case ServiceType.mobileBanking:
        return Colors.purple;
      case ServiceType.billPayment:
        return Colors.green;
      case ServiceType.ticketSale:
        return Colors.orange;
      case ServiceType.productSale:
        return Colors.teal;
      case ServiceType.other:
        return Colors.grey;
    }
  }

  /// Get service type icon
  static IconData getServiceTypeIcon(ServiceType type) {
    switch (type) {
      case ServiceType.recharge:
        return Icons.phone_android;
      case ServiceType.mobileBanking:
        return Icons.account_balance_wallet;
      case ServiceType.billPayment:
        return Icons.receipt;
      case ServiceType.ticketSale:
        return Icons.confirmation_number;
      case ServiceType.productSale:
        return Icons.shopping_cart;
      case ServiceType.other:
        return Icons.more_horiz;
    }
  }
}

/// Demo service commissions data
class DemoServiceCommissions {
  static List<ServiceCommission> get serviceCommissions => [
    // Mobile Recharge - Sub Agents
    ServiceCommission(
      id: 'SC001',
      serviceType: ServiceType.recharge,
      serviceName: 'Airtel Recharge',
      operatorId: 'OP001',
      operatorName: 'Airtel',
      commissionRate: 2.5,
      commissionType: CommissionType.percentage,
      serviceCharge: 0.5,
      chargeType: ChargeType.percentage,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ServiceCommission(
      id: 'SC002',
      serviceType: ServiceType.recharge,
      serviceName: 'Grameenphone Recharge',
      operatorId: 'OP002',
      operatorName: 'Grameenphone',
      commissionRate: 2.0,
      commissionType: CommissionType.percentage,
      serviceCharge: 0.5,
      chargeType: ChargeType.percentage,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ServiceCommission(
      id: 'SC003',
      serviceType: ServiceType.recharge,
      serviceName: 'Banglalink Recharge',
      operatorId: 'OP003',
      operatorName: 'Banglalink',
      commissionRate: 2.2,
      commissionType: CommissionType.percentage,
      serviceCharge: 0.5,
      chargeType: ChargeType.percentage,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ServiceCommission(
      id: 'SC004',
      serviceType: ServiceType.recharge,
      serviceName: 'Robi Recharge',
      operatorId: 'OP004',
      operatorName: 'Robi',
      commissionRate: 2.3,
      commissionType: CommissionType.percentage,
      serviceCharge: 0.5,
      chargeType: ChargeType.percentage,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),

    // Mobile Banking - Sub Agents
    ServiceCommission(
      id: 'SC005',
      serviceType: ServiceType.mobileBanking,
      serviceName: 'bKash Cash In',
      operatorId: 'MB001',
      operatorName: 'bKash',
      commissionRate: 1.0,
      commissionType: CommissionType.percentage,
      serviceCharge: 5,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
    ServiceCommission(
      id: 'SC006',
      serviceType: ServiceType.mobileBanking,
      serviceName: 'Nagad Cash In',
      operatorId: 'MB002',
      operatorName: 'Nagad',
      commissionRate: 1.2,
      commissionType: CommissionType.percentage,
      serviceCharge: 5,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
    ServiceCommission(
      id: 'SC007',
      serviceType: ServiceType.mobileBanking,
      serviceName: 'Rocket Cash In',
      operatorId: 'MB003',
      operatorName: 'Rocket',
      commissionRate: 1.1,
      commissionType: CommissionType.percentage,
      serviceCharge: 5,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),

    // Bill Payment - Sub Agents
    ServiceCommission(
      id: 'SC008',
      serviceType: ServiceType.billPayment,
      serviceName: 'Electricity Bill',
      operatorId: 'BP001',
      operatorName: 'DESCO',
      commissionRate: 1.5,
      commissionType: CommissionType.percentage,
      serviceCharge: null,
      chargeType: ChargeType.none,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
    ServiceCommission(
      id: 'SC009',
      serviceType: ServiceType.billPayment,
      serviceName: 'Water Bill',
      operatorId: 'BP002',
      operatorName: 'WASA',
      commissionRate: 1.8,
      commissionType: CommissionType.percentage,
      serviceCharge: null,
      chargeType: ChargeType.none,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
    ServiceCommission(
      id: 'SC010',
      serviceType: ServiceType.billPayment,
      serviceName: 'Gas Bill',
      operatorId: 'BP003',
      operatorName: 'Titas Gas',
      commissionRate: 1.6,
      commissionType: CommissionType.percentage,
      serviceCharge: null,
      chargeType: ChargeType.none,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),

    // Ticket Sale - Sub Agents
    ServiceCommission(
      id: 'SC011',
      serviceType: ServiceType.ticketSale,
      serviceName: 'Bus Ticket',
      operatorId: 'TS001',
      operatorName: 'Shohagh Paribahan',
      commissionRate: 3.0,
      commissionType: CommissionType.percentage,
      serviceCharge: 10,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
    ),
    ServiceCommission(
      id: 'SC012',
      serviceType: ServiceType.ticketSale,
      serviceName: 'Train Ticket',
      operatorId: 'TS002',
      operatorName: 'Bangladesh Railway',
      commissionRate: 2.5,
      commissionType: CommissionType.percentage,
      serviceCharge: 10,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
    ),

    // Product Sale - Sub Agents
    ServiceCommission(
      id: 'SC013',
      serviceType: ServiceType.productSale,
      serviceName: 'Electronics',
      operatorId: 'PS001',
      operatorName: 'Electronics Store',
      commissionRate: 5.0,
      commissionType: CommissionType.percentage,
      serviceCharge: null,
      chargeType: ChargeType.none,
      userType: AgentCustomerType.subAgent,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
    ),

    // Mobile Recharge - Customers
    ServiceCommission(
      id: 'SC014',
      serviceType: ServiceType.recharge,
      serviceName: 'Airtel Recharge',
      operatorId: 'OP001',
      operatorName: 'Airtel',
      commissionRate: 1.0,
      commissionType: CommissionType.percentage,
      serviceCharge: 1.0,
      chargeType: ChargeType.percentage,
      userType: AgentCustomerType.customer,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    ServiceCommission(
      id: 'SC015',
      serviceType: ServiceType.recharge,
      serviceName: 'Grameenphone Recharge',
      operatorId: 'OP002',
      operatorName: 'Grameenphone',
      commissionRate: 0.8,
      commissionType: CommissionType.percentage,
      serviceCharge: 1.0,
      chargeType: ChargeType.percentage,
      userType: AgentCustomerType.customer,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),

    // Mobile Banking - Customers
    ServiceCommission(
      id: 'SC016',
      serviceType: ServiceType.mobileBanking,
      serviceName: 'bKash Cash In',
      operatorId: 'MB001',
      operatorName: 'bKash',
      commissionRate: 0.5,
      commissionType: CommissionType.percentage,
      serviceCharge: 10,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.customer,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
    ServiceCommission(
      id: 'SC017',
      serviceType: ServiceType.mobileBanking,
      serviceName: 'Nagad Cash In',
      operatorId: 'MB002',
      operatorName: 'Nagad',
      commissionRate: 0.6,
      commissionType: CommissionType.percentage,
      serviceCharge: 10,
      chargeType: ChargeType.fixedAmount,
      userType: AgentCustomerType.customer,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
  ];

  /// Get service commission by ID
  static ServiceCommission? getById(String id) {
    try {
      return serviceCommissions.firstWhere((commission) => commission.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Generate a new service commission ID
  static String generateId() {
    final lastId = serviceCommissions.isNotEmpty
        ? int.parse(serviceCommissions.last.id.replaceAll('SC', ''))
        : 0;
    return 'SC${(lastId + 1).toString().padLeft(3, '0')}';
  }
}

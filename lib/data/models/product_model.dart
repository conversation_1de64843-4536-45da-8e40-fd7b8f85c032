import 'package:flutter/material.dart';

/// Model representing a product category
class ProductCategory {
  final String id;
  final String name;
  final String? iconPath;
  final IconData? icon;
  final Color color;

  const ProductCategory({
    required this.id,
    required this.name,
    this.iconPath,
    this.icon,
    required this.color,
  });

  /// Create ProductCategory from JSON
  factory ProductCategory.fromJson(Map<String, dynamic> json) {
    return ProductCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      iconPath: json['icon_path'] as String?,
      color: Color(int.parse(json['color'] as String, radix: 16)),
    );
  }

  /// Convert ProductCategory to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon_path': iconPath,
      'color': color.value.toRadixString(16),
    };
  }
}

/// Model representing a product
class Product {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? discountPrice;
  final String imagePath;
  final String categoryId;
  final bool isFeatured;
  final bool isAvailable;
  final double rating;
  final int reviewCount;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.discountPrice,
    required this.imagePath,
    required this.categoryId,
    this.isFeatured = false,
    this.isAvailable = true,
    this.rating = 0.0,
    this.reviewCount = 0,
  });

  /// Create Product from JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      discountPrice: json['discount_price'] != null
          ? (json['discount_price'] as num).toDouble()
          : null,
      imagePath: json['image_path'] as String,
      categoryId: json['category_id'] as String,
      isFeatured: json['is_featured'] as bool? ?? false,
      isAvailable: json['is_available'] as bool? ?? true,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['review_count'] as int? ?? 0,
    );
  }

  /// Convert Product to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'discount_price': discountPrice,
      'image_path': imagePath,
      'category_id': categoryId,
      'is_featured': isFeatured,
      'is_available': isAvailable,
      'rating': rating,
      'review_count': reviewCount,
    };
  }

  /// Calculate discount percentage
  double? get discountPercentage {
    if (discountPrice == null || discountPrice! >= price) {
      return null;
    }
    return ((price - discountPrice!) / price) * 100;
  }

  /// Get the actual price (discount price if available, otherwise regular price)
  double get actualPrice => discountPrice ?? price;
}

/// List of predefined product categories
class DemoProductCategories {
  // Electronics
  static const ProductCategory electronics = ProductCategory(
    id: 'electronics',
    name: 'Electronics',
    icon: Icons.devices,
    color: Color(0xFF1976D2), // Blue
  );

  // Clothing
  static const ProductCategory clothing = ProductCategory(
    id: 'clothing',
    name: 'Clothing',
    icon: Icons.checkroom,
    color: Color(0xFFE53935), // Red
  );

  // Home & Kitchen
  static const ProductCategory homeKitchen = ProductCategory(
    id: 'home_kitchen',
    name: 'Home & Kitchen',
    icon: Icons.home,
    color: Color(0xFF4CAF50), // Green
  );

  // Beauty & Personal Care
  static const ProductCategory beautyPersonalCare = ProductCategory(
    id: 'beauty_personal_care',
    name: 'Beauty & Personal Care',
    icon: Icons.spa,
    color: Color(0xFFFF9800), // Orange
  );

  // Books
  static const ProductCategory books = ProductCategory(
    id: 'books',
    name: 'Books',
    icon: Icons.book,
    color: Color(0xFF9C27B0), // Purple
  );

  // Get all categories
  static List<ProductCategory> getAll() {
    return [
      electronics,
      clothing,
      homeKitchen,
      beautyPersonalCare,
      books,
    ];
  }

  // Get category by ID
  static ProductCategory? getById(String id) {
    try {
      return getAll().firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }
}

/// List of demo products
class DemoProducts {
  // Electronics
  static final List<Product> electronics = [
    const Product(
      id: 'e1',
      name: 'Wireless Earbuds',
      description: 'High-quality wireless earbuds with noise cancellation.',
      price: 99.99,
      discountPrice: 79.99,
      imagePath: 'assets/images/products/earbuds.png',
      categoryId: 'electronics',
      isFeatured: true,
      rating: 4.5,
      reviewCount: 120,
    ),
    const Product(
      id: 'e2',
      name: 'Smart Watch',
      description: 'Track your fitness and stay connected with this smart watch.',
      price: 199.99,
      discountPrice: 149.99,
      imagePath: 'assets/images/products/smartwatch.png',
      categoryId: 'electronics',
      isFeatured: true,
      rating: 4.2,
      reviewCount: 85,
    ),
    const Product(
      id: 'e3',
      name: 'Bluetooth Speaker',
      description: 'Portable Bluetooth speaker with amazing sound quality.',
      price: 79.99,
      imagePath: 'assets/images/products/speaker.png',
      categoryId: 'electronics',
      rating: 4.0,
      reviewCount: 65,
    ),
    const Product(
      id: 'e4',
      name: 'Power Bank',
      description: '20000mAh power bank for charging your devices on the go.',
      price: 49.99,
      discountPrice: 39.99,
      imagePath: 'assets/images/products/powerbank.png',
      categoryId: 'electronics',
      rating: 4.7,
      reviewCount: 200,
    ),
  ];

  // Clothing
  static final List<Product> clothing = [
    const Product(
      id: 'c1',
      name: 'Men\'s T-Shirt',
      description: 'Comfortable cotton t-shirt for everyday wear.',
      price: 24.99,
      discountPrice: 19.99,
      imagePath: 'assets/images/products/tshirt.png',
      categoryId: 'clothing',
      rating: 4.3,
      reviewCount: 110,
    ),
    const Product(
      id: 'c2',
      name: 'Women\'s Jeans',
      description: 'Stylish and comfortable jeans for women.',
      price: 59.99,
      imagePath: 'assets/images/products/jeans.png',
      categoryId: 'clothing',
      isFeatured: true,
      rating: 4.1,
      reviewCount: 75,
    ),
  ];

  // Home & Kitchen
  static final List<Product> homeKitchen = [
    const Product(
      id: 'h1',
      name: 'Coffee Maker',
      description: 'Automatic coffee maker for brewing delicious coffee.',
      price: 129.99,
      discountPrice: 99.99,
      imagePath: 'assets/images/products/coffeemaker.png',
      categoryId: 'home_kitchen',
      isFeatured: true,
      rating: 4.6,
      reviewCount: 150,
    ),
    const Product(
      id: 'h2',
      name: 'Non-Stick Pan Set',
      description: 'Set of 3 non-stick pans for cooking.',
      price: 89.99,
      imagePath: 'assets/images/products/panset.png',
      categoryId: 'home_kitchen',
      rating: 4.4,
      reviewCount: 95,
    ),
  ];

  // Beauty & Personal Care
  static final List<Product> beautyPersonalCare = [
    const Product(
      id: 'b1',
      name: 'Face Cream',
      description: 'Moisturizing face cream for all skin types.',
      price: 34.99,
      discountPrice: 29.99,
      imagePath: 'assets/images/products/facecream.png',
      categoryId: 'beauty_personal_care',
      rating: 4.2,
      reviewCount: 80,
    ),
    const Product(
      id: 'b2',
      name: 'Hair Dryer',
      description: 'Professional hair dryer with multiple settings.',
      price: 69.99,
      imagePath: 'assets/images/products/hairdryer.png',
      categoryId: 'beauty_personal_care',
      isFeatured: true,
      rating: 4.3,
      reviewCount: 110,
    ),
  ];

  // Books
  static final List<Product> books = [
    const Product(
      id: 'bk1',
      name: 'The Great Novel',
      description: 'A bestselling novel that will keep you engaged.',
      price: 19.99,
      discountPrice: 14.99,
      imagePath: 'assets/images/products/book1.png',
      categoryId: 'books',
      rating: 4.8,
      reviewCount: 220,
    ),
    const Product(
      id: 'bk2',
      name: 'Cooking Guide',
      description: 'Learn to cook delicious meals with this guide.',
      price: 29.99,
      imagePath: 'assets/images/products/book2.png',
      categoryId: 'books',
      isFeatured: true,
      rating: 4.5,
      reviewCount: 130,
    ),
  ];

  // Get all products
  static List<Product> getAll() {
    return [
      ...electronics,
      ...clothing,
      ...homeKitchen,
      ...beautyPersonalCare,
      ...books,
    ];
  }

  // Get featured products
  static List<Product> getFeatured() {
    return getAll().where((product) => product.isFeatured).toList();
  }

  // Get products by category
  static List<Product> getByCategory(String categoryId) {
    return getAll().where((product) => product.categoryId == categoryId).toList();
  }

  // Get product by ID
  static Product? getById(String id) {
    try {
      return getAll().firstWhere((product) => product.id == id);
    } catch (e) {
      return null;
    }
  }
}

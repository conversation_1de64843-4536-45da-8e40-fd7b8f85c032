import 'package:flutter/material.dart';

/// Mobile banking operator model
class MobileBankingOperator {
  final String id;
  final String name;
  final String logoPath;
  final Color color;

  const MobileBankingOperator({
    required this.id,
    required this.name,
    required this.logoPath,
    required this.color,
  });

  /// Get all operators
  static List<MobileBankingOperator> getAllOperators() {
    return [
      getOperator('bkash')!,
      getOperator('nagad')!,
      getOperator('rocket')!,
      getOperator('upay')!,
    ];
  }

  /// Get operator by ID
  static MobileBankingOperator? getOperator(String id) {
    switch (id) {
      case 'bkash':
        return const MobileBankingOperator(
          id: 'bkash',
          name: 'bKash',
          logoPath: 'assets/images/banking/bkash.png',
          color: Color(0xFFE2136E),
        );
      case 'nagad':
        return const MobileBankingOperator(
          id: 'nagad',
          name: 'Nagad',
          logoPath: 'assets/images/banking/nagad.png',
          color: Color(0xFFFF6A00),
        );
      case 'rocket':
        return const MobileBankingOperator(
          id: 'rocket',
          name: 'Rocket',
          logoPath: 'assets/images/banking/rocket.png',
          color: Color(0xFF8C3494),
        );
      case 'upay':
        return const MobileBankingOperator(
          id: 'upay',
          name: 'Upay',
          logoPath: 'assets/images/banking/upay.png',
          color: Color(0xFF0077B6),
        );
      default:
        return null;
    }
  }
}

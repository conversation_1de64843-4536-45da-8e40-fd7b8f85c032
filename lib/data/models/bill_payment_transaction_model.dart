import 'package:flutter/material.dart';

/// Status of a bill payment transaction
enum BillPaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
}

/// Type of bill
enum BillType {
  electricity,
  water,
  gas,
  internet,
  telephone,
  tv,
  education,
  tax,
  other,
}

/// Model representing a bill payment transaction
class BillPaymentTransaction {
  final String id;
  final String provider;
  final String accountNumber;
  final double amount;
  final DateTime timestamp;
  final BillPaymentStatus status;
  final BillType type;
  final String? transactionReference;
  final String? failureReason;
  final String? customerName;
  final String? billNumber;
  final String? billMonth;
  final double? fee;

  const BillPaymentTransaction({
    required this.id,
    required this.provider,
    required this.accountNumber,
    required this.amount,
    required this.timestamp,
    required this.status,
    required this.type,
    this.transactionReference,
    this.failureReason,
    this.customerName,
    this.billNumber,
    this.billMonth,
    this.fee,
  });

  /// Get formatted amount
  String get formattedAmount => '৳${amount.toStringAsFixed(2)}';

  /// Get formatted date
  String get formattedDate => '${timestamp.day}/${timestamp.month}/${timestamp.year}';

  /// Get formatted time
  String get formattedTime => '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';

  /// Get status color
  Color get statusColor {
    switch (status) {
      case BillPaymentStatus.pending:
        return Colors.amber;
      case BillPaymentStatus.processing:
        return Colors.blue;
      case BillPaymentStatus.completed:
        return Colors.green;
      case BillPaymentStatus.failed:
        return Colors.red;
      case BillPaymentStatus.cancelled:
        return Colors.grey;
    }
  }

  /// Get status text
  String get statusText {
    switch (status) {
      case BillPaymentStatus.pending:
        return 'Pending';
      case BillPaymentStatus.processing:
        return 'Processing';
      case BillPaymentStatus.completed:
        return 'Completed';
      case BillPaymentStatus.failed:
        return 'Failed';
      case BillPaymentStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get type text
  String get typeText {
    switch (type) {
      case BillType.electricity:
        return 'Electricity';
      case BillType.water:
        return 'Water';
      case BillType.gas:
        return 'Gas';
      case BillType.internet:
        return 'Internet';
      case BillType.telephone:
        return 'Telephone';
      case BillType.tv:
        return 'TV';
      case BillType.education:
        return 'Education';
      case BillType.tax:
        return 'Tax';
      case BillType.other:
        return 'Other';
    }
  }

  /// Get type icon
  IconData get typeIcon {
    switch (type) {
      case BillType.electricity:
        return Icons.electric_bolt;
      case BillType.water:
        return Icons.water_drop;
      case BillType.gas:
        return Icons.local_fire_department;
      case BillType.internet:
        return Icons.wifi;
      case BillType.telephone:
        return Icons.phone;
      case BillType.tv:
        return Icons.tv;
      case BillType.education:
        return Icons.school;
      case BillType.tax:
        return Icons.receipt_long;
      case BillType.other:
        return Icons.receipt;
    }
  }

  /// Get formatted fee
  String get formattedFee => fee != null ? '৳${fee!.toStringAsFixed(2)}' : '৳0.00';

  /// Get total amount (amount + fee)
  double get totalAmount => amount + (fee ?? 0);

  /// Get formatted total amount
  String get formattedTotalAmount => '৳${totalAmount.toStringAsFixed(2)}';
}

/// Sample bill payment transactions for testing
class SampleBillPaymentTransactions {
  static List<BillPaymentTransaction> getAll() {
    final now = DateTime.now();
    
    return [
      BillPaymentTransaction(
        id: 'bp_001',
        provider: 'DESCO',
        accountNumber: '*********',
        amount: 1250.0,
        timestamp: now.subtract(const Duration(hours: 4)),
        status: BillPaymentStatus.completed,
        type: BillType.electricity,
        transactionReference: 'REF123456',
        customerName: 'Ahmed Khan',
        billNumber: 'DESCO-123456',
        billMonth: 'May 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_002',
        provider: 'WASA',
        accountNumber: '*********',
        amount: 850.0,
        timestamp: now.subtract(const Duration(days: 1)),
        status: BillPaymentStatus.completed,
        type: BillType.water,
        transactionReference: 'REF123457',
        customerName: 'Sarah Ahmed',
        billNumber: 'WASA-987654',
        billMonth: 'May 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_003',
        provider: 'Titas Gas',
        accountNumber: '*********',
        amount: 650.0,
        timestamp: now.subtract(const Duration(days: 2)),
        status: BillPaymentStatus.completed,
        type: BillType.gas,
        transactionReference: 'REF123458',
        customerName: 'Karim Rahman',
        billNumber: 'TITAS-456789',
        billMonth: 'May 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_004',
        provider: 'Link3',
        accountNumber: '*********',
        amount: 1500.0,
        timestamp: now.subtract(const Duration(days: 3)),
        status: BillPaymentStatus.failed,
        type: BillType.internet,
        failureReason: 'Payment gateway error',
        customerName: 'Rahim Mia',
        billNumber: 'LINK3-789123',
        billMonth: 'May 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_005',
        provider: 'BTCL',
        accountNumber: '*********',
        amount: 750.0,
        timestamp: now.subtract(const Duration(days: 4)),
        status: BillPaymentStatus.completed,
        type: BillType.telephone,
        transactionReference: 'REF123459',
        customerName: 'Fatima Begum',
        billNumber: 'BTCL-321654',
        billMonth: 'May 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_006',
        provider: 'Akash DTH',
        accountNumber: '*********',
        amount: 350.0,
        timestamp: now.subtract(const Duration(days: 5)),
        status: BillPaymentStatus.completed,
        type: BillType.tv,
        transactionReference: 'REF123460',
        customerName: 'Jamal Uddin',
        billNumber: 'AKASH-654987',
        billMonth: 'May 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_007',
        provider: 'Dhaka University',
        accountNumber: '*********',
        amount: 5000.0,
        timestamp: now.subtract(const Duration(days: 6)),
        status: BillPaymentStatus.completed,
        type: BillType.education,
        transactionReference: 'REF123461',
        customerName: 'Nusrat Jahan',
        billNumber: 'DU-159753',
        billMonth: 'Spring 2023',
      ),
      BillPaymentTransaction(
        id: 'bp_008',
        provider: 'NBR',
        accountNumber: '*********',
        amount: 3500.0,
        timestamp: now.subtract(const Duration(days: 7)),
        status: BillPaymentStatus.pending,
        type: BillType.tax,
        customerName: 'Masud Rana',
        billNumber: 'NBR-753159',
        billMonth: 'FY 2022-2023',
      ),
    ];
  }

  /// Get recent transactions
  static List<BillPaymentTransaction> getRecent(int count) {
    final all = getAll();
    all.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return all.take(count).toList();
  }

  /// Get transactions by status
  static List<BillPaymentTransaction> getByStatus(BillPaymentStatus status) {
    return getAll().where((tx) => tx.status == status).toList();
  }

  /// Get transactions by type
  static List<BillPaymentTransaction> getByType(BillType type) {
    return getAll().where((tx) => tx.type == type).toList();
  }

  /// Get transactions by provider
  static List<BillPaymentTransaction> getByProvider(String provider) {
    return getAll().where((tx) => tx.provider == provider).toList();
  }
}

import 'package:irecharge_pro/data/models/operator_model.dart';
import 'package:irecharge_pro/data/models/offer_package_model.dart';

/// Status of a recharge transaction
enum RechargeStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
}

/// Payment method for recharge
enum PaymentMethod {
  wallet,
  card,
  mobileBanking,
  internetBanking,
}

/// Model representing a mobile recharge transaction
class RechargeTransaction {
  final String id;
  final String operatorId;
  final String phoneNumber;
  final double amount;
  final DateTime timestamp;
  final RechargeStatus status;
  final PaymentMethod paymentMethod;
  final String? packageId;
  final String? transactionReference;
  final String? failureReason;

  const RechargeTransaction({
    required this.id,
    required this.operatorId,
    required this.phoneNumber,
    required this.amount,
    required this.timestamp,
    required this.status,
    required this.paymentMethod,
    this.packageId,
    this.transactionReference,
    this.failureReason,
  });

  /// Create RechargeTransaction from JSON
  factory RechargeTransaction.fromJson(Map<String, dynamic> json) {
    return RechargeTransaction(
      id: json['id'] as String,
      operatorId: json['operator_id'] as String,
      phoneNumber: json['phone_number'] as String,
      amount: (json['amount'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: RechargeStatus.values.firstWhere(
        (e) => e.toString() == 'RechargeStatus.${json['status']}',
        orElse: () => RechargeStatus.pending,
      ),
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.toString() == 'PaymentMethod.${json['payment_method']}',
        orElse: () => PaymentMethod.wallet,
      ),
      packageId: json['package_id'] as String?,
      transactionReference: json['transaction_reference'] as String?,
      failureReason: json['failure_reason'] as String?,
    );
  }

  /// Convert RechargeTransaction to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operator_id': operatorId,
      'phone_number': phoneNumber,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
      'status': status.toString().split('.').last,
      'payment_method': paymentMethod.toString().split('.').last,
      'package_id': packageId,
      'transaction_reference': transactionReference,
      'failure_reason': failureReason,
    };
  }

  /// Get the operator for this transaction
  Operator? get operator {
    final operators = BangladeshOperators.getAll();
    for (final op in operators) {
      if (op.id == operatorId) {
        return op;
      }
    }
    return null;
  }

  /// Get the package for this transaction if it's a package recharge
  OfferPackage? get package {
    if (packageId == null) return null;
    
    final packages = SampleOfferPackages.getAll();
    for (final pkg in packages) {
      if (pkg.id == packageId) {
        return pkg;
      }
    }
    return null;
  }

  /// Get formatted amount
  String get formattedAmount => '৳${amount.toStringAsFixed(2)}';

  /// Get formatted date
  String get formattedDate {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
  }

  /// Get formatted time
  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Get formatted status
  String get formattedStatus {
    switch (status) {
      case RechargeStatus.pending:
        return 'Pending';
      case RechargeStatus.processing:
        return 'Processing';
      case RechargeStatus.completed:
        return 'Completed';
      case RechargeStatus.failed:
        return 'Failed';
      case RechargeStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get formatted payment method
  String get formattedPaymentMethod {
    switch (paymentMethod) {
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.mobileBanking:
        return 'Mobile Banking';
      case PaymentMethod.internetBanking:
        return 'Internet Banking';
    }
  }

  /// Get color for status
  int getStatusColor() {
    switch (status) {
      case RechargeStatus.pending:
        return 0xFFFFA000; // Amber
      case RechargeStatus.processing:
        return 0xFF2196F3; // Blue
      case RechargeStatus.completed:
        return 0xFF4CAF50; // Green
      case RechargeStatus.failed:
        return 0xFFE53935; // Red
      case RechargeStatus.cancelled:
        return 0xFF757575; // Grey
    }
  }
}

/// Sample recharge transactions for testing
class SampleRechargeTransactions {
  static List<RechargeTransaction> getAll() {
    final now = DateTime.now();
    
    return [
      RechargeTransaction(
        id: 'tx_001',
        operatorId: 'gp',
        phoneNumber: '***********',
        amount: 100,
        timestamp: now.subtract(const Duration(hours: 2)),
        status: RechargeStatus.completed,
        paymentMethod: PaymentMethod.wallet,
        transactionReference: 'REF123456',
      ),
      RechargeTransaction(
        id: 'tx_002',
        operatorId: 'robi',
        phoneNumber: '***********',
        amount: 50,
        timestamp: now.subtract(const Duration(days: 1)),
        status: RechargeStatus.completed,
        paymentMethod: PaymentMethod.mobileBanking,
        transactionReference: 'REF123457',
      ),
      RechargeTransaction(
        id: 'tx_003',
        operatorId: 'airtel',
        phoneNumber: '***********',
        amount: 249,
        timestamp: now.subtract(const Duration(days: 2)),
        status: RechargeStatus.completed,
        paymentMethod: PaymentMethod.card,
        packageId: 'airtel_combo_1',
        transactionReference: 'REF123458',
      ),
      RechargeTransaction(
        id: 'tx_004',
        operatorId: 'banglalink',
        phoneNumber: '***********',
        amount: 200,
        timestamp: now.subtract(const Duration(days: 3)),
        status: RechargeStatus.failed,
        paymentMethod: PaymentMethod.wallet,
        failureReason: 'Payment gateway error',
      ),
      RechargeTransaction(
        id: 'tx_005',
        operatorId: 'teletalk',
        phoneNumber: '***********',
        amount: 90,
        timestamp: now.subtract(const Duration(days: 4)),
        status: RechargeStatus.completed,
        paymentMethod: PaymentMethod.wallet,
        packageId: 'teletalk_data_1',
        transactionReference: 'REF123459',
      ),
    ];
  }

  /// Get recent transactions
  static List<RechargeTransaction> getRecent(int count) {
    final all = getAll();
    all.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return all.take(count).toList();
  }

  /// Get transactions by status
  static List<RechargeTransaction> getByStatus(RechargeStatus status) {
    return getAll().where((tx) => tx.status == status).toList();
  }

  /// Get transactions by operator
  static List<RechargeTransaction> getByOperator(String operatorId) {
    return getAll().where((tx) => tx.operatorId == operatorId).toList();
  }
}

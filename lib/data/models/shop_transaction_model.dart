import 'package:flutter/material.dart';

/// Shop Transaction Status enum
enum ShopTransactionStatus {
  pending,
  processing,
  completed,
  shipped,
  delivered,
  cancelled,
  refunded
}

/// Shop Transaction model
class ShopTransaction {
  final String id;
  final String productId;
  final String productName;
  final String? productImage;
  final int quantity;
  final double price;
  final double? discount;
  final double? shippingCost;
  final String? shippingAddress;
  final String? customerName;
  final String? customerPhone;
  final ShopTransactionStatus status;
  final DateTime timestamp;
  final String? transactionReference;
  final String? paymentMethod;
  final String? failureReason;

  const ShopTransaction({
    required this.id,
    required this.productId,
    required this.productName,
    this.productImage,
    required this.quantity,
    required this.price,
    this.discount,
    this.shippingCost,
    this.shippingAddress,
    this.customerName,
    this.customerPhone,
    required this.status,
    required this.timestamp,
    this.transactionReference,
    this.paymentMethod,
    this.failureReason,
  });

  /// Get formatted date
  String get formattedDate {
    return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
  }

  /// Get formatted time
  String get formattedTime {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Get formatted price
  String get formattedPrice {
    return '৳${price.toStringAsFixed(2)}';
  }

  /// Get formatted total price
  String get formattedTotalPrice {
    final total = (price * quantity);
    return '৳${total.toStringAsFixed(2)}';
  }

  /// Get formatted discount
  String get formattedDiscount {
    if (discount == null || discount == 0) return '৳0.00';
    return '৳${discount!.toStringAsFixed(2)}';
  }

  /// Get formatted shipping cost
  String get formattedShippingCost {
    if (shippingCost == null || shippingCost == 0) return '৳0.00';
    return '৳${shippingCost!.toStringAsFixed(2)}';
  }

  /// Get formatted final amount
  String get formattedFinalAmount {
    final total = (price * quantity);
    final discountAmount = discount ?? 0;
    final shipping = shippingCost ?? 0;
    final finalAmount = total - discountAmount + shipping;
    return '৳${finalAmount.toStringAsFixed(2)}';
  }

  /// Get status text
  String get statusText {
    switch (status) {
      case ShopTransactionStatus.pending:
        return 'Pending';
      case ShopTransactionStatus.processing:
        return 'Processing';
      case ShopTransactionStatus.completed:
        return 'Completed';
      case ShopTransactionStatus.shipped:
        return 'Shipped';
      case ShopTransactionStatus.delivered:
        return 'Delivered';
      case ShopTransactionStatus.cancelled:
        return 'Cancelled';
      case ShopTransactionStatus.refunded:
        return 'Refunded';
    }
  }

  /// Get status color
  Color get statusColor {
    switch (status) {
      case ShopTransactionStatus.pending:
        return Colors.orange;
      case ShopTransactionStatus.processing:
        return Colors.blue;
      case ShopTransactionStatus.completed:
        return Colors.green;
      case ShopTransactionStatus.shipped:
        return Colors.purple;
      case ShopTransactionStatus.delivered:
        return Colors.teal;
      case ShopTransactionStatus.cancelled:
        return Colors.red;
      case ShopTransactionStatus.refunded:
        return Colors.amber;
    }
  }
}

/// Demo shop transactions
class DemoShopTransactions {
  static List<ShopTransaction> get transactions => [
    ShopTransaction(
      id: 'ST001',
      productId: 'P001',
      productName: 'Smartphone X Pro',
      productImage: 'assets/images/products/smartphone.png',
      quantity: 1,
      price: 45000,
      discount: 2000,
      shippingCost: 150,
      shippingAddress: '123 Main St, Dhaka, Bangladesh',
      customerName: 'John Doe',
      customerPhone: '+8801712345678',
      status: ShopTransactionStatus.delivered,
      timestamp: DateTime.now().subtract(const Duration(days: 5)),
      transactionReference: 'REF123456',
      paymentMethod: 'Cash on Delivery',
    ),
    ShopTransaction(
      id: 'ST002',
      productId: 'P002',
      productName: 'Wireless Earbuds',
      productImage: 'assets/images/products/earbuds.png',
      quantity: 2,
      price: 2500,
      shippingCost: 80,
      shippingAddress: '456 Park Ave, Chittagong, Bangladesh',
      customerName: 'Jane Smith',
      customerPhone: '+8801812345678',
      status: ShopTransactionStatus.shipped,
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      transactionReference: 'REF789012',
      paymentMethod: 'bKash',
    ),
    ShopTransaction(
      id: 'ST003',
      productId: 'P003',
      productName: 'Smart Watch',
      productImage: 'assets/images/products/smartwatch.png',
      quantity: 1,
      price: 8500,
      discount: 500,
      shippingAddress: '789 Lake View, Sylhet, Bangladesh',
      customerName: 'Robert Johnson',
      customerPhone: '+8801912345678',
      status: ShopTransactionStatus.processing,
      timestamp: DateTime.now().subtract(const Duration(hours: 12)),
      transactionReference: 'REF345678',
      paymentMethod: 'Nagad',
    ),
    ShopTransaction(
      id: 'ST004',
      productId: 'P004',
      productName: 'Bluetooth Speaker',
      productImage: 'assets/images/products/speaker.png',
      quantity: 1,
      price: 3200,
      shippingCost: 100,
      shippingAddress: '101 River Road, Khulna, Bangladesh',
      customerName: 'Emily Wilson',
      customerPhone: '+*************',
      status: ShopTransactionStatus.pending,
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      transactionReference: 'REF901234',
      paymentMethod: 'Credit Card',
    ),
    ShopTransaction(
      id: 'ST005',
      productId: 'P005',
      productName: 'Power Bank 20000mAh',
      productImage: 'assets/images/products/powerbank.png',
      quantity: 3,
      price: 1800,
      discount: 200,
      shippingCost: 120,
      shippingAddress: '202 Hill View, Rajshahi, Bangladesh',
      customerName: 'Michael Brown',
      customerPhone: '+*************',
      status: ShopTransactionStatus.cancelled,
      timestamp: DateTime.now().subtract(const Duration(days: 8)),
      transactionReference: 'REF567890',
      paymentMethod: 'Rocket',
      failureReason: 'Customer cancelled the order',
    ),
    ShopTransaction(
      id: 'ST006',
      productId: 'P006',
      productName: 'Laptop Backpack',
      productImage: 'assets/images/products/backpack.png',
      quantity: 1,
      price: 2200,
      shippingCost: 90,
      shippingAddress: '303 Green Road, Barisal, Bangladesh',
      customerName: 'Sarah Davis',
      customerPhone: '+*************',
      status: ShopTransactionStatus.completed,
      timestamp: DateTime.now().subtract(const Duration(days: 15)),
      transactionReference: 'REF123789',
      paymentMethod: 'Cash on Delivery',
    ),
    ShopTransaction(
      id: 'ST007',
      productId: 'P007',
      productName: 'Gaming Mouse',
      productImage: 'assets/images/products/mouse.png',
      quantity: 1,
      price: 1500,
      shippingCost: 70,
      shippingAddress: '404 Tech Street, Dhaka, Bangladesh',
      customerName: 'David Wilson',
      customerPhone: '+8801412345678',
      status: ShopTransactionStatus.refunded,
      timestamp: DateTime.now().subtract(const Duration(days: 10)),
      transactionReference: 'REF456123',
      paymentMethod: 'bKash',
      failureReason: 'Product was defective',
    ),
  ];
}

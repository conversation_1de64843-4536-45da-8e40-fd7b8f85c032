import 'package:irecharge_pro/data/models/mobile_operator_model.dart';

/// Types of offer packages
enum PackageType {
  data,
  minutes,
  sms,
  combo,
}

/// Model representing a mobile operator offer package
class OfferPackage {
  final String id;
  final String operatorId;
  final String title;
  final String description;
  final double price;
  final PackageType type;
  final Map<String, String> benefits;
  final int validityDays;
  final String? activationCode;
  final bool isActive;
  final bool isFeatured;
  final double? cashbackAmount;
  final String? cashbackDescription;

  const OfferPackage({
    required this.id,
    required this.operatorId,
    required this.title,
    required this.description,
    required this.price,
    required this.type,
    required this.benefits,
    required this.validityDays,
    this.activationCode,
    this.isActive = true,
    this.isFeatured = false,
    this.cashbackAmount,
    this.cashbackDescription,
  });

  /// Create OfferPackage from JSON
  factory OfferPackage.fromJson(Map<String, dynamic> json) {
    return OfferPackage(
      id: json['id'] as String,
      operatorId: json['operator_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      type: PackageType.values.firstWhere(
        (e) => e.toString() == 'PackageType.${json['type']}',
        orElse: () => PackageType.combo,
      ),
      benefits: Map<String, String>.from(json['benefits'] as Map),
      validityDays: json['validity_days'] as int,
      activationCode: json['activation_code'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      isFeatured: json['is_featured'] as bool? ?? false,
      cashbackAmount: json['cashback_amount'] != null ? (json['cashback_amount'] as num).toDouble() : null,
      cashbackDescription: json['cashback_description'] as String?,
    );
  }

  /// Convert OfferPackage to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operator_id': operatorId,
      'title': title,
      'description': description,
      'price': price,
      'type': type.toString().split('.').last,
      'benefits': benefits,
      'validity_days': validityDays,
      'activation_code': activationCode,
      'is_active': isActive,
      'is_featured': isFeatured,
      'cashback_amount': cashbackAmount,
      'cashback_description': cashbackDescription,
    };
  }

  /// Get formatted cashback amount
  String? get formattedCashback => cashbackAmount != null ? '৳${cashbackAmount!.toStringAsFixed(2)}' : null;

  /// Get packages for a specific operator
  static List<OfferPackage> getPackagesForOperator(String operatorId) {
    return SampleOfferPackages.getByOperator(operatorId);
  }

  /// Get the operator for this package
  MobileOperator? get operator {
    return MobileOperator.getOperator(operatorId);
  }

  /// Get formatted price
  String get formattedPrice => '৳${price.toStringAsFixed(2)}';

  /// Get formatted validity
  String get formattedValidity => '$validityDays Days';

  /// Get primary benefit based on package type
  String get primaryBenefit {
    switch (type) {
      case PackageType.data:
        return benefits['data'] ?? '';
      case PackageType.minutes:
        return benefits['minutes'] ?? '';
      case PackageType.sms:
        return benefits['sms'] ?? '';
      case PackageType.combo:
        return benefits['data'] ?? benefits['minutes'] ?? benefits['sms'] ?? '';
    }
  }
}

/// Sample offer packages for testing
class SampleOfferPackages {
  // Grameenphone packages
  static final List<OfferPackage> gpPackages = [
    OfferPackage(
      id: 'gp_data_1',
      operatorId: 'gp',
      title: 'Internet Pack 1GB',
      description: '1GB data for 7 days',
      price: 99,
      type: PackageType.data,
      benefits: {
        'data': '1GB',
      },
      validityDays: 7,
      activationCode: '*121*1*1#',
      isFeatured: true,
      cashbackAmount: 10,
      cashbackDescription: '10% cashback on recharge',
    ),
    OfferPackage(
      id: 'gp_data_2',
      operatorId: 'gp',
      title: 'Internet Pack 2GB',
      description: '2GB data for 15 days',
      price: 179,
      type: PackageType.data,
      benefits: {
        'data': '2GB',
      },
      validityDays: 15,
      activationCode: '*121*1*2#',
    ),
    OfferPackage(
      id: 'gp_combo_1',
      operatorId: 'gp',
      title: 'Combo Pack',
      description: '1GB data + 100 Minutes + 100 SMS',
      price: 249,
      type: PackageType.combo,
      benefits: {
        'data': '1GB',
        'minutes': '100 Min',
        'sms': '100 SMS',
      },
      validityDays: 30,
      activationCode: '*121*3*1#',
      isFeatured: true,
      cashbackAmount: 25,
      cashbackDescription: '10% cashback on recharge',
    ),
  ];

  // Robi packages
  static final List<OfferPackage> robiPackages = [
    OfferPackage(
      id: 'robi_data_1',
      operatorId: 'robi',
      title: 'Internet Pack 1.5GB',
      description: '1.5GB data for 7 days',
      price: 98,
      type: PackageType.data,
      benefits: {
        'data': '1.5GB',
      },
      validityDays: 7,
      activationCode: '*123*1*1#',
      isFeatured: true,
      cashbackAmount: 15,
      cashbackDescription: '15% cashback on first recharge',
    ),
    OfferPackage(
      id: 'robi_minutes_1',
      operatorId: 'robi',
      title: 'Talk Time Pack',
      description: '200 Minutes for 15 days',
      price: 150,
      type: PackageType.minutes,
      benefits: {
        'minutes': '200 Min',
      },
      validityDays: 15,
      activationCode: '*123*2*1#',
    ),
  ];

  // Airtel packages
  static final List<OfferPackage> airtelPackages = [
    OfferPackage(
      id: 'airtel_data_1',
      operatorId: 'airtel',
      title: 'Internet Pack 2GB',
      description: '2GB data for 10 days',
      price: 129,
      type: PackageType.data,
      benefits: {
        'data': '2GB',
      },
      validityDays: 10,
      activationCode: '*123*1*1#',
      isFeatured: true,
      cashbackAmount: 20,
      cashbackDescription: 'Instant cashback to wallet',
    ),
    OfferPackage(
      id: 'airtel_combo_1',
      operatorId: 'airtel',
      title: 'Combo Pack',
      description: '1GB data + 150 Minutes + 150 SMS',
      price: 279,
      type: PackageType.combo,
      benefits: {
        'data': '1GB',
        'minutes': '150 Min',
        'sms': '150 SMS',
      },
      validityDays: 30,
      activationCode: '*123*3*1#',
    ),
  ];

  // Banglalink packages
  static final List<OfferPackage> banglalinkPackages = [
    OfferPackage(
      id: 'bl_data_1',
      operatorId: 'banglalink',
      title: 'Internet Pack 1GB',
      description: '1GB data for 7 days',
      price: 94,
      type: PackageType.data,
      benefits: {
        'data': '1GB',
      },
      validityDays: 7,
      activationCode: '*124*1*1#',
      isFeatured: true,
    ),
    OfferPackage(
      id: 'bl_minutes_1',
      operatorId: 'banglalink',
      title: 'Talk Time Pack',
      description: '180 Minutes for 15 days',
      price: 140,
      type: PackageType.minutes,
      benefits: {
        'minutes': '180 Min',
      },
      validityDays: 15,
      activationCode: '*124*2*1#',
    ),
  ];

  // Teletalk packages
  static final List<OfferPackage> teletalkPackages = [
    OfferPackage(
      id: 'teletalk_data_1',
      operatorId: 'teletalk',
      title: 'Internet Pack 1GB',
      description: '1GB data for 10 days',
      price: 90,
      type: PackageType.data,
      benefits: {
        'data': '1GB',
      },
      validityDays: 10,
      activationCode: '*111*1*1#',
      isFeatured: true,
    ),
    OfferPackage(
      id: 'teletalk_combo_1',
      operatorId: 'teletalk',
      title: 'Combo Pack',
      description: '1GB data + 120 Minutes + 120 SMS',
      price: 230,
      type: PackageType.combo,
      benefits: {
        'data': '1GB',
        'minutes': '120 Min',
        'sms': '120 SMS',
      },
      validityDays: 30,
      activationCode: '*111*3*1#',
    ),
  ];

  // Get all packages
  static List<OfferPackage> getAll() {
    return [
      ...gpPackages,
      ...robiPackages,
      ...airtelPackages,
      ...banglalinkPackages,
      ...teletalkPackages,
    ];
  }

  // Get packages by operator
  static List<OfferPackage> getByOperator(String operatorId) {
    switch (operatorId) {
      case 'gp':
        return gpPackages;
      case 'robi':
        return robiPackages;
      case 'airtel':
        return airtelPackages;
      case 'banglalink':
        return banglalinkPackages;
      case 'teletalk':
        return teletalkPackages;
      default:
        return [];
    }
  }

  // Get featured packages
  static List<OfferPackage> getFeatured() {
    return getAll().where((package) => package.isFeatured).toList();
  }
}

import 'package:flutter/material.dart';

/// Mobile operator model
class MobileOperator {
  final String id;
  final String name;
  final String logoPath;
  final Color color;
  final List<String> prefixes;

  const MobileOperator({
    required this.id,
    required this.name,
    required this.logoPath,
    required this.color,
    required this.prefixes,
  });

  /// Get all operators
  static List<MobileOperator> getAllOperators() {
    return [
      getOperator('gp')!,
      getOperator('robi')!,
      getOperator('banglalink')!,
      getOperator('teletalk')!,
      getOperator('airtel')!,
    ];
  }

  /// Get operator by ID
  static MobileOperator? getOperator(String id) {
    switch (id) {
      case 'gp':
        return const MobileOperator(
          id: 'gp',
          name: 'Grameenphone',
          logoPath: 'assets/images/operators/gp.png',
          color: Color(0xFF1976D2),
          prefixes: ['017', '013'],
        );
      case 'robi':
        return const MobileOperator(
          id: 'robi',
          name: '<PERSON><PERSON>',
          logoPath: 'assets/images/operators/robi.png',
          color: Color(0xFFE53935),
          prefixes: ['018', '016'],
        );
      case 'banglalink':
        return const MobileOperator(
          id: 'banglalink',
          name: 'Banglalink',
          logoPath: 'assets/images/operators/banglalink.png',
          color: Color(0xFFFF9800),
          prefixes: ['019', '014'],
        );
      case 'teletalk':
        return const MobileOperator(
          id: 'teletalk',
          name: 'Teletalk',
          logoPath: 'assets/images/operators/teletalk.png',
          color: Color(0xFF4CAF50),
          prefixes: ['015'],
        );
      case 'airtel':
        return const MobileOperator(
          id: 'airtel',
          name: 'Airtel',
          logoPath: 'assets/images/operators/airtel.png',
          color: Color(0xFFD32F2F),
          prefixes: ['011'],
        );
      default:
        return null;
    }
  }
}

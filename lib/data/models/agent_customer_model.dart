import 'package:flutter/material.dart';

/// Agent customer status enum
enum AgentCustomerStatus {
  active,
  inactive,
  pending,
  blocked
}

/// Agent customer type enum
enum AgentCustomerType {
  customer,
  subAgent,
  business,
  vip
}

/// Agent customer model
class AgentCustomer {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String? address;
  final DateTime createdAt;
  final DateTime? lastTransaction;
  final double balance;
  final int totalTransactions;
  final AgentCustomerStatus status;
  final AgentCustomerType type;
  final String? notes;
  final String? profileImage;

  const AgentCustomer({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    this.address,
    required this.createdAt,
    this.lastTransaction,
    required this.balance,
    required this.totalTransactions,
    required this.status,
    required this.type,
    this.notes,
    this.profileImage,
  });

  /// Create a copy of this customer with given fields replaced with new values
  Agent<PERSON>ust<PERSON> copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    DateTime? createdAt,
    DateTime? lastTransaction,
    double? balance,
    int? totalTransactions,
    AgentCustomerStatus? status,
    AgentCustomerType? type,
    String? notes,
    String? profileImage,
  }) {
    return AgentCustomer(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      createdAt: createdAt ?? this.createdAt,
      lastTransaction: lastTransaction ?? this.lastTransaction,
      balance: balance ?? this.balance,
      totalTransactions: totalTransactions ?? this.totalTransactions,
      status: status ?? this.status,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      profileImage: profileImage ?? this.profileImage,
    );
  }

  /// Get status color
  static Color getStatusColor(AgentCustomerStatus status) {
    switch (status) {
      case AgentCustomerStatus.active:
        return Colors.green;
      case AgentCustomerStatus.inactive:
        return Colors.grey;
      case AgentCustomerStatus.pending:
        return Colors.orange;
      case AgentCustomerStatus.blocked:
        return Colors.red;
    }
  }

  /// Get status text
  static String getStatusText(AgentCustomerStatus status) {
    switch (status) {
      case AgentCustomerStatus.active:
        return 'Active';
      case AgentCustomerStatus.inactive:
        return 'Inactive';
      case AgentCustomerStatus.pending:
        return 'Pending';
      case AgentCustomerStatus.blocked:
        return 'Blocked';
    }
  }

  /// Get customer type text
  static String getTypeText(AgentCustomerType type) {
    switch (type) {
      case AgentCustomerType.customer:
        return 'Customer';
      case AgentCustomerType.subAgent:
        return 'Sub Agent';
      case AgentCustomerType.business:
        return 'Business';
      case AgentCustomerType.vip:
        return 'VIP';
    }
  }

  /// Get customer type color
  static Color getTypeColor(AgentCustomerType type) {
    switch (type) {
      case AgentCustomerType.customer:
        return Colors.blue;
      case AgentCustomerType.subAgent:
        return Colors.purple;
      case AgentCustomerType.business:
        return Colors.teal;
      case AgentCustomerType.vip:
        return Colors.amber;
    }
  }
}

/// Demo customers data
class DemoAgentCustomers {
  static List<AgentCustomer> get customers => [
    AgentCustomer(
      id: 'AC001',
      name: 'Ahmed Khan',
      phone: '+880 1712345678',
      email: '<EMAIL>',
      address: 'Dhaka, Bangladesh',
      createdAt: DateTime(2023, 1, 15),
      lastTransaction: DateTime(2023, 5, 20),
      balance: 5000.0,
      totalTransactions: 42,
      status: AgentCustomerStatus.active,
      type: AgentCustomerType.subAgent,
      notes: 'Loyal customer since 2023',
    ),
    AgentCustomer(
      id: 'AC002',
      name: 'Fatima Rahman',
      phone: '+880 1812345678',
      email: '<EMAIL>',
      address: 'Chittagong, Bangladesh',
      createdAt: DateTime(2023, 2, 10),
      lastTransaction: DateTime(2023, 5, 18),
      balance: 3500.0,
      totalTransactions: 28,
      status: AgentCustomerStatus.active,
      type: AgentCustomerType.customer,
    ),
    AgentCustomer(
      id: 'AC003',
      name: 'Mohammad Ali',
      phone: '+880 1912345678',
      email: '<EMAIL>',
      address: 'Sylhet, Bangladesh',
      createdAt: DateTime(2023, 3, 5),
      lastTransaction: DateTime(2023, 4, 30),
      balance: 1200.0,
      totalTransactions: 15,
      status: AgentCustomerStatus.inactive,
      type: AgentCustomerType.customer,
      notes: 'Needs follow-up',
    ),
    AgentCustomer(
      id: 'AC004',
      name: 'Nusrat Jahan',
      phone: '+880 1612345678',
      email: '<EMAIL>',
      address: 'Khulna, Bangladesh',
      createdAt: DateTime(2023, 4, 20),
      lastTransaction: null,
      balance: 0.0,
      totalTransactions: 0,
      status: AgentCustomerStatus.pending,
      type: AgentCustomerType.customer,
      notes: 'New customer, pending verification',
    ),
    AgentCustomer(
      id: 'AC005',
      name: 'Kamal Hossain',
      phone: '+880 **********',
      email: '<EMAIL>',
      address: 'Rajshahi, Bangladesh',
      createdAt: DateTime(2022, 11, 12),
      lastTransaction: DateTime(2023, 5, 15),
      balance: 12000.0,
      totalTransactions: 87,
      status: AgentCustomerStatus.active,
      type: AgentCustomerType.business,
      notes: 'Business account for local shop',
    ),
    AgentCustomer(
      id: 'AC006',
      name: 'Sadia Islam',
      phone: '+880 **********',
      email: '<EMAIL>',
      address: 'Barisal, Bangladesh',
      createdAt: DateTime(2023, 1, 5),
      lastTransaction: DateTime(2023, 3, 10),
      balance: 2500.0,
      totalTransactions: 18,
      status: AgentCustomerStatus.blocked,
      type: AgentCustomerType.customer,
      notes: 'Account blocked due to suspicious activity',
    ),
    AgentCustomer(
      id: 'AC007',
      name: 'Rahim Uddin',
      phone: '+880 **********',
      email: '<EMAIL>',
      address: 'Comilla, Bangladesh',
      createdAt: DateTime(2022, 12, 8),
      lastTransaction: DateTime(2023, 5, 22),
      balance: 8500.0,
      totalTransactions: 56,
      status: AgentCustomerStatus.active,
      type: AgentCustomerType.vip,
      notes: 'VIP customer with high transaction volume',
    ),
    AgentCustomer(
      id: 'AC008',
      name: 'Nasreen Akter',
      phone: '+880 **********',
      email: '<EMAIL>',
      address: 'Rangpur, Bangladesh',
      createdAt: DateTime(2023, 2, 28),
      lastTransaction: DateTime(2023, 5, 10),
      balance: 4200.0,
      totalTransactions: 31,
      status: AgentCustomerStatus.active,
      type: AgentCustomerType.subAgent,
    ),
    AgentCustomer(
      id: 'AC009',
      name: 'Jamal Uddin',
      phone: '+880 **********',
      email: '<EMAIL>',
      address: 'Mymensingh, Bangladesh',
      createdAt: DateTime(2023, 3, 15),
      lastTransaction: DateTime(2023, 4, 5),
      balance: 1800.0,
      totalTransactions: 12,
      status: AgentCustomerStatus.inactive,
      type: AgentCustomerType.customer,
      notes: 'Seasonal customer',
    ),
    AgentCustomer(
      id: 'AC010',
      name: 'Tahmina Begum',
      phone: '+880 1012345678',
      email: '<EMAIL>',
      address: 'Jessore, Bangladesh',
      createdAt: DateTime(2022, 10, 20),
      lastTransaction: DateTime(2023, 5, 21),
      balance: 15000.0,
      totalTransactions: 102,
      status: AgentCustomerStatus.active,
      type: AgentCustomerType.business,
      notes: 'High-value business customer',
    ),
  ];

  /// Get customer by ID
  static AgentCustomer? getById(String id) {
    try {
      return customers.firstWhere((customer) => customer.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get customers by status
  static List<AgentCustomer> getByStatus(AgentCustomerStatus status) {
    return customers.where((customer) => customer.status == status).toList();
  }

  /// Get customers by type
  static List<AgentCustomer> getByType(AgentCustomerType type) {
    return customers.where((customer) => customer.type == type).toList();
  }
}

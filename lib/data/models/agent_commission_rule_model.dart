import 'package:flutter/material.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/models/service_commission_model.dart';

/// Agent Commission Rule model
class AgentCommissionRule {
  final String id;
  final String agentId;
  final AgentCustomerType agentType;
  final ServiceType serviceType;
  final String? operatorId;
  final String? operatorName;
  final double commissionRate;
  final CommissionType commissionType;
  final double? serviceCharge;
  final ChargeType chargeType;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final double? minTransactionAmount;
  final double? maxTransactionAmount;

  const AgentCommissionRule({
    required this.id,
    required this.agentId,
    required this.agentType,
    required this.serviceType,
    this.operatorId,
    this.operatorName,
    required this.commissionRate,
    required this.commissionType,
    this.serviceCharge,
    required this.chargeType,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.minTransactionAmount,
    this.maxTransactionAmount,
  });

  /// Copy with
  AgentCommissionRule copyWith({
    String? id,
    String? agentId,
    AgentCustomerType? agentType,
    ServiceType? serviceType,
    String? operatorId,
    String? operatorName,
    double? commissionRate,
    CommissionType? commissionType,
    double? serviceCharge,
    ChargeType? chargeType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? minTransactionAmount,
    double? maxTransactionAmount,
  }) {
    return AgentCommissionRule(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentType: agentType ?? this.agentType,
      serviceType: serviceType ?? this.serviceType,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      commissionRate: commissionRate ?? this.commissionRate,
      commissionType: commissionType ?? this.commissionType,
      serviceCharge: serviceCharge ?? this.serviceCharge,
      chargeType: chargeType ?? this.chargeType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      minTransactionAmount: minTransactionAmount ?? this.minTransactionAmount,
      maxTransactionAmount: maxTransactionAmount ?? this.maxTransactionAmount,
    );
  }

  /// Get formatted commission rate
  String get formattedCommissionRate {
    if (commissionType == CommissionType.percentage) {
      return '$commissionRate%';
    } else {
      return '৳$commissionRate';
    }
  }

  /// Get formatted service charge
  String get formattedServiceCharge {
    if (serviceCharge == null || chargeType == ChargeType.none) {
      return 'No Charge';
    } else if (chargeType == ChargeType.percentage) {
      return '$serviceCharge%';
    } else {
      return '৳$serviceCharge';
    }
  }

  /// Calculate commission amount for a transaction
  double calculateCommission(double transactionAmount) {
    if (commissionType == CommissionType.percentage) {
      return transactionAmount * (commissionRate / 100);
    } else {
      return commissionRate;
    }
  }

  /// Calculate service charge amount for a transaction
  double calculateServiceCharge(double transactionAmount) {
    if (serviceCharge == null || chargeType == ChargeType.none) {
      return 0;
    } else if (chargeType == ChargeType.percentage) {
      return transactionAmount * (serviceCharge! / 100);
    } else {
      return serviceCharge!;
    }
  }
}

/// Demo agent commission rules data
class DemoAgentCommissionRules {
  static List<AgentCommissionRule> get agentCommissionRules => [
    // Mobile Recharge - Sub Agents
    AgentCommissionRule(
      id: 'ACR001',
      agentId: 'AC001',
      agentType: AgentCustomerType.subAgent,
      serviceType: ServiceType.recharge,
      operatorId: 'OP001',
      operatorName: 'Airtel',
      commissionRate: 3.0,
      commissionType: CommissionType.percentage,
      serviceCharge: 0.5,
      chargeType: ChargeType.percentage,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    AgentCommissionRule(
      id: 'ACR002',
      agentId: 'AC001',
      agentType: AgentCustomerType.subAgent,
      serviceType: ServiceType.recharge,
      operatorId: 'OP002',
      operatorName: 'Grameenphone',
      commissionRate: 2.5,
      commissionType: CommissionType.percentage,
      serviceCharge: 0.5,
      chargeType: ChargeType.percentage,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    
    // Mobile Banking - Sub Agents
    AgentCommissionRule(
      id: 'ACR003',
      agentId: 'AC001',
      agentType: AgentCustomerType.subAgent,
      serviceType: ServiceType.mobileBanking,
      operatorId: 'MB001',
      operatorName: 'bKash',
      commissionRate: 1.5,
      commissionType: CommissionType.percentage,
      serviceCharge: 5,
      chargeType: ChargeType.fixedAmount,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
    
    // Bill Payment - Sub Agents
    AgentCommissionRule(
      id: 'ACR004',
      agentId: 'AC001',
      agentType: AgentCustomerType.subAgent,
      serviceType: ServiceType.billPayment,
      operatorId: 'BP001',
      operatorName: 'DESCO',
      commissionRate: 2.0,
      commissionType: CommissionType.percentage,
      serviceCharge: null,
      chargeType: ChargeType.none,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
    ),
    
    // Mobile Recharge - Customers
    AgentCommissionRule(
      id: 'ACR005',
      agentId: 'AC003',
      agentType: AgentCustomerType.customer,
      serviceType: ServiceType.recharge,
      operatorId: 'OP001',
      operatorName: 'Airtel',
      commissionRate: 1.0,
      commissionType: CommissionType.percentage,
      serviceCharge: 1.0,
      chargeType: ChargeType.percentage,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
    ),
    
    // Mobile Banking - Customers
    AgentCommissionRule(
      id: 'ACR006',
      agentId: 'AC003',
      agentType: AgentCustomerType.customer,
      serviceType: ServiceType.mobileBanking,
      operatorId: 'MB001',
      operatorName: 'bKash',
      commissionRate: 0.5,
      commissionType: CommissionType.percentage,
      serviceCharge: 10,
      chargeType: ChargeType.fixedAmount,
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
    ),
  ];

  /// Get agent commission rule by ID
  static AgentCommissionRule? getById(String id) {
    try {
      return agentCommissionRules.firstWhere((rule) => rule.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get agent commission rules by agent ID
  static List<AgentCommissionRule> getByAgentId(String agentId) {
    return agentCommissionRules.where((rule) => rule.agentId == agentId).toList();
  }

  /// Generate a new agent commission rule ID
  static String generateId() {
    final lastId = agentCommissionRules.isNotEmpty 
        ? int.parse(agentCommissionRules.last.id.replaceAll('ACR', '')) 
        : 0;
    return 'ACR${(lastId + 1).toString().padLeft(3, '0')}';
  }
}

import 'package:flutter/material.dart';

/// Commission status enum
enum CommissionStatus {
  pending,
  approved,
  paid,
  rejected
}

/// Commission type enum
enum CommissionType {
  recharge,
  mobileBanking,
  billPayment,
  ticketSale,
  productSale,
  referral,
  other
}

/// Commission model
class Commission {
  final String id;
  final String agentId;
  final CommissionType type;
  final double amount;
  final double rate;
  final DateTime date;
  final String description;
  final CommissionStatus status;
  final String? transactionId;
  final String? notes;

  const Commission({
    required this.id,
    required this.agentId,
    required this.type,
    required this.amount,
    required this.rate,
    required this.date,
    required this.description,
    required this.status,
    this.transactionId,
    this.notes,
  });

  /// Create a copy of this commission with given fields replaced with new values
  Commission copyWith({
    String? id,
    String? agentId,
    CommissionType? type,
    double? amount,
    double? rate,
    DateTime? date,
    String? description,
    CommissionStatus? status,
    String? transactionId,
    String? notes,
  }) {
    return Commission(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      rate: rate ?? this.rate,
      date: date ?? this.date,
      description: description ?? this.description,
      status: status ?? this.status,
      transactionId: transactionId ?? this.transactionId,
      notes: notes ?? this.notes,
    );
  }

  /// Get formatted amount
  String get formattedAmount => '৳${amount.toStringAsFixed(2)}';

  /// Get formatted date
  String get formattedDate {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get formatted time
  String get formattedTime {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Get commission status text
  static String getStatusText(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return 'Pending';
      case CommissionStatus.approved:
        return 'Approved';
      case CommissionStatus.paid:
        return 'Paid';
      case CommissionStatus.rejected:
        return 'Rejected';
    }
  }

  /// Get commission status color
  static Color getStatusColor(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return Colors.amber;
      case CommissionStatus.approved:
        return Colors.green;
      case CommissionStatus.paid:
        return Colors.blue;
      case CommissionStatus.rejected:
        return Colors.red;
    }
  }

  /// Get commission type text
  static String getTypeText(CommissionType type) {
    switch (type) {
      case CommissionType.recharge:
        return 'Recharge';
      case CommissionType.mobileBanking:
        return 'Mobile Banking';
      case CommissionType.billPayment:
        return 'Bill Payment';
      case CommissionType.ticketSale:
        return 'Ticket Sale';
      case CommissionType.productSale:
        return 'Product Sale';
      case CommissionType.referral:
        return 'Referral';
      case CommissionType.other:
        return 'Other';
    }
  }

  /// Get commission type color
  static Color getTypeColor(CommissionType type) {
    switch (type) {
      case CommissionType.recharge:
        return Colors.purple;
      case CommissionType.mobileBanking:
        return Colors.teal;
      case CommissionType.billPayment:
        return Colors.indigo;
      case CommissionType.ticketSale:
        return Colors.orange;
      case CommissionType.productSale:
        return Colors.pink;
      case CommissionType.referral:
        return Colors.cyan;
      case CommissionType.other:
        return Colors.grey;
    }
  }

  /// Get commission type icon
  static IconData getTypeIcon(CommissionType type) {
    switch (type) {
      case CommissionType.recharge:
        return Icons.phone_android;
      case CommissionType.mobileBanking:
        return Icons.account_balance_wallet;
      case CommissionType.billPayment:
        return Icons.receipt_long;
      case CommissionType.ticketSale:
        return Icons.confirmation_number;
      case CommissionType.productSale:
        return Icons.shopping_bag;
      case CommissionType.referral:
        return Icons.people;
      case CommissionType.other:
        return Icons.more_horiz;
    }
  }
}

/// Demo commissions data
class DemoCommissions {
  static List<Commission> get commissions => [
    Commission(
      id: 'COM001',
      agentId: 'AC001',
      type: CommissionType.recharge,
      amount: 25.0,
      rate: 2.5,
      date: DateTime.now().subtract(const Duration(hours: 5)),
      description: 'Commission for mobile recharge',
      status: CommissionStatus.paid,
      transactionId: 'TX123456',
    ),
    Commission(
      id: 'COM002',
      agentId: 'AC001',
      type: CommissionType.mobileBanking,
      amount: 50.0,
      rate: 1.0,
      date: DateTime.now().subtract(const Duration(days: 1)),
      description: 'Commission for mobile banking transaction',
      status: CommissionStatus.approved,
      transactionId: 'TX123457',
    ),
    Commission(
      id: 'COM003',
      agentId: 'AC001',
      type: CommissionType.billPayment,
      amount: 30.0,
      rate: 1.5,
      date: DateTime.now().subtract(const Duration(days: 2)),
      description: 'Commission for electricity bill payment',
      status: CommissionStatus.pending,
      transactionId: 'TX123458',
    ),
    Commission(
      id: 'COM004',
      agentId: 'AC001',
      type: CommissionType.productSale,
      amount: 120.0,
      rate: 5.0,
      date: DateTime.now().subtract(const Duration(days: 3)),
      description: 'Commission for product sale',
      status: CommissionStatus.paid,
      transactionId: 'TX123459',
    ),
    Commission(
      id: 'COM005',
      agentId: 'AC001',
      type: CommissionType.referral,
      amount: 200.0,
      rate: 10.0,
      date: DateTime.now().subtract(const Duration(days: 5)),
      description: 'Commission for referring new sub-agent',
      status: CommissionStatus.paid,
      transactionId: 'TX123460',
    ),
    Commission(
      id: 'COM006',
      agentId: 'AC008',
      type: CommissionType.recharge,
      amount: 18.0,
      rate: 2.0,
      date: DateTime.now().subtract(const Duration(days: 1)),
      description: 'Commission for mobile recharge',
      status: CommissionStatus.pending,
      transactionId: 'TX123461',
    ),
    Commission(
      id: 'COM007',
      agentId: 'AC008',
      type: CommissionType.billPayment,
      amount: 45.0,
      rate: 1.5,
      date: DateTime.now().subtract(const Duration(days: 2)),
      description: 'Commission for water bill payment',
      status: CommissionStatus.approved,
      transactionId: 'TX123462',
    ),
    Commission(
      id: 'COM008',
      agentId: 'AC005',
      type: CommissionType.mobileBanking,
      amount: 75.0,
      rate: 1.5,
      date: DateTime.now().subtract(const Duration(days: 1)),
      description: 'Commission for mobile banking transaction',
      status: CommissionStatus.paid,
      transactionId: 'TX123463',
    ),
    Commission(
      id: 'COM009',
      agentId: 'AC005',
      type: CommissionType.ticketSale,
      amount: 150.0,
      rate: 3.0,
      date: DateTime.now().subtract(const Duration(days: 3)),
      description: 'Commission for bus ticket sale',
      status: CommissionStatus.paid,
      transactionId: 'TX123464',
    ),
    Commission(
      id: 'COM010',
      agentId: 'AC007',
      type: CommissionType.productSale,
      amount: 250.0,
      rate: 5.0,
      date: DateTime.now().subtract(const Duration(days: 2)),
      description: 'Commission for high-value product sale',
      status: CommissionStatus.approved,
      transactionId: 'TX123465',
    ),
  ];

  /// Get commission by ID
  static Commission? getById(String id) {
    try {
      return commissions.firstWhere((commission) => commission.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get commissions by agent ID
  static List<Commission> getByAgentId(String agentId) {
    return commissions.where((commission) => commission.agentId == agentId).toList();
  }

  /// Get commissions by status
  static List<Commission> getByStatus(CommissionStatus status) {
    return commissions.where((commission) => commission.status == status).toList();
  }

  /// Get commissions by type
  static List<Commission> getByType(CommissionType type) {
    return commissions.where((commission) => commission.type == type).toList();
  }

  /// Get total commission amount for an agent
  static double getTotalCommissionForAgent(String agentId) {
    final agentCommissions = getByAgentId(agentId);
    return agentCommissions.fold(0, (sum, commission) => sum + commission.amount);
  }

  /// Get pending commission amount for an agent
  static double getPendingCommissionForAgent(String agentId) {
    final agentCommissions = getByAgentId(agentId).where(
      (commission) => commission.status == CommissionStatus.pending ||
                      commission.status == CommissionStatus.approved
    ).toList();
    return agentCommissions.fold(0, (sum, commission) => sum + commission.amount);
  }

  /// Get paid commission amount for an agent
  static double getPaidCommissionForAgent(String agentId) {
    final agentCommissions = getByAgentId(agentId).where(
      (commission) => commission.status == CommissionStatus.paid
    ).toList();
    return agentCommissions.fold(0, (sum, commission) => sum + commission.amount);
  }

  /// Generate a new commission ID
  static String generateCommissionId() {
    final lastId = commissions.isNotEmpty
        ? int.parse(commissions.last.id.replaceAll('COM', ''))
        : 0;
    return 'COM${(lastId + 1).toString().padLeft(3, '0')}';
  }
}

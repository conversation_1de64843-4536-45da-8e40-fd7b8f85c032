import 'package:flutter/material.dart';

/// Bank model
class Bank {
  final String id;
  final String name;
  final String logoPath;
  final Color color;
  final bool isPopular;

  const Bank({
    required this.id,
    required this.name,
    required this.logoPath,
    required this.color,
    this.isPopular = false,
  });

  /// Get all banks
  static List<Bank> getAllBanks() {
    return [
      // Popular banks
      getBankById('brac')!,
      getBankById('dbbl')!,
      getBankById('ebl')!,
      getBankById('citybank')!,
      getBankById('ucb')!,
      // Other banks
      getBankById('sonali')!,
      getBankById('janata')!,
      getBankById('agrani')!,
      getBankById('rupali')!,
      getBankById('ab')!,
      getBankById('bankasia')!,
      getBankById('ific')!,
      getBankById('ncc')!,
      getBankById('onebankltd')!,
      getBankById('pubali')!,
      getBankById('southeast')!,
      getBankById('standardbank')!,
      getBankById('uttara')!,
    ];
  }

  /// Get popular banks
  static List<Bank> getPopularBanks() {
    return getAllBanks().where((bank) => bank.isPopular).toList();
  }

  /// Get bank by ID
  static Bank? getBankById(String id) {
    switch (id) {
      // Popular banks
      case 'brac':
        return const Bank(
          id: 'brac',
          name: 'BRAC Bank',
          logoPath: 'assets/images/banks/brac.png',
          color: Color(0xFF00A0DF),
          isPopular: true,
        );
      case 'dbbl':
        return const Bank(
          id: 'dbbl',
          name: 'Dutch-Bangla Bank',
          logoPath: 'assets/images/banks/dbbl.png',
          color: Color(0xFFE4002B),
          isPopular: true,
        );
      case 'ebl':
        return const Bank(
          id: 'ebl',
          name: 'Eastern Bank Limited',
          logoPath: 'assets/images/banks/ebl.png',
          color: Color(0xFF00A651),
          isPopular: true,
        );
      case 'citybank':
        return const Bank(
          id: 'citybank',
          name: 'City Bank',
          logoPath: 'assets/images/banks/citybank.png',
          color: Color(0xFF1E3F66),
          isPopular: true,
        );
      case 'ucb':
        return const Bank(
          id: 'ucb',
          name: 'United Commercial Bank',
          logoPath: 'assets/images/banks/ucb.png',
          color: Color(0xFF00529B),
          isPopular: true,
        );
      // Other banks
      case 'sonali':
        return const Bank(
          id: 'sonali',
          name: 'Sonali Bank',
          logoPath: 'assets/images/banks/sonali.png',
          color: Color(0xFF007236),
        );
      case 'janata':
        return const Bank(
          id: 'janata',
          name: 'Janata Bank',
          logoPath: 'assets/images/banks/janata.png',
          color: Color(0xFF005BAA),
        );
      case 'agrani':
        return const Bank(
          id: 'agrani',
          name: 'Agrani Bank',
          logoPath: 'assets/images/banks/agrani.png',
          color: Color(0xFF00A651),
        );
      case 'rupali':
        return const Bank(
          id: 'rupali',
          name: 'Rupali Bank',
          logoPath: 'assets/images/banks/rupali.png',
          color: Color(0xFF00A651),
        );
      case 'ab':
        return const Bank(
          id: 'ab',
          name: 'AB Bank',
          logoPath: 'assets/images/banks/ab.png',
          color: Color(0xFF00A651),
        );
      case 'bankasia':
        return const Bank(
          id: 'bankasia',
          name: 'Bank Asia',
          logoPath: 'assets/images/banks/bankasia.png',
          color: Color(0xFF00A651),
        );
      case 'ific':
        return const Bank(
          id: 'ific',
          name: 'IFIC Bank',
          logoPath: 'assets/images/banks/ific.png',
          color: Color(0xFF00A651),
        );
      case 'ncc':
        return const Bank(
          id: 'ncc',
          name: 'NCC Bank',
          logoPath: 'assets/images/banks/ncc.png',
          color: Color(0xFF00A651),
        );
      case 'onebankltd':
        return const Bank(
          id: 'onebankltd',
          name: 'One Bank Ltd',
          logoPath: 'assets/images/banks/onebankltd.png',
          color: Color(0xFF00A651),
        );
      case 'pubali':
        return const Bank(
          id: 'pubali',
          name: 'Pubali Bank',
          logoPath: 'assets/images/banks/pubali.png',
          color: Color(0xFF00A651),
        );
      case 'southeast':
        return const Bank(
          id: 'southeast',
          name: 'Southeast Bank',
          logoPath: 'assets/images/banks/southeast.png',
          color: Color(0xFF00A651),
        );
      case 'standardbank':
        return const Bank(
          id: 'standardbank',
          name: 'Standard Bank',
          logoPath: 'assets/images/banks/standardbank.png',
          color: Color(0xFF00A651),
        );
      case 'uttara':
        return const Bank(
          id: 'uttara',
          name: 'Uttara Bank',
          logoPath: 'assets/images/banks/uttara.png',
          color: Color(0xFF00A651),
        );
      default:
        return null;
    }
  }
}

/// Bank account type enum
enum BankAccountType {
  savings,
  current,
  fixed,
  other
}

/// Bank account type extension
extension BankAccountTypeExtension on BankAccountType {
  String get name {
    switch (this) {
      case BankAccountType.savings:
        return 'Savings Account';
      case BankAccountType.current:
        return 'Current Account';
      case BankAccountType.fixed:
        return 'Fixed Deposit Account';
      case BankAccountType.other:
        return 'Other Account';
    }
  }
}

/// Bank transfer model
class BankTransfer {
  final String id;
  final String bankId;
  final String accountNumber;
  final String accountName;
  final String? branchName;
  final BankAccountType accountType;
  final double amount;
  final DateTime date;
  final String? reference;
  final String? note;
  final BankTransferStatus status;
  final String? transactionId;

  const BankTransfer({
    required this.id,
    required this.bankId,
    required this.accountNumber,
    required this.accountName,
    this.branchName,
    required this.accountType,
    required this.amount,
    required this.date,
    this.reference,
    this.note,
    required this.status,
    this.transactionId,
  });

  /// Get bank
  Bank? get bank => Bank.getBankById(bankId);

  /// Get formatted amount
  String get formattedAmount => '৳${amount.toStringAsFixed(2)}';

  /// Get formatted date
  String get formattedDate => '${date.day}/${date.month}/${date.year}';

  /// Get formatted time
  String get formattedTime => '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';

  /// Get status color
  Color get statusColor {
    switch (status) {
      case BankTransferStatus.pending:
        return Colors.amber;
      case BankTransferStatus.processing:
        return Colors.blue;
      case BankTransferStatus.completed:
        return Colors.green;
      case BankTransferStatus.failed:
        return Colors.red;
      case BankTransferStatus.cancelled:
        return Colors.grey;
    }
  }

  /// Get status text
  String get statusText {
    switch (status) {
      case BankTransferStatus.pending:
        return 'Pending';
      case BankTransferStatus.processing:
        return 'Processing';
      case BankTransferStatus.completed:
        return 'Completed';
      case BankTransferStatus.failed:
        return 'Failed';
      case BankTransferStatus.cancelled:
        return 'Cancelled';
    }
  }
}

/// Bank transfer status enum
enum BankTransferStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled
}

/// Sample bank transfers
class SampleBankTransfers {
  static List<BankTransfer> getAll() {
    return [
      BankTransfer(
        id: 'BT001',
        bankId: 'brac',
        accountNumber: '**********',
        accountName: 'John Doe',
        branchName: 'Gulshan Branch',
        accountType: BankAccountType.savings,
        amount: 5000.0,
        date: DateTime.now().subtract(const Duration(hours: 2)),
        reference: 'REF123456',
        note: 'Monthly rent payment',
        status: BankTransferStatus.completed,
        transactionId: 'TX123456',
      ),
      BankTransfer(
        id: 'BT002',
        bankId: 'dbbl',
        accountNumber: '**********',
        accountName: 'Jane Smith',
        branchName: 'Dhanmondi Branch',
        accountType: BankAccountType.current,
        amount: 3500.0,
        date: DateTime.now().subtract(const Duration(days: 1)),
        reference: 'REF123457',
        status: BankTransferStatus.completed,
        transactionId: 'TX123457',
      ),
      BankTransfer(
        id: 'BT003',
        bankId: 'ebl',
        accountNumber: '**********',
        accountName: 'Robert Johnson',
        accountType: BankAccountType.savings,
        amount: 10000.0,
        date: DateTime.now().subtract(const Duration(days: 2)),
        reference: 'REF123458',
        note: 'Business investment',
        status: BankTransferStatus.pending,
        transactionId: 'TX123458',
      ),
      BankTransfer(
        id: 'BT004',
        bankId: 'citybank',
        accountNumber: '**********',
        accountName: 'Emily Davis',
        accountType: BankAccountType.current,
        amount: 7500.0,
        date: DateTime.now().subtract(const Duration(days: 3)),
        reference: 'REF123459',
        status: BankTransferStatus.failed,
        transactionId: 'TX123459',
      ),
      BankTransfer(
        id: 'BT005',
        bankId: 'ucb',
        accountNumber: '**********',
        accountName: 'Michael Wilson',
        accountType: BankAccountType.savings,
        amount: 2000.0,
        date: DateTime.now().subtract(const Duration(days: 4)),
        reference: 'REF123460',
        note: 'Loan repayment',
        status: BankTransferStatus.completed,
        transactionId: 'TX123460',
      ),
    ];
  }

  /// Get by ID
  static BankTransfer? getById(String id) {
    try {
      return getAll().firstWhere((transfer) => transfer.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Generate new ID
  static String generateId() {
    final lastId = getAll().isNotEmpty ? int.parse(getAll().last.id.replaceAll('BT', '')) : 0;
    return 'BT${(lastId + 1).toString().padLeft(3, '0')}';
  }
}

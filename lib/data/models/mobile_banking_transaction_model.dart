import 'package:flutter/material.dart';
import 'package:irecharge_pro/data/models/mobile_banking_operator_model.dart';

/// Status of a mobile banking transaction
enum MobileBankingStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
}

/// Type of mobile banking transaction
enum MobileBankingType {
  cashIn,
  cashOut,
  sendMoney,
  payment,
  other,
}

/// Model representing a mobile banking transaction
class MobileBankingTransaction {
  final String id;
  final String operatorId;
  final String phoneNumber;
  final double amount;
  final DateTime timestamp;
  final MobileBankingStatus status;
  final MobileBankingType type;
  final String? transactionReference;
  final String? failureReason;
  final String? recipientName;
  final double? fee;

  const MobileBankingTransaction({
    required this.id,
    required this.operatorId,
    required this.phoneNumber,
    required this.amount,
    required this.timestamp,
    required this.status,
    required this.type,
    this.transactionReference,
    this.failureReason,
    this.recipientName,
    this.fee,
  });

  /// Get operator
  MobileBankingOperator? get operator => MobileBankingOperator.getOperator(operatorId);

  /// Get formatted amount
  String get formattedAmount => '৳${amount.toStringAsFixed(2)}';

  /// Get formatted date
  String get formattedDate => '${timestamp.day}/${timestamp.month}/${timestamp.year}';

  /// Get formatted time
  String get formattedTime => '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';

  /// Get status color
  Color get statusColor {
    switch (status) {
      case MobileBankingStatus.pending:
        return Colors.amber;
      case MobileBankingStatus.processing:
        return Colors.blue;
      case MobileBankingStatus.completed:
        return Colors.green;
      case MobileBankingStatus.failed:
        return Colors.red;
      case MobileBankingStatus.cancelled:
        return Colors.grey;
    }
  }

  /// Get status text
  String get statusText {
    switch (status) {
      case MobileBankingStatus.pending:
        return 'Pending';
      case MobileBankingStatus.processing:
        return 'Processing';
      case MobileBankingStatus.completed:
        return 'Completed';
      case MobileBankingStatus.failed:
        return 'Failed';
      case MobileBankingStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get type text
  String get typeText {
    switch (type) {
      case MobileBankingType.cashIn:
        return 'Cash In';
      case MobileBankingType.cashOut:
        return 'Cash Out';
      case MobileBankingType.sendMoney:
        return 'Send Money';
      case MobileBankingType.payment:
        return 'Payment';
      case MobileBankingType.other:
        return 'Other';
    }
  }

  /// Get type icon
  IconData get typeIcon {
    switch (type) {
      case MobileBankingType.cashIn:
        return Icons.arrow_downward;
      case MobileBankingType.cashOut:
        return Icons.arrow_upward;
      case MobileBankingType.sendMoney:
        return Icons.send;
      case MobileBankingType.payment:
        return Icons.payment;
      case MobileBankingType.other:
        return Icons.more_horiz;
    }
  }

  /// Get formatted fee
  String get formattedFee => fee != null ? '৳${fee!.toStringAsFixed(2)}' : '৳0.00';

  /// Get total amount (amount + fee)
  double get totalAmount => amount + (fee ?? 0);

  /// Get formatted total amount
  String get formattedTotalAmount => '৳${totalAmount.toStringAsFixed(2)}';
}

/// Sample mobile banking transactions for testing
class SampleMobileBankingTransactions {
  static List<MobileBankingTransaction> getAll() {
    final now = DateTime.now();
    
    return [
      MobileBankingTransaction(
        id: 'mb_001',
        operatorId: 'bkash',
        phoneNumber: '***********',
        amount: 1000.0,
        timestamp: now.subtract(const Duration(hours: 3)),
        status: MobileBankingStatus.completed,
        type: MobileBankingType.sendMoney,
        transactionReference: 'REF123456',
        recipientName: 'Ahmed Khan',
        fee: 5.0,
      ),
      MobileBankingTransaction(
        id: 'mb_002',
        operatorId: 'nagad',
        phoneNumber: '***********',
        amount: 500.0,
        timestamp: now.subtract(const Duration(days: 1)),
        status: MobileBankingStatus.completed,
        type: MobileBankingType.payment,
        transactionReference: 'REF123457',
        recipientName: 'Electricity Bill',
        fee: 0.0,
      ),
      MobileBankingTransaction(
        id: 'mb_003',
        operatorId: 'rocket',
        phoneNumber: '***********',
        amount: 2000.0,
        timestamp: now.subtract(const Duration(days: 2)),
        status: MobileBankingStatus.completed,
        type: MobileBankingType.cashIn,
        transactionReference: 'REF123458',
        fee: 0.0,
      ),
      MobileBankingTransaction(
        id: 'mb_004',
        operatorId: 'bkash',
        phoneNumber: '***********',
        amount: 1500.0,
        timestamp: now.subtract(const Duration(days: 3)),
        status: MobileBankingStatus.failed,
        type: MobileBankingType.cashOut,
        failureReason: 'Insufficient balance',
        fee: 18.5,
      ),
      MobileBankingTransaction(
        id: 'mb_005',
        operatorId: 'upay',
        phoneNumber: '***********',
        amount: 800.0,
        timestamp: now.subtract(const Duration(days: 4)),
        status: MobileBankingStatus.completed,
        type: MobileBankingType.sendMoney,
        transactionReference: 'REF123459',
        recipientName: 'Sarah Ahmed',
        fee: 5.0,
      ),
      MobileBankingTransaction(
        id: 'mb_006',
        operatorId: 'nagad',
        phoneNumber: '***********',
        amount: 1200.0,
        timestamp: now.subtract(const Duration(days: 5)),
        status: MobileBankingStatus.completed,
        type: MobileBankingType.payment,
        transactionReference: 'REF123460',
        recipientName: 'Water Bill',
        fee: 0.0,
      ),
      MobileBankingTransaction(
        id: 'mb_007',
        operatorId: 'bkash',
        phoneNumber: '***********',
        amount: 3000.0,
        timestamp: now.subtract(const Duration(days: 6)),
        status: MobileBankingStatus.completed,
        type: MobileBankingType.cashIn,
        transactionReference: 'REF123461',
        fee: 0.0,
      ),
      MobileBankingTransaction(
        id: 'mb_008',
        operatorId: 'rocket',
        phoneNumber: '***********',
        amount: 2500.0,
        timestamp: now.subtract(const Duration(days: 7)),
        status: MobileBankingStatus.pending,
        type: MobileBankingType.sendMoney,
        transactionReference: 'REF123462',
        recipientName: 'Karim Rahman',
        fee: 5.0,
      ),
    ];
  }

  /// Get recent transactions
  static List<MobileBankingTransaction> getRecent(int count) {
    final all = getAll();
    all.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return all.take(count).toList();
  }

  /// Get transactions by status
  static List<MobileBankingTransaction> getByStatus(MobileBankingStatus status) {
    return getAll().where((tx) => tx.status == status).toList();
  }

  /// Get transactions by type
  static List<MobileBankingTransaction> getByType(MobileBankingType type) {
    return getAll().where((tx) => tx.type == type).toList();
  }

  /// Get transactions by operator
  static List<MobileBankingTransaction> getByOperator(String operatorId) {
    return getAll().where((tx) => tx.operatorId == operatorId).toList();
  }
}

import 'package:flutter/material.dart';
import 'package:irecharge_pro/data/models/bank_model.dart';
import 'package:irecharge_pro/data/models/bill_payment_transaction_model.dart';
import 'package:irecharge_pro/data/models/mobile_banking_transaction_model.dart';
import 'package:irecharge_pro/data/models/mobile_operator_model.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';

/// Type of transaction
enum TransactionCategory {
  recharge,
  mobileBanking,
  billPayment,
  bankTransfer,
  wallet,
}

/// Unified transaction model
class UnifiedTransaction {
  final String id;
  final TransactionCategory category;
  final String title;
  final String subtitle;
  final double amount;
  final DateTime timestamp;
  final Color statusColor;
  final String statusText;
  final IconData icon;
  final Color iconColor;
  final dynamic originalTransaction;

  const UnifiedTransaction({
    required this.id,
    required this.category,
    required this.title,
    required this.subtitle,
    required this.amount,
    required this.timestamp,
    required this.statusColor,
    required this.statusText,
    required this.icon,
    required this.iconColor,
    required this.originalTransaction,
  });

  /// Create from recharge transaction
  factory UnifiedTransaction.fromRecharge(RechargeTransaction transaction) {
    final operator = MobileOperator.getOperator(transaction.operatorId);

    return UnifiedTransaction(
      id: transaction.id,
      category: TransactionCategory.recharge,
      title: 'Mobile Recharge',
      subtitle: '${operator?.name ?? 'Unknown'} - ${transaction.phoneNumber}',
      amount: transaction.amount,
      timestamp: transaction.timestamp,
      statusColor: _getRechargeStatusColor(transaction.status),
      statusText: _getRechargeStatusText(transaction.status),
      icon: Icons.phone_android,
      iconColor: operator?.color ?? Colors.grey,
      originalTransaction: transaction,
    );
  }

  /// Create from mobile banking transaction
  factory UnifiedTransaction.fromMobileBanking(MobileBankingTransaction transaction) {
    return UnifiedTransaction(
      id: transaction.id,
      category: TransactionCategory.mobileBanking,
      title: 'Mobile Banking - ${transaction.typeText}',
      subtitle: '${transaction.operator?.name ?? 'Unknown'} - ${transaction.phoneNumber}',
      amount: transaction.amount,
      timestamp: transaction.timestamp,
      statusColor: transaction.statusColor,
      statusText: transaction.statusText,
      icon: transaction.typeIcon,
      iconColor: transaction.operator?.color ?? Colors.grey,
      originalTransaction: transaction,
    );
  }

  /// Create from bill payment transaction
  factory UnifiedTransaction.fromBillPayment(BillPaymentTransaction transaction) {
    return UnifiedTransaction(
      id: transaction.id,
      category: TransactionCategory.billPayment,
      title: 'Bill Payment - ${transaction.typeText}',
      subtitle: '${transaction.provider} - ${transaction.accountNumber}',
      amount: transaction.amount,
      timestamp: transaction.timestamp,
      statusColor: transaction.statusColor,
      statusText: transaction.statusText,
      icon: transaction.typeIcon,
      iconColor: _getBillTypeColor(transaction.type),
      originalTransaction: transaction,
    );
  }

  /// Create from bank transfer
  factory UnifiedTransaction.fromBankTransfer(BankTransfer transfer) {
    return UnifiedTransaction(
      id: transfer.id,
      category: TransactionCategory.bankTransfer,
      title: 'Bank Transfer',
      subtitle: '${transfer.bank?.name ?? 'Unknown'} - ${transfer.accountNumber}',
      amount: transfer.amount,
      timestamp: transfer.date,
      statusColor: _getBankTransferStatusColor(transfer.status),
      statusText: _getBankTransferStatusText(transfer.status),
      icon: Icons.account_balance,
      iconColor: transfer.bank?.color ?? Colors.grey,
      originalTransaction: transfer,
    );
  }

  /// Get formatted amount
  String get formattedAmount => '৳${amount.toStringAsFixed(2)}';

  /// Get formatted date
  String get formattedDate => '${timestamp.day}/${timestamp.month}/${timestamp.year}';

  /// Get formatted time
  String get formattedTime => '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';

  /// Get category text
  String get categoryText {
    switch (category) {
      case TransactionCategory.recharge:
        return 'Mobile Recharge';
      case TransactionCategory.mobileBanking:
        return 'Mobile Banking';
      case TransactionCategory.billPayment:
        return 'Bill Payment';
      case TransactionCategory.bankTransfer:
        return 'Bank Transfer';
      case TransactionCategory.wallet:
        return 'Wallet';
    }
  }

  /// Helper method to get recharge status color
  static Color _getRechargeStatusColor(RechargeStatus status) {
    switch (status) {
      case RechargeStatus.pending:
        return Colors.amber;
      case RechargeStatus.processing:
        return Colors.blue;
      case RechargeStatus.completed:
        return Colors.green;
      case RechargeStatus.failed:
        return Colors.red;
      case RechargeStatus.cancelled:
        return Colors.grey;
    }
  }

  /// Helper method to get recharge status text
  static String _getRechargeStatusText(RechargeStatus status) {
    switch (status) {
      case RechargeStatus.pending:
        return 'Pending';
      case RechargeStatus.processing:
        return 'Processing';
      case RechargeStatus.completed:
        return 'Completed';
      case RechargeStatus.failed:
        return 'Failed';
      case RechargeStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Helper method to get bank transfer status color
  static Color _getBankTransferStatusColor(BankTransferStatus status) {
    switch (status) {
      case BankTransferStatus.pending:
        return Colors.amber;
      case BankTransferStatus.processing:
        return Colors.blue;
      case BankTransferStatus.completed:
        return Colors.green;
      case BankTransferStatus.failed:
        return Colors.red;
      case BankTransferStatus.cancelled:
        return Colors.grey;
    }
  }

  /// Helper method to get bank transfer status text
  static String _getBankTransferStatusText(BankTransferStatus status) {
    switch (status) {
      case BankTransferStatus.pending:
        return 'Pending';
      case BankTransferStatus.processing:
        return 'Processing';
      case BankTransferStatus.completed:
        return 'Completed';
      case BankTransferStatus.failed:
        return 'Failed';
      case BankTransferStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Helper method to get bill type color
  static Color _getBillTypeColor(BillType type) {
    switch (type) {
      case BillType.electricity:
        return Colors.amber;
      case BillType.water:
        return Colors.blue;
      case BillType.gas:
        return Colors.orange;
      case BillType.internet:
        return Colors.purple;
      case BillType.telephone:
        return Colors.green;
      case BillType.tv:
        return Colors.red;
      case BillType.education:
        return Colors.indigo;
      case BillType.tax:
        return Colors.brown;
      case BillType.other:
        return Colors.grey;
    }
  }
}

import 'package:flutter/material.dart';

/// Model representing a mobile network operator
class Operator {
  final String id;
  final String name;
  final String logoPath;
  final Color color;
  final String prefix;
  final bool isActive;

  const Operator({
    required this.id,
    required this.name,
    required this.logoPath,
    required this.color,
    required this.prefix,
    this.isActive = true,
  });

  /// Create Operator from JSON
  factory Operator.fromJson(Map<String, dynamic> json) {
    return Operator(
      id: json['id'] as String,
      name: json['name'] as String,
      logoPath: json['logo_path'] as String,
      color: Color(int.parse(json['color'] as String, radix: 16)),
      prefix: json['prefix'] as String,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// Convert Operator to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'logo_path': logoPath,
      'color': color.value.toRadixString(16),
      'prefix': prefix,
      'is_active': isActive,
    };
  }

  /// Check if a phone number belongs to this operator
  bool matchesPhoneNumber(String phoneNumber) {
    // Remove any spaces or special characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if the number starts with the operator prefix
    // For Bangladesh, we check if it starts with the operator code after '0' or '+880'
    if (cleanNumber.startsWith('0')) {
      return cleanNumber.substring(1).startsWith(prefix);
    } else if (cleanNumber.startsWith('+880')) {
      return cleanNumber.substring(4).startsWith(prefix);
    } else if (cleanNumber.startsWith('880')) {
      return cleanNumber.substring(3).startsWith(prefix);
    }
    
    return false;
  }
}

/// List of predefined operators in Bangladesh
class BangladeshOperators {
  // Grameenphone
  static const Operator grameenphone = Operator(
    id: 'gp',
    name: 'Grameenphone',
    logoPath: 'assets/images/operators/gp.png',
    color: Color(0xFF1976D2), // Blue
    prefix: '17',
  );

  // Robi
  static const Operator robi = Operator(
    id: 'robi',
    name: 'Robi',
    logoPath: 'assets/images/operators/robi.png',
    color: Color(0xFFE53935), // Red
    prefix: '18',
  );

  // Airtel
  static const Operator airtel = Operator(
    id: 'airtel',
    name: 'Airtel',
    logoPath: 'assets/images/operators/airtel.png',
    color: Color(0xFFE53935), // Red
    prefix: '16',
  );

  // Banglalink
  static const Operator banglalink = Operator(
    id: 'banglalink',
    name: 'Banglalink',
    logoPath: 'assets/images/operators/banglalink.png',
    color: Color(0xFFFFA000), // Orange
    prefix: '19',
  );

  // Teletalk
  static const Operator teletalk = Operator(
    id: 'teletalk',
    name: 'Teletalk',
    logoPath: 'assets/images/operators/teletalk.png',
    color: Color(0xFF4CAF50), // Green
    prefix: '15',
  );

  // Get all operators
  static List<Operator> getAll() {
    return [
      grameenphone,
      robi,
      airtel,
      banglalink,
      teletalk,
    ];
  }

  // Detect operator from phone number
  static Operator? detectFromPhoneNumber(String phoneNumber) {
    for (final operator in getAll()) {
      if (operator.matchesPhoneNumber(phoneNumber)) {
        return operator;
      }
    }
    return null;
  }
}

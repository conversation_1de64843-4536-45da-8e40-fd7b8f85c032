import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/data/models/mobile_operator_model.dart';
import 'package:irecharge_pro/data/models/offer_package_model.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Offers screen
class OffersScreen extends ConsumerStatefulWidget {
  const OffersScreen({super.key});

  @override
  ConsumerState<OffersScreen> createState() => _OffersScreenState();
}

class _OffersScreenState extends ConsumerState<OffersScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();

  // Selected operator
  MobileOperator? _selectedOperator;

  // Selected package
  OfferPackage? _selectedPackage;

  // Loading states
  bool _isLoading = false;
  bool _isSearching = false;

  // Step in the flow
  int _currentStep = 0;

  // Available packages
  List<OfferPackage> _availablePackages = [];

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  /// Get operator from phone number
  MobileOperator? _getOperatorFromPhone(String phone) {
    if (phone.isEmpty || phone.length < 3) {
      return null;
    }

    final prefix = phone.substring(0, 3);

    // Check operator based on prefix
    if (['017', '013'].contains(prefix)) {
      return MobileOperator.getOperator('gp');
    } else if (['018', '016'].contains(prefix)) {
      return MobileOperator.getOperator('robi');
    } else if (['019', '014'].contains(prefix)) {
      return MobileOperator.getOperator('banglalink');
    } else if (['015'].contains(prefix)) {
      return MobileOperator.getOperator('teletalk');
    } else if (['011'].contains(prefix)) {
      return MobileOperator.getOperator('airtel');
    }

    return null;
  }

  /// Handle phone number change
  void _onPhoneChanged(String value) {
    if (value.length >= 3) {
      final operator = _getOperatorFromPhone(value);
      if (operator != null && _selectedOperator?.id != operator.id) {
        setState(() {
          _selectedOperator = operator;
        });
      }
    } else if (_selectedOperator != null) {
      setState(() {
        _selectedOperator = null;
      });
    }
  }

  /// Handle operator selection
  void _selectOperator(MobileOperator operator) {
    setState(() {
      _selectedOperator = operator;
    });
  }

  /// Search for offers
  void _searchOffers() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedOperator == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an operator'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isSearching = false;
        _currentStep = 1;
        _availablePackages = OfferPackage.getPackagesForOperator(_selectedOperator!.id);
      });
    });
  }

  /// Select a package
  void _selectPackage(OfferPackage package) {
    setState(() {
      _selectedPackage = package;
      _currentStep = 2;
    });
  }

  /// Activate package
  void _activatePackage() {
    if (_selectedPackage == null || _selectedOperator == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show success dialog
      _showSuccessDialog();
    });
  }

  /// Show success dialog
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
            const SizedBox(width: 8),
            const Text('Success'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your package has been activated successfully.'),
            const SizedBox(height: 16),
            _buildDetailRow('Phone', _phoneController.text),
            _buildDetailRow('Operator', _selectedOperator!.name),
            _buildDetailRow('Package', _selectedPackage!.title),
            _buildDetailRow('Price', _selectedPackage!.formattedPrice),
            if (_selectedPackage!.cashbackAmount != null)
              _buildDetailRow('Cashback', _selectedPackage!.formattedCashback!),
            _buildDetailRow('Validity', _selectedPackage!.formattedValidity),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset the flow
              setState(() {
                _currentStep = 0;
                _selectedPackage = null;
                _availablePackages = [];
              });
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('offers')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          if (_currentStep > 0)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                setState(() {
                  _currentStep = 0;
                  _selectedPackage = null;
                  _availablePackages = [];
                });
              },
              tooltip: 'Start Over',
            ),
        ],
      ),
      body: _currentStep == 0
          ? _buildStep1()
          : _currentStep == 1
              ? _buildStep2()
              : _buildStep3(),
    );
  }

  /// Build step 1: Enter phone number and select operator
  Widget _buildStep1() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'Find Offers',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Enter your phone number and select your operator to find available offers',
              style: TextStyle(
                color: ColorConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 24),

            // Phone number input
            CustomTextField(
              controller: _phoneController,
              labelText: 'Phone Number',
              hintText: 'Enter phone number',
              keyboardType: TextInputType.phone,
              prefixIcon: const Icon(Icons.phone_android),
              validator: Validators.validateBangladeshPhone,
              onChanged: _onPhoneChanged,
            ),

            const SizedBox(height: 24),

            // Operator selection
            const Text(
              'Select Operator',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Operator grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: MobileOperator.getAllOperators().map((operator) {
                final isSelected = _selectedOperator?.id == operator.id;
                return InkWell(
                  onTap: () => _selectOperator(operator),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? operator.color.withAlpha(30)
                          : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? operator.color : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: Colors.white,
                          child: Icon(
                            Icons.sim_card,
                            color: operator.color,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          operator.name,
                          style: TextStyle(
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 24),

            // Search button
            CustomButton(
              text: 'Find Offers',
              onPressed: _searchOffers,
              isLoading: _isSearching,
            ),
          ],
        ),
      ),
    );
  }

  /// Build step 2: Select package
  Widget _buildStep2() {
    return Column(
      children: [
        // Selected phone and operator
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade100,
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: _selectedOperator!.color.withAlpha(30),
                child: Icon(
                  Icons.sim_card,
                  color: _selectedOperator!.color,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedOperator!.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _phoneController.text,
                      style: const TextStyle(
                        color: ColorConstants.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _currentStep = 0;
                  });
                },
                icon: const Icon(Icons.edit),
                label: const Text('Change'),
              ),
            ],
          ),
        ),

        // Available packages
        Expanded(
          child: _availablePackages.isEmpty
              ? const Center(
                  child: Text('No offers available for this operator'),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _availablePackages.length,
                  itemBuilder: (context, index) {
                    final package = _availablePackages[index];
                    return _buildPackageCard(package);
                  },
                ),
        ),
      ],
    );
  }

  /// Build step 3: Confirm and activate
  Widget _buildStep3() {
    if (_selectedPackage == null) {
      return const Center(
        child: Text('No package selected'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'Confirm Package',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please review and confirm your package selection',
            style: TextStyle(
              color: ColorConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 24),

          // Package details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Package title and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          _selectedPackage!.title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: _selectedOperator?.color ?? ColorConstants.primaryColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          _selectedPackage!.formattedPrice,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Package description
                  Text(
                    _selectedPackage!.description,
                    style: const TextStyle(
                      color: ColorConstants.textSecondaryColor,
                    ),
                  ),

                  // Cashback description
                  if (_selectedPackage!.cashbackDescription != null) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.savings,
                          size: 16,
                          color: Colors.green,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _selectedPackage!.cashbackDescription!,
                          style: const TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],

                  const SizedBox(height: 16),

                  // Package benefits
                  const Text(
                    'Benefits:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Data
                  if (_selectedPackage!.benefits['data'] != null)
                    _buildBenefitRow(
                      icon: Icons.data_usage,
                      label: 'Data',
                      value: _selectedPackage!.benefits['data']!,
                      color: ColorConstants.primaryColor,
                    ),

                  // Minutes
                  if (_selectedPackage!.benefits['minutes'] != null)
                    _buildBenefitRow(
                      icon: Icons.call,
                      label: 'Minutes',
                      value: _selectedPackage!.benefits['minutes']!,
                      color: ColorConstants.secondaryColor,
                    ),

                  // SMS
                  if (_selectedPackage!.benefits['sms'] != null)
                    _buildBenefitRow(
                      icon: Icons.message,
                      label: 'SMS',
                      value: _selectedPackage!.benefits['sms']!,
                      color: ColorConstants.accentColor,
                    ),

                  const SizedBox(height: 16),

                  // Validity
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Validity: ${_selectedPackage!.formattedValidity}',
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),

                  // Activation code
                  if (_selectedPackage!.activationCode != null) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.dialpad,
                          size: 16,
                          color: ColorConstants.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Dial: ${_selectedPackage!.activationCode}',
                          style: const TextStyle(
                            color: ColorConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Phone number confirmation
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Recipient',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: _selectedOperator!.color.withAlpha(30),
                        child: Icon(
                          Icons.sim_card,
                          color: _selectedOperator!.color,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedOperator!.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _phoneController.text,
                              style: const TextStyle(
                                color: ColorConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _currentStep = 0;
                          });
                        },
                        icon: const Icon(Icons.edit),
                        label: const Text('Change'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Payment method
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Payment Method',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: ColorConstants.primaryColor.withAlpha(30),
                        child: const Icon(
                          Icons.account_balance_wallet,
                          color: ColorConstants.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Wallet Balance',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '৳ 5,000.00',
                              style: TextStyle(
                                color: ColorConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Activate button
          CustomButton(
            text: 'Activate Package',
            onPressed: _activatePackage,
            isLoading: _isLoading,
          ),

          const SizedBox(height: 16),

          // Back button
          OutlinedButton(
            onPressed: () {
              setState(() {
                _currentStep = 1;
                _selectedPackage = null;
              });
            },
            style: OutlinedButton.styleFrom(
              minimumSize: const Size(double.infinity, 48),
            ),
            child: const Text('Back to Offers'),
          ),
        ],
      ),
    );
  }

  /// Build a package card
  Widget _buildPackageCard(OfferPackage package) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: Stack(
        children: [
          // Cashback badge
          if (package.cashbackAmount != null)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: const BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Text(
                  'Cashback ${package.formattedCashback}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Package title and price
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        package.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _selectedOperator?.color ?? ColorConstants.primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        package.formattedPrice,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Package description
                Text(
                  package.description,
                  style: const TextStyle(
                    color: ColorConstants.textSecondaryColor,
                  ),
                ),

                // Cashback description
                if (package.cashbackDescription != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.savings,
                        size: 16,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        package.cashbackDescription!,
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 16),

                // Package benefits
                Row(
                  children: [
                    if (package.benefits['data'] != null)
                      _buildBenefitChip(
                        icon: Icons.data_usage,
                        label: package.benefits['data'] ?? '-',
                        color: ColorConstants.primaryColor,
                      ),
                    if (package.benefits['minutes'] != null) ...[
                      const SizedBox(width: 8),
                      _buildBenefitChip(
                        icon: Icons.call,
                        label: package.benefits['minutes'] ?? '-',
                        color: ColorConstants.secondaryColor,
                      ),
                    ],
                    if (package.benefits['sms'] != null) ...[
                      const SizedBox(width: 8),
                      _buildBenefitChip(
                        icon: Icons.message,
                        label: package.benefits['sms'] ?? '-',
                        color: ColorConstants.accentColor,
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),

                // Validity and activation
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: ColorConstants.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Validity: ${package.formattedValidity}',
                          style: const TextStyle(
                            color: ColorConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    if (package.activationCode != null) ...[
                      Row(
                        children: [
                          const Icon(
                            Icons.dialpad,
                            size: 16,
                            color: ColorConstants.textSecondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Dial: ${package.activationCode}',
                            style: const TextStyle(
                              color: ColorConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),

                // Select button
                CustomButton(
                  text: 'Select Package',
                  onPressed: () => _selectPackage(package),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a benefit chip
  Widget _buildBenefitChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        color: Color.fromRGBO(
          color.red & 0xFF,
          color.green & 0xFF,
          color.blue & 0xFF,
          0.1,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a benefit row
  Widget _buildBenefitRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

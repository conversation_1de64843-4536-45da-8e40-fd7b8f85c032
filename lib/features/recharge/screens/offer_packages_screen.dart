import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/error_widget.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/offer_package_model.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Screen for viewing and selecting offer packages
class OfferPackagesScreen extends ConsumerStatefulWidget {
  const OfferPackagesScreen({super.key});

  @override
  ConsumerState<OfferPackagesScreen> createState() => _OfferPackagesScreenState();
}

class _OfferPackagesScreenState extends ConsumerState<OfferPackagesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Handle package selection
  void _selectPackage(OfferPackage package) {
    ref.read(selectedOfferPackageProvider.notifier).state = package;

    // Navigate to confirmation screen
    context.push(RouteNames.rechargeDetails);
  }

  @override
  Widget build(BuildContext context) {
    final selectedOperator = ref.watch(selectedOperatorProvider);

    if (selectedOperator == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Offer Packages'),
        ),
        body: const Center(
          child: Text('No operator selected. Please select an operator first.'),
        ),
      );
    }

    final packagesAsyncValue = ref.watch(offerPackagesByOperatorProvider(selectedOperator.id));

    return Scaffold(
      appBar: AppBar(
        title: Text('${selectedOperator.name} Packages'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Data'),
            Tab(text: 'Minutes'),
            Tab(text: 'Combo'),
          ],
        ),
      ),
      body: packagesAsyncValue.when(
        data: (packages) {
          if (packages.isEmpty) {
            return const Center(
              child: Text('No packages available for this operator.'),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              // All packages
              _buildPackageList(packages),

              // Data packages
              _buildPackageList(packages.where(
                (pkg) => pkg.type == PackageType.data
              ).toList()),

              // Minutes packages
              _buildPackageList(packages.where(
                (pkg) => pkg.type == PackageType.minutes
              ).toList()),

              // Combo packages
              _buildPackageList(packages.where(
                (pkg) => pkg.type == PackageType.combo
              ).toList()),
            ],
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => ErrorDisplayWidget(
          title: 'Error',
          message: 'Failed to load packages',
          buttonText: 'Retry',
          onRetry: () => ref.refresh(offerPackagesByOperatorProvider(selectedOperator.id)),
        ),
      ),
    );
  }

  /// Build a list of packages
  Widget _buildPackageList(List<OfferPackage> packages) {
    if (packages.isEmpty) {
      return const Center(
        child: Text('No packages available in this category.'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: packages.length,
      itemBuilder: (context, index) {
        final package = packages[index];
        return _buildPackageCard(package);
      },
    );
  }

  /// Build a package card
  Widget _buildPackageCard(OfferPackage package) {
    final operator = package.operator;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: Stack(
        children: [
          // Cashback badge
          if (package.cashbackAmount != null)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: const BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Text(
                  'Cashback ${package.formattedCashback}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Package title and price
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        package.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: operator?.color ?? ColorConstants.primaryColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        package.formattedPrice,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Package description
                Text(
                  package.description,
                  style: const TextStyle(
                    color: ColorConstants.textSecondaryColor,
                  ),
                ),

                // Cashback description
                if (package.cashbackDescription != null) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(
                        Icons.savings,
                        size: 16,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        package.cashbackDescription!,
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 16),

                // Package benefits
                Row(
                  children: [
                    _buildBenefitChip(
                      icon: Icons.data_usage,
                      label: package.benefits['data'] ?? '-',
                      color: ColorConstants.primaryColor,
                    ),
                    if (package.benefits['minutes'] != null) ...[
                      const SizedBox(width: 8),
                      _buildBenefitChip(
                        icon: Icons.call,
                        label: package.benefits['minutes'] ?? '-',
                        color: ColorConstants.secondaryColor,
                      ),
                    ],
                    if (package.benefits['sms'] != null) ...[
                      const SizedBox(width: 8),
                      _buildBenefitChip(
                        icon: Icons.message,
                        label: package.benefits['sms'] ?? '-',
                        color: ColorConstants.accentColor,
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),

                // Validity and activation
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: ColorConstants.textSecondaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Validity: ${package.formattedValidity}',
                          style: const TextStyle(
                            color: ColorConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    if (package.activationCode != null) ...[
                      Row(
                        children: [
                          const Icon(
                            Icons.dialpad,
                            size: 16,
                            color: ColorConstants.textSecondaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Dial: ${package.activationCode}',
                            style: const TextStyle(
                              color: ColorConstants.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 16),

                // Activate button
                CustomButton(
                  text: 'Activate Package',
                  onPressed: () => _selectPackage(package),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build a benefit chip
  Widget _buildBenefitChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        color: Color.fromRGBO(
          color.red & 0xFF,
          color.green & 0xFF,
          color.blue & 0xFF,
          0.1,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

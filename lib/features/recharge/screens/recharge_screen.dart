import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/core/widgets/error_widget.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/operator_model.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Screen for mobile recharge
class RechargeScreen extends ConsumerStatefulWidget {
  const RechargeScreen({super.key});

  @override
  ConsumerState<RechargeScreen> createState() => _RechargeScreenState();
}

class _RechargeScreenState extends ConsumerState<RechargeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Reset the selected operator when the screen is loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(selectedOperatorProvider.notifier).state = null;
      ref.read(phoneNumberProvider.notifier).state = '';
      ref.read(rechargeAmountProvider.notifier).state = 0;
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  /// Handle operator selection
  void _selectOperator(Operator operator) {
    ref.read(selectedOperatorProvider.notifier).state = operator;
  }

  /// Handle phone number change
  void _onPhoneNumberChanged(String value) {
    ref.read(phoneNumberProvider.notifier).state = value;

    // Auto-detect operator
    if (value.length >= 11) {
      final operator = BangladeshOperators.detectFromPhoneNumber(value);
      if (operator != null) {
        ref.read(selectedOperatorProvider.notifier).state = operator;
      }
    }
  }

  /// Handle contact selection
  Future<void> _selectContact() async {
    // This is a mock implementation since we can't access real contacts in this demo
    // In a real app, you would use a contacts picker plugin

    // Show a mock contact selection dialog
    final result = await showDialog<String>(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('Select Contact'),
        children: [
          for (final contact in _mockContacts)
            SimpleDialogOption(
              onPressed: () {
                Navigator.pop(context, contact['phone']);
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    CircleAvatar(
                      child: Text(contact['name']![0]),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contact['name']!,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(contact['phone']!),
                      ],
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );

    if (result != null) {
      _phoneController.text = result;
      _onPhoneNumberChanged(result);
    }
  }

  // Mock contacts for demo
  final List<Map<String, String>> _mockContacts = [
    {'name': 'John Doe', 'phone': '01712345678'},
    {'name': 'Jane Smith', 'phone': '01812345678'},
    {'name': 'Alice Johnson', 'phone': '01912345678'},
    {'name': 'Bob Williams', 'phone': '01612345678'},
    {'name': 'Charlie Brown', 'phone': '01512345678'},
  ];

  /// Handle amount change
  void _onAmountChanged(String value) {
    final amount = double.tryParse(value) ?? 0;
    ref.read(rechargeAmountProvider.notifier).state = amount;
  }

  /// Handle recharge button press
  void _handleRecharge() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final operator = ref.read(selectedOperatorProvider);
    if (operator == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an operator'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    final phoneNumber = _phoneController.text;
    final amount = double.tryParse(_amountController.text) ?? 0;

    if (amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid amount'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Successfully recharged ৳$amount to $phoneNumber'),
          backgroundColor: Colors.green,
        ),
      );

      // Update providers
      ref.read(phoneNumberProvider.notifier).state = phoneNumber;
      ref.read(rechargeAmountProvider.notifier).state = amount;

      // Navigate to confirmation screen
      context.push(RouteNames.rechargeDetails);
    });
  }

  /// Handle offer packages button press
  void _handleOfferPackages() {
    final operator = ref.read(selectedOperatorProvider);
    if (operator == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an operator first'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    // Navigate to offer packages screen
    context.push(RouteNames.offerPackages);
  }

  @override
  Widget build(BuildContext context) {
    final selectedOperator = ref.watch(selectedOperatorProvider);
    final operators = ref.watch(operatorsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mobile Recharge'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              context.go(RouteNames.history);
              // Navigate to the Recharge tab in the history screen
              // This will be handled by the history screen's tab controller
            },
            tooltip: 'Recharge History',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Phone number input with contact selection
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _phoneController,
                      labelText: 'Phone Number',
                      hintText: 'Enter phone number',
                      keyboardType: TextInputType.phone,
                      prefixIcon: const Icon(Icons.phone_android),
                      validator: Validators.validateBangladeshPhone,
                      onChanged: _onPhoneNumberChanged,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    height: 56,
                    width: 56,
                    margin: const EdgeInsets.only(top: 4),
                    child: ElevatedButton(
                      onPressed: _selectContact,
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Icon(Icons.contacts),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Operator selection
              const Text(
                'Select Operator',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              operators.when(
                data: (operatorList) {
                  return GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5,
                      childAspectRatio: 0.8,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                    ),
                    itemCount: operatorList.length,
                    itemBuilder: (context, index) {
                      final operator = operatorList[index];
                      final isSelected = selectedOperator?.id == operator.id;

                      return _buildOperatorItem(
                        operator: operator,
                        isSelected: isSelected,
                        onTap: () => _selectOperator(operator),
                      );
                    },
                  );
                },
                loading: () => const Center(child: LoadingIndicator()),
                error: (error, stackTrace) => ErrorDisplayWidget(
                  title: 'Error',
                  message: 'Failed to load operators',
                  buttonText: 'Retry',
                  onRetry: () => ref.refresh(operatorsProvider),
                ),
              ),

              const SizedBox(height: 16),

              // Offer packages button
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: ColorConstants.primaryColor.withAlpha(20),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: ColorConstants.primaryColor.withAlpha(50),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.local_offer,
                          color: ColorConstants.primaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Get the best deals with our special offers',
                          style: TextStyle(
                            color: ColorConstants.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomButton(
                      text: 'View Offer Packages',
                      onPressed: _handleOfferPackages,
                      isOutlined: true,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Quick amounts
              const Text(
                'Quick Amounts',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [10, 20, 50, 100, 200, 500].map((amount) {
                  return ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _amountController.text = amount.toString();
                        _onAmountChanged(amount.toString());
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: ColorConstants.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('৳$amount'),
                  );
                }).toList(),
              ),

              const SizedBox(height: 16),

              // Amount input
              CustomTextField(
                controller: _amountController,
                labelText: 'Amount',
                hintText: 'Enter amount',
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(Icons.attach_money),
                validator: Validators.validateAmount,
                onChanged: _onAmountChanged,
              ),

              const SizedBox(height: 24),

              // Recharge button
              CustomButton(
                text: 'Recharge Now',
                onPressed: _handleRecharge,
                isLoading: _isLoading,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build operator selection item
  Widget _buildOperatorItem({
    required Operator operator,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? Color.fromRGBO(
                  operator.color.red & 0xFF,
                  operator.color.green & 0xFF,
                  operator.color.blue & 0xFF,
                  0.1,
                )
              : Colors.grey.withAlpha(13),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? operator.color : Colors.grey.withAlpha(51),
            width: isSelected ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.all(6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Operator logo
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: ClipOval(
                child: Image.asset(
                  operator.logoPath,
                  width: 36,
                  height: 36,
                  errorBuilder: (context, error, stackTrace) {
                    return CircleAvatar(
                      backgroundColor: Color.fromRGBO(
                        operator.color.red & 0xFF,
                        operator.color.green & 0xFF,
                        operator.color.blue & 0xFF,
                        0.2,
                      ),
                      child: Icon(
                        Icons.sim_card,
                        color: operator.color,
                        size: 18,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 4),
            // Operator name
            Text(
              operator.name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? operator.color : Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/widgets/error_widget.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Screen for viewing recharge history
class RechargeHistoryScreen extends ConsumerWidget {
  const RechargeHistoryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsyncValue = ref.watch(rechargeTransactionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Recharge History'),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
      ),
      body: transactionsAsyncValue.when(
        data: (transactions) {
          if (transactions.isEmpty) {
            return const Center(
              child: Text('No recharge history found.'),
            );
          }

          // Sort transactions by timestamp (newest first)
          transactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              final transaction = transactions[index];
              return _buildTransactionCard(context, transaction);
            },
          );
        },
        loading: () => const Center(child: LoadingIndicator()),
        error: (error, stackTrace) => ErrorDisplayWidget(
          title: 'Error',
          message: 'Failed to load recharge history',
          buttonText: 'Retry',
          onRetry: () => ref.refresh(rechargeTransactionsProvider),
        ),
      ),
    );
  }

  /// Build a transaction card
  Widget _buildTransactionCard(BuildContext context, RechargeTransaction transaction) {
    final operator = transaction.operator;
    final package = transaction.package;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          context.push('${RouteNames.rechargeDetails}/${transaction.id}');
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction header
              Row(
                children: [
                  // Operator logo
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: operator?.color.withOpacity(0.1) ?? Colors.grey.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: operator != null
                          ? Image.asset(
                              operator.logoPath,
                              width: 30,
                              height: 30,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.sim_card,
                                  color: operator.color,
                                  size: 20,
                                );
                              },
                            )
                          : const Icon(
                              Icons.sim_card,
                              color: Colors.grey,
                              size: 20,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Transaction info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          operator?.name ?? 'Unknown Operator',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          transaction.phoneNumber,
                          style: const TextStyle(
                            color: ColorConstants.textSecondaryColor,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Transaction amount
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        transaction.formattedAmount,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Color(transaction.getStatusColor()).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          transaction.formattedStatus,
                          style: TextStyle(
                            color: Color(transaction.getStatusColor()),
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              if (package != null) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                // Package info
                Row(
                  children: [
                    const Icon(
                      Icons.local_offer,
                      size: 16,
                      color: ColorConstants.textSecondaryColor,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        package.title,
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),

              // Transaction date and payment method
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${transaction.formattedDate} ${transaction.formattedTime}',
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Icon(
                        Icons.payment,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        transaction.formattedPaymentMethod,
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

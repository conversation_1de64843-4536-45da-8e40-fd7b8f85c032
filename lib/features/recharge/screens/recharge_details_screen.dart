import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/error_widget.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';

/// Screen for viewing recharge transaction details
class RechargeDetailsScreen extends ConsumerWidget {
  final String? transactionId;

  const RechargeDetailsScreen({
    super.key,
    this.transactionId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // If transactionId is provided, fetch the transaction
    // Otherwise, use the confirmation screen
    if (transactionId != null) {
      final transactionAsyncValue = ref.watch(rechargeTransactionByIdProvider(transactionId!));

      return Scaffold(
        appBar: AppBar(
          title: const Text('Recharge Details'),
          actions: [
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                // TODO: Implement share functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Share functionality coming soon'),
                  ),
                );
              },
              tooltip: 'Share',
            ),
          ],
        ),
        body: transactionAsyncValue.when(
          data: (transaction) {
            if (transaction == null) {
              return const Center(
                child: Text('Transaction not found.'),
              );
            }

            return _buildTransactionDetails(context, transaction);
          },
          loading: () => const Center(child: LoadingIndicator()),
          error: (error, stackTrace) => ErrorDisplayWidget(
            title: 'Error',
            message: 'Failed to load transaction details',
            buttonText: 'Retry',
            onRetry: () => ref.refresh(rechargeTransactionByIdProvider(transactionId!)),
          ),
        ),
      );
    } else {
      // Use the confirmation screen for new recharges
      return const RechargeConfirmationScreen();
    }
  }

  /// Build transaction details view
  Widget _buildTransactionDetails(BuildContext context, RechargeTransaction transaction) {
    final operator = transaction.operator;
    final package = transaction.package;
    final isSuccessful = transaction.status == RechargeStatus.completed;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status card
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            color: Color(transaction.getStatusColor()).withOpacity(0.1),
            elevation: 0,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    isSuccessful ? Icons.check_circle : Icons.error,
                    color: Color(transaction.getStatusColor()),
                    size: 40,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          isSuccessful ? 'Recharge Successful' : 'Recharge ${transaction.formattedStatus}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(transaction.getStatusColor()),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          isSuccessful
                              ? 'Your recharge has been processed successfully'
                              : transaction.failureReason ?? 'There was an issue with your recharge',
                          style: TextStyle(
                            color: Color(transaction.getStatusColor()).withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Transaction details card
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Transaction Details',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Transaction ID
                  _buildDetailRow(
                    label: 'Transaction ID',
                    value: transaction.id,
                    icon: Icons.receipt,
                    showCopy: true,
                    onCopy: () => _copyToClipboard(context, transaction.id),
                  ),
                  const Divider(),
                  
                  // Date and time
                  _buildDetailRow(
                    label: 'Date & Time',
                    value: '${transaction.formattedDate} ${transaction.formattedTime}',
                    icon: Icons.calendar_today,
                  ),
                  const Divider(),
                  
                  // Operator
                  _buildDetailRow(
                    label: 'Operator',
                    value: operator?.name ?? 'Unknown Operator',
                    icon: Icons.sim_card,
                    iconColor: operator?.color,
                  ),
                  const Divider(),
                  
                  // Phone number
                  _buildDetailRow(
                    label: 'Phone Number',
                    value: transaction.phoneNumber,
                    icon: Icons.phone_android,
                  ),
                  const Divider(),
                  
                  // Amount
                  _buildDetailRow(
                    label: 'Amount',
                    value: transaction.formattedAmount,
                    icon: Icons.attach_money,
                  ),
                  
                  if (package != null) ...[
                    const Divider(),
                    // Package
                    _buildDetailRow(
                      label: 'Package',
                      value: package.title,
                      icon: Icons.local_offer,
                    ),
                  ],
                  
                  const Divider(),
                  
                  // Payment method
                  _buildDetailRow(
                    label: 'Payment Method',
                    value: transaction.formattedPaymentMethod,
                    icon: Icons.payment,
                  ),
                  
                  if (transaction.transactionReference != null) ...[
                    const Divider(),
                    // Reference
                    _buildDetailRow(
                      label: 'Reference',
                      value: transaction.transactionReference!,
                      icon: Icons.numbers,
                      showCopy: true,
                      onCopy: () => _copyToClipboard(context, transaction.transactionReference!),
                    ),
                  ],
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Action buttons
          if (isSuccessful) ...[
            CustomButton(
              text: 'Recharge Again',
              onPressed: () => context.go('/recharge'),
            ),
          ] else ...[
            CustomButton(
              text: 'Try Again',
              onPressed: () => context.go('/recharge'),
            ),
          ],
          
          const SizedBox(height: 16),
          
          CustomButton(
            text: 'Contact Support',
            onPressed: () {
              // TODO: Implement contact support
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Support functionality coming soon'),
                ),
              );
            },
            isOutlined: true,
          ),
        ],
      ),
    );
  }

  /// Build a detail row
  Widget _buildDetailRow({
    required String label,
    required String value,
    required IconData icon,
    Color? iconColor,
    bool showCopy = false,
    VoidCallback? onCopy,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: iconColor ?? ColorConstants.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: ColorConstants.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          if (showCopy && onCopy != null)
            IconButton(
              icon: const Icon(
                Icons.copy,
                size: 16,
                color: ColorConstants.primaryColor,
              ),
              onPressed: onCopy,
              tooltip: 'Copy to clipboard',
              constraints: const BoxConstraints(),
              padding: const EdgeInsets.all(8),
            ),
        ],
      ),
    );
  }

  /// Copy text to clipboard
  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Copied to clipboard'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}

/// Recharge confirmation screen
class RechargeConfirmationScreen extends ConsumerWidget {
  const RechargeConfirmationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // This is a placeholder to redirect to the actual confirmation screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.go('/recharge/confirmation');
    });
    
    return const Scaffold(
      body: Center(child: LoadingIndicator()),
    );
  }
}

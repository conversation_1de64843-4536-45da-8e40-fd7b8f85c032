import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Screen for confirming a mobile recharge
class RechargeConfirmationScreen extends ConsumerStatefulWidget {
  const RechargeConfirmationScreen({super.key});

  @override
  ConsumerState<RechargeConfirmationScreen> createState() => _RechargeConfirmationScreenState();
}

class _RechargeConfirmationScreenState extends ConsumerState<RechargeConfirmationScreen> {
  bool _isProcessing = false;
  RechargeTransaction? _transaction;

  @override
  Widget build(BuildContext context) {
    final selectedOperator = ref.watch(selectedOperatorProvider);
    final phoneNumber = ref.watch(phoneNumberProvider);
    final amount = ref.watch(rechargeAmountProvider);
    final selectedPackage = ref.watch(selectedOfferPackageProvider);
    final paymentMethod = ref.watch(selectedPaymentMethodProvider);

    // If no operator is selected, go back to recharge screen
    if (selectedOperator == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.go(RouteNames.recharge);
      });
      return const Scaffold(
        body: Center(child: LoadingIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Confirm Recharge'),
      ),
      body: _isProcessing
          ? _buildProcessingView()
          : _transaction != null
              ? _buildSuccessView()
              : _buildConfirmationView(
                  operator: selectedOperator,
                  phoneNumber: phoneNumber,
                  amount: amount,
                  package: selectedPackage,
                  paymentMethod: paymentMethod,
                ),
    );
  }

  /// Build the confirmation view
  Widget _buildConfirmationView({
    required operator,
    required String phoneNumber,
    required double amount,
    required package,
    required PaymentMethod paymentMethod,
  }) {
    final displayAmount = package != null ? package.price : amount;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Confirmation card
          Card(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Recharge Details',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    label: 'Operator',
                    value: operator.name,
                    icon: Icons.sim_card,
                    iconColor: operator.color,
                  ),
                  const Divider(),
                  _buildDetailRow(
                    label: 'Phone Number',
                    value: phoneNumber,
                    icon: Icons.phone_android,
                  ),
                  const Divider(),
                  _buildDetailRow(
                    label: 'Amount',
                    value: '৳${displayAmount.toStringAsFixed(2)}',
                    icon: Icons.attach_money,
                  ),
                  if (package != null) ...[
                    const Divider(),
                    _buildDetailRow(
                      label: 'Package',
                      value: package.title,
                      icon: Icons.local_offer,
                    ),
                  ],
                  const Divider(),
                  _buildDetailRow(
                    label: 'Payment Method',
                    value: _getPaymentMethodName(paymentMethod),
                    icon: _getPaymentMethodIcon(paymentMethod),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Warning text
          const Text(
            'Please verify the details before confirming the recharge. Once confirmed, the recharge cannot be cancelled.',
            style: TextStyle(
              color: ColorConstants.warningColor,
              fontStyle: FontStyle.italic,
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Confirm button
          CustomButton(
            text: 'Confirm Recharge',
            onPressed: _handleConfirmRecharge,
          ),
          
          const SizedBox(height: 16),
          
          // Cancel button
          CustomButton(
            text: 'Cancel',
            onPressed: () => context.pop(),
            isOutlined: true,
          ),
        ],
      ),
    );
  }

  /// Build the processing view
  Widget _buildProcessingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const LoadingIndicator(
            size: 60,
            useAnimation: true,
          ),
          const SizedBox(height: 24),
          const Text(
            'Processing Recharge',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Please wait while we process your recharge...',
            style: TextStyle(
              color: ColorConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build the success view
  Widget _buildSuccessView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.check_circle,
            color: ColorConstants.successColor,
            size: 80,
          ),
          const SizedBox(height: 24),
          const Text(
            'Recharge Successful',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your recharge of ${_transaction?.formattedAmount} has been processed successfully.',
            style: const TextStyle(
              color: ColorConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: CustomButton(
              text: 'View Receipt',
              onPressed: () {
                if (_transaction != null) {
                  context.push('${RouteNames.rechargeDetails}/${_transaction!.id}');
                } else {
                  context.go(RouteNames.home);
                }
              },
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: CustomButton(
              text: 'Back to Home',
              onPressed: () => context.go(RouteNames.home),
              isOutlined: true,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a detail row
  Widget _buildDetailRow({
    required String label,
    required String value,
    required IconData icon,
    Color? iconColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: iconColor ?? ColorConstants.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: ColorConstants.textSecondaryColor,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get payment method name
  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.card:
        return 'Credit/Debit Card';
      case PaymentMethod.mobileBanking:
        return 'Mobile Banking';
      case PaymentMethod.internetBanking:
        return 'Internet Banking';
    }
  }

  /// Get payment method icon
  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.wallet:
        return Icons.account_balance_wallet;
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.mobileBanking:
        return Icons.phone_android;
      case PaymentMethod.internetBanking:
        return Icons.account_balance;
    }
  }

  /// Handle confirm recharge button press
  Future<void> _handleConfirmRecharge() async {
    final selectedOperator = ref.read(selectedOperatorProvider);
    final phoneNumber = ref.read(phoneNumberProvider);
    final amount = ref.read(rechargeAmountProvider);
    final selectedPackage = ref.read(selectedOfferPackageProvider);
    final paymentMethod = ref.read(selectedPaymentMethodProvider);

    if (selectedOperator == null || phoneNumber.isEmpty || amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Invalid recharge details'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // Simulate processing delay
      await Future.delayed(const Duration(seconds: 2));

      final repository = ref.read(rechargeRepositoryProvider);
      
      // Perform recharge
      final transaction = await repository.performRecharge(
        operatorId: selectedOperator.id,
        phoneNumber: phoneNumber,
        amount: selectedPackage?.price ?? amount,
        paymentMethod: paymentMethod,
        packageId: selectedPackage?.id,
      );

      if (mounted) {
        setState(() {
          _isProcessing = false;
          _transaction = transaction;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Recharge failed: ${e.toString()}'),
            backgroundColor: ColorConstants.errorColor,
          ),
        );
      }
    }
  }
}

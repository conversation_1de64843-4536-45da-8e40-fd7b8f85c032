import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/features/profile/screens/edit_profile_screen.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Profile screen
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  // Mock user data
  Map<String, dynamic> _userData = {
    'name': '<PERSON>',
    'email': '<EMAIL>',
    'phone': '+880 **********',
    'address': 'Dhaka, Bangladesh',
    'profileImage': null, // No image for now
    'memberSince': '2023-05-15',
    'walletBalance': 5000.0,
    'totalTransactions': 42,
  };

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('profile')),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              context.push(RouteNames.settings);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Profile header
            _buildProfileHeader(),

            const SizedBox(height: 16),

            // Profile menu items
            _buildProfileMenu(context),
          ],
        ),
      ),
    );
  }

  /// Build profile header with user info
  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorConstants.primaryColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile image
          CircleAvatar(
            radius: 50,
            backgroundColor: Colors.white,
            child: _userData['profileImage'] != null
                ? ClipOval(
                    child: Image.network(
                      _userData['profileImage'],
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                    ),
                  )
                : Text(
                    _userData['name'].substring(0, 1),
                    style: TextStyle(
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.primaryColor,
                    ),
                  ),
          ),

          const SizedBox(height: 16),

          // User name
          Text(
            _userData['name'],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 8),

          // User email
          Text(
            _userData['email'],
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),

          const SizedBox(height: 4),

          // User phone
          Text(
            _userData['phone'],
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),

          const SizedBox(height: 16),

          // User stats
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                label: 'Balance',
                value: '৳${_userData['walletBalance'].toStringAsFixed(2)}',
              ),
              _buildStatItem(
                label: 'Transactions',
                value: _userData['totalTransactions'].toString(),
              ),
              _buildStatItem(
                label: 'Member Since',
                value: _formatDate(_userData['memberSince']),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build profile menu with options
  Widget _buildProfileMenu(BuildContext context) {
    return Column(
      children: [
        _buildMenuItem(
          icon: Icons.account_circle,
          title: ref.watch(languageProvider) == AppLanguage.english
              ? 'Edit Profile'
              : 'প্রোফাইল সম্পাদনা করুন',
          subtitle: ref.watch(languageProvider) == AppLanguage.english
              ? 'Update your personal information'
              : 'আপনার ব্যক্তিগত তথ্য আপডেট করুন',
          onTap: () async {
            // Navigate to edit profile
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => EditProfileScreen(userData: _userData),
              ),
            );

            // Update user data if result is not null
            if (result != null && mounted) {
              setState(() {
                _userData = {
                  ..._userData,
                  ...result,
                };
              });
            }
          },
        ),
        _buildMenuItem(
          icon: Icons.history,
          title: 'Transaction History',
          subtitle: 'View your past transactions',
          onTap: () {
            context.push(RouteNames.walletHistory);
          },
        ),
        _buildMenuItem(
          icon: Icons.payment,
          title: 'Payment Methods',
          subtitle: 'Manage your payment options',
          onTap: () {
            // TODO: Navigate to payment methods
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Payment Methods - Coming Soon')),
            );
          },
        ),
        _buildMenuItem(
          icon: Icons.notifications,
          title: 'Notifications',
          subtitle: 'Manage your notification preferences',
          onTap: () {
            // TODO: Navigate to notifications
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Notifications - Coming Soon')),
            );
          },
        ),
        _buildMenuItem(
          icon: Icons.security,
          title: 'Security',
          subtitle: 'Update your security settings',
          onTap: () {
            // TODO: Navigate to security
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Security - Coming Soon')),
            );
          },
        ),
        _buildMenuItem(
          icon: Icons.support_agent,
          title: 'Support',
          subtitle: 'Get help and support',
          onTap: () {
            context.push(RouteNames.support);
          },
        ),
        _buildMenuItem(
          icon: Icons.logout,
          title: 'Logout',
          subtitle: 'Sign out from your account',
          onTap: () {
            _showLogoutConfirmation(context);
          },
        ),
      ],
    );
  }

  /// Build a menu item
  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: ColorConstants.primaryLightColor.withAlpha(50),
        child: Icon(
          icon,
          color: ColorConstants.primaryColor,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  /// Build a stat item
  Widget _buildStatItem({
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  /// Format date string
  String _formatDate(String dateString) {
    final date = DateTime.parse(dateString);
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Show logout confirmation dialog
  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement logout
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Logged out successfully')),
              );
              context.go(RouteNames.login);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}

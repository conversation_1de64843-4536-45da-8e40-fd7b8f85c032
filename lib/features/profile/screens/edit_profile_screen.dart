import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';

/// Edit Profile Screen
class EditProfileScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> userData;

  const EditProfileScreen({
    super.key,
    required this.userData,
  });

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _addressController;
  bool _isLoading = false;
  String? _profileImageUrl;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.userData['name']);
    _emailController = TextEditingController(text: widget.userData['email']);
    _phoneController = TextEditingController(text: widget.userData['phone']);
    _addressController = TextEditingController(text: widget.userData['address'] ?? '');
    _profileImageUrl = widget.userData['profileImage'];
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// Handle profile update
  Future<void> _updateProfile() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Simulate API call with delay
        await Future.delayed(const Duration(seconds: 1));

        // In a real app, you would update the user profile in the backend
        // For now, we'll just show a success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Profile updated successfully'
                    : 'প্রোফাইল সফলভাবে আপডেট করা হয়েছে',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, {
            'name': _nameController.text,
            'email': _emailController.text,
            'phone': _phoneController.text,
            'address': _addressController.text,
            'profileImage': _profileImageUrl,
          });
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Failed to update profile: $e'
                    : 'প্রোফাইল আপডেট করতে ব্যর্থ হয়েছে: $e',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  /// Show image source selection dialog
  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Select Image Source'
              : 'ছবির উৎস নির্বাচন করুন',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Camera'
                    : 'ক্যামেরা',
              ),
              onTap: () {
                Navigator.pop(context);
                _getImage(true);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Gallery'
                    : 'গ্যালারি',
              ),
              onTap: () {
                Navigator.pop(context);
                _getImage(false);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Get image from camera or gallery
  Future<void> _getImage(bool fromCamera) async {
    // In a real app, you would use image_picker package to get the image
    // For now, we'll just simulate it
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Simulate setting a new profile image URL
    setState(() {
      // For demo purposes, we'll just toggle between null and a placeholder
      _profileImageUrl = _profileImageUrl == null
          ? 'https://via.placeholder.com/150'
          : null;
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Edit Profile'
              : 'প্রোফাইল সম্পাদনা করুন',
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Profile image
              GestureDetector(
                onTap: _showImageSourceDialog,
                child: Stack(
                  children: [
                    CircleAvatar(
                      radius: 60,
                      backgroundColor: Colors.grey.shade200,
                      child: _profileImageUrl != null
                          ? ClipOval(
                              child: Image.network(
                                _profileImageUrl!,
                                width: 120,
                                height: 120,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Text(
                                    _nameController.text.isNotEmpty
                                        ? _nameController.text.substring(0, 1)
                                        : 'U',
                                    style: TextStyle(
                                      fontSize: 40,
                                      fontWeight: FontWeight.bold,
                                      color: ColorConstants.primaryColor,
                                    ),
                                  );
                                },
                              ),
                            )
                          : Text(
                              _nameController.text.isNotEmpty
                                  ? _nameController.text.substring(0, 1)
                                  : 'U',
                              style: TextStyle(
                                fontSize: 40,
                                fontWeight: FontWeight.bold,
                                color: ColorConstants.primaryColor,
                              ),
                            ),
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: ColorConstants.primaryColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Name field
              CustomTextField(
                controller: _nameController,
                labelText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Full Name'
                    : 'পুরো নাম',
                hintText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Enter your full name'
                    : 'আপনার পুরো নাম লিখুন',
                prefixIcon: const Icon(Icons.person),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter your name'
                        : 'অনুগ্রহ করে আপনার নাম লিখুন';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Email field
              CustomTextField(
                controller: _emailController,
                labelText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Email Address'
                    : 'ইমেইল ঠিকানা',
                hintText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Enter your email address'
                    : 'আপনার ইমেইল ঠিকানা লিখুন',
                keyboardType: TextInputType.emailAddress,
                prefixIcon: const Icon(Icons.email),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter your email'
                        : 'অনুগ্রহ করে আপনার ইমেইল লিখুন';
                  }
                  
                  // Basic email validation
                  final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                  if (!emailRegex.hasMatch(value)) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter a valid email'
                        : 'একটি বৈধ ইমেইল লিখুন';
                  }
                  
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Phone field
              CustomTextField(
                controller: _phoneController,
                labelText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Phone Number'
                    : 'ফোন নম্বর',
                hintText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Enter your phone number'
                    : 'আপনার ফোন নম্বর লিখুন',
                keyboardType: TextInputType.phone,
                prefixIcon: const Icon(Icons.phone),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter your phone number'
                        : 'অনুগ্রহ করে আপনার ফোন নম্বর লিখুন';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Address field
              CustomTextField(
                controller: _addressController,
                labelText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Address'
                    : 'ঠিকানা',
                hintText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Enter your address'
                    : 'আপনার ঠিকানা লিখুন',
                prefixIcon: const Icon(Icons.location_on),
                maxLines: 2,
              ),
              
              const SizedBox(height: 32),
              
              // Update button
              CustomButton(
                text: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Update Profile'
                    : 'প্রোফাইল আপডেট করুন',
                onPressed: _updateProfile,
                isLoading: _isLoading,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

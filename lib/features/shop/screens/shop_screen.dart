import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/widgets/error_widget.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/product_model.dart';
import 'package:irecharge_pro/data/providers/shop_providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Shop screen to display products
class ShopScreen extends ConsumerStatefulWidget {
  const ShopScreen({super.key});

  @override
  ConsumerState<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends ConsumerState<ShopScreen> {
  final _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Handle category selection
  void _selectCategory(ProductCategory category) {
    ref.read(selectedProductCategoryProvider.notifier).state = category;
  }

  /// Handle search query change
  void _onSearchChanged(String value) {
    ref.read(searchQueryProvider.notifier).state = value;
  }

  /// Handle add to cart
  void _addToCart(Product product) {
    ref.read(cartProvider.notifier).addToCart(product);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${product.name} added to cart'),
        action: SnackBarAction(
          label: 'View Cart',
          onPressed: () {
            // Navigate to cart screen
            // context.push(RouteNames.cart);
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final selectedCategory = ref.watch(selectedProductCategoryProvider);
    final searchQuery = ref.watch(searchQueryProvider);
    final cart = ref.watch(cartProvider);
    final cartTotal = ref.read(cartProvider.notifier).totalPrice;

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Search products...',
                  border: InputBorder.none,
                ),
                onChanged: _onSearchChanged,
                autofocus: true,
              )
            : const Text('Shop'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          // History button
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              context.go(RouteNames.history);
              // Navigate to the Shop tab in the history screen
            },
            tooltip: 'Order History',
          ),
          // Search button
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                if (_isSearching) {
                  _searchController.clear();
                  _onSearchChanged('');
                }
                _isSearching = !_isSearching;
              });
            },
          ),
          // Cart button
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.shopping_cart),
                onPressed: () {
                  // Show cart bottom sheet
                  _showCartBottomSheet(context);
                },
              ),
              if (cart.isNotEmpty)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: const BoxDecoration(
                      color: ColorConstants.errorColor,
                      shape: BoxShape.circle,
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      ref.read(cartProvider.notifier).totalItems.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search bar (when not in app bar)
          if (!_isSearching)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search products...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade200,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0),
                ),
                onChanged: _onSearchChanged,
              ),
            ),

          // Categories
          if (!_isSearching && searchQuery.isEmpty) ...[
            const Padding(
              padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                'Categories',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            _buildCategoriesSection(),
          ],

          // Products
          Expanded(
            child: _isSearching || searchQuery.isNotEmpty
                ? _buildSearchResults(searchQuery)
                : selectedCategory != null
                    ? _buildProductsByCategory(selectedCategory.id)
                    : _buildFeaturedProducts(),
          ),
        ],
      ),
      // Show cart summary at bottom if items in cart
      bottomNavigationBar: cart.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${ref.read(cartProvider.notifier).totalItems} items',
                        style: const TextStyle(
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        '\$${cartTotal.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Show cart bottom sheet
                        _showCartBottomSheet(context);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('View Cart'),
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  /// Build categories section
  Widget _buildCategoriesSection() {
    final categories = ref.watch(productCategoriesProvider);

    return categories.when(
      data: (categoryList) {
        return Container(
          height: 80,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: categoryList.length,
            itemBuilder: (context, index) {
              final category = categoryList[index];
              final isSelected =
                  ref.watch(selectedProductCategoryProvider)?.id == category.id;

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: _buildCategoryItem(
                  category: category,
                  isSelected: isSelected,
                  onTap: () => _selectCategory(category),
                ),
              );
            },
          ),
        );
      },
      loading: () => const SizedBox(
        height: 80,
        child: Center(child: LoadingIndicator()),
      ),
      error: (error, stackTrace) => SizedBox(
        height: 80,
        child: Center(
          child: ErrorDisplayWidget(
            title: 'Error',
            message: 'Failed to load categories',
            buttonText: 'Retry',
            onRetry: () => ref.refresh(productCategoriesProvider),
          ),
        ),
      ),
    );
  }

  /// Build category item
  Widget _buildCategoryItem({
    required ProductCategory category,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        width: 70,
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? category.color.withOpacity(0.1)
              : Colors.grey.withOpacity(0.05),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? category.color : Colors.transparent,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? category.color
                    : Color.fromRGBO(
                        category.color.red,
                        category.color.green,
                        category.color.blue,
                        0.1,
                      ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                category.icon ?? Icons.category,
                color: isSelected ? Colors.white : category.color,
                size: 20,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              category.name,
              style: TextStyle(
                fontSize: 11,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? category.color : Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// Show cart bottom sheet
  void _showCartBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Consumer(
              builder: (context, ref, child) {
                final cart = ref.watch(cartProvider);
                final cartTotal = ref.read(cartProvider.notifier).totalPrice;

                return cart.isEmpty
                    ? const Center(
                        child: Text('Your cart is empty'),
                      )
                    : Column(
                        children: [
                          // Header
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(13),
                                  blurRadius: 5,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                const Text(
                                  'Your Cart',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const Spacer(),
                                TextButton.icon(
                                  onPressed: () {
                                    ref.read(cartProvider.notifier).clearCart();
                                  },
                                  icon: const Icon(Icons.delete_outline,
                                      size: 16),
                                  label: const Text('Clear'),
                                ),
                              ],
                            ),
                          ),

                          // Cart items
                          Expanded(
                            child: ListView.builder(
                              controller: scrollController,
                              itemCount: cart.length,
                              itemBuilder: (context, index) {
                                final item = cart[index];
                                return ListTile(
                                  leading: Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade200,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(Icons.image),
                                  ),
                                  title: Text(
                                    item.product.name,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  subtitle: Text(
                                    '\$${item.product.actualPrice.toStringAsFixed(2)} × ${item.quantity}',
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(
                                            Icons.remove_circle_outline),
                                        onPressed: () {
                                          if (item.quantity > 1) {
                                            ref
                                                .read(cartProvider.notifier)
                                                .updateQuantity(
                                                  item.product.id,
                                                  item.quantity - 1,
                                                );
                                          } else {
                                            ref
                                                .read(cartProvider.notifier)
                                                .removeFromCart(
                                                  item.product.id,
                                                );
                                          }
                                        },
                                      ),
                                      Text('${item.quantity}'),
                                      IconButton(
                                        icon: const Icon(
                                            Icons.add_circle_outline),
                                        onPressed: () {
                                          ref
                                              .read(cartProvider.notifier)
                                              .updateQuantity(
                                                item.product.id,
                                                item.quantity + 1,
                                              );
                                        },
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),

                          // Checkout section
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(13),
                                  blurRadius: 10,
                                  offset: const Offset(0, -5),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Subtotal'),
                                    Text('\$${cartTotal.toStringAsFixed(2)}'),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text('Shipping'),
                                    const Text('\$5.00'),
                                  ],
                                ),
                                const Divider(height: 24),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'Total',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                    Text(
                                      '\$${(cartTotal + 5.0).toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      // Navigate to checkout
                                      Navigator.pop(context);
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content:
                                              Text('Proceeding to checkout...'),
                                        ),
                                      );
                                    },
                                    style: ElevatedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 16),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: const Text('Checkout'),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
              },
            );
          },
        );
      },
    );
  }

  /// Build featured products section
  Widget _buildFeaturedProducts() {
    final featuredProducts = ref.watch(featuredProductsProvider);
    final allProducts = ref.watch(productsProvider);

    return featuredProducts.when(
      data: (featuredList) {
        if (featuredList.isEmpty) {
          return const Center(
            child: Text('No featured products available.'),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            await Future.wait([
              ref.refresh(featuredProductsProvider.future),
              ref.refresh(productsProvider.future),
            ]);
          },
          child: ListView(
            padding: const EdgeInsets.only(bottom: 16),
            children: [
              // Banner
              Container(
                height: 150,
                margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(
                    ColorConstants.primaryColor.red,
                    ColorConstants.primaryColor.green,
                    ColorConstants.primaryColor.blue,
                    0.1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image:
                        const AssetImage('assets/images/products/banner.png'),
                    fit: BoxFit.cover,
                    onError: (_, __) => const SizedBox(),
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              Colors.black.withAlpha(179),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Summer Sale',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Up to 50% off',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {},
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: ColorConstants.primaryColor,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Shop Now'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Featured products
              const Padding(
                padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                child: Text(
                  'Featured Products',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(
                height: 200, // Increased height to accommodate flexible layout
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  itemCount: featuredList.length,
                  itemBuilder: (context, index) {
                    final product = featuredList[index];
                    return Container(
                      width: 120,
                      margin: const EdgeInsets.only(right: 8),
                      child: _buildFeaturedProductItem(product),
                    );
                  },
                ),
              ),

              // All products
              allProducts.when(
                data: (productList) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.fromLTRB(16, 24, 16, 8),
                        child: Text(
                          'All Products',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.all(16),
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          childAspectRatio: 0.65,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: productList.length,
                        itemBuilder: (context, index) {
                          final product = productList[index];
                          return _buildProductItem(product);
                        },
                      ),
                    ],
                  );
                },
                loading: () => const Center(child: LoadingIndicator()),
                error: (error, stackTrace) => Center(
                  child: ErrorDisplayWidget(
                    title: 'Error',
                    message: 'Failed to load products',
                    buttonText: 'Retry',
                    onRetry: () => ref.refresh(productsProvider),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorDisplayWidget(
          title: 'Error',
          message: 'Failed to load featured products',
          buttonText: 'Retry',
          onRetry: () => ref.refresh(featuredProductsProvider),
        ),
      ),
    );
  }

  /// Build products by category
  Widget _buildProductsByCategory(String categoryId) {
    final productsByCategory =
        ref.watch(productsByCategoryProvider(categoryId));
    final category = ref.watch(productCategoryByIdProvider(categoryId));

    return productsByCategory.when(
      data: (productList) {
        if (productList.isEmpty) {
          return Center(
            child: Text(
                'No products available in ${category.value?.name ?? "this category"}.'),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                category.value?.name ?? 'Products',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.65,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: productList.length,
                itemBuilder: (context, index) {
                  final product = productList[index];
                  return _buildProductItem(product);
                },
              ),
            ),
          ],
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorDisplayWidget(
          title: 'Error',
          message: 'Failed to load products',
          buttonText: 'Retry',
          onRetry: () => ref.refresh(productsByCategoryProvider(categoryId)),
        ),
      ),
    );
  }

  /// Build search results
  Widget _buildSearchResults(String query) {
    if (query.isEmpty) {
      return const Center(
        child: Text('Enter a search term to find products.'),
      );
    }

    final searchResults = ref.watch(productSearchProvider(query));

    return searchResults.when(
      data: (productList) {
        if (productList.isEmpty) {
          return Center(
            child: Text('No products found for "$query".'),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                'Search Results for "$query"',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 0.65,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: productList.length,
                itemBuilder: (context, index) {
                  final product = productList[index];
                  return _buildProductItem(product);
                },
              ),
            ),
          ],
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: ErrorDisplayWidget(
          title: 'Error',
          message: 'Failed to search products',
          buttonText: 'Retry',
          onRetry: () => ref.refresh(productSearchProvider(query)),
        ),
      ),
    );
  }

  /// Build featured product item (smaller version)
  Widget _buildFeaturedProductItem(Product product) {
    final discountPercentage = product.discountPercentage;

    return Card(
      elevation: 1,
      margin: const EdgeInsets.all(2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      child: InkWell(
        onTap: () {
          // Navigate to product details
          context.push(
              '${RouteNames.productDetails.replaceAll(':id', '')}${product.id}');
        },
        borderRadius: BorderRadius.circular(4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Prevent overflow
          children: [
            // Product image
            Flexible(
              flex: 3,
              child: Stack(
                children: [
                  AspectRatio(
                    aspectRatio: 1,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(4),
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.image,
                          size: 28,
                          color: Colors.grey.shade400,
                        ),
                      ),
                    ),
                  ),
                  if (discountPercentage != null)
                    Positioned(
                      top: 2,
                      left: 2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 3,
                          vertical: 1,
                        ),
                        decoration: BoxDecoration(
                          color: ColorConstants.errorColor,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: Text(
                          '-${discountPercentage.toStringAsFixed(0)}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 8,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Product details
            Flexible(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Product name
                    Flexible(
                      child: Text(
                        product.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const SizedBox(height: 1),

                    // Product price
                    Flexible(
                      child: Row(
                        children: [
                          if (product.discountPrice != null) ...[
                            Flexible(
                              child: Text(
                                '৳${product.discountPrice!.toStringAsFixed(0)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: ColorConstants.primaryColor,
                                  fontSize: 9,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 2),
                            Flexible(
                              child: Text(
                                '৳${product.price.toStringAsFixed(0)}',
                                style: const TextStyle(
                                  decoration: TextDecoration.lineThrough,
                                  color: Colors.grey,
                                  fontSize: 7,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ] else
                            Flexible(
                              child: Text(
                                '৳${product.price.toStringAsFixed(0)}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: ColorConstants.primaryColor,
                                  fontSize: 9,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 1),

                    // Rating
                    Flexible(
                      child: Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 10,
                          ),
                          const SizedBox(width: 1),
                          Flexible(
                            child: Text(
                              product.rating.toStringAsFixed(1),
                              style: const TextStyle(
                                fontSize: 8,
                                color: Colors.grey,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 2),

                    // Add to cart button
                    SizedBox(
                      width: double.infinity,
                      height: 18,
                      child: ElevatedButton(
                        onPressed: () => _addToCart(product),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        child: const Text(
                          'Add to Cart',
                          style: TextStyle(fontSize: 8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build product item
  Widget _buildProductItem(Product product) {
    final discountPercentage = product.discountPercentage;

    return Card(
      elevation: 1,
      margin: const EdgeInsets.all(2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(6),
      ),
      child: InkWell(
        onTap: () {
          // Navigate to product details
          context.push(
              '${RouteNames.productDetails.replaceAll(':id', '')}${product.id}');
        },
        borderRadius: BorderRadius.circular(6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            Stack(
              children: [
                AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(6),
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.image,
                        size: 36,
                        color: Colors.grey.shade400,
                      ),
                    ),
                  ),
                ),
                if (discountPercentage != null)
                  Positioned(
                    top: 4,
                    left: 4,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: ColorConstants.errorColor,
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Text(
                        '-${discountPercentage.toStringAsFixed(0)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Product details
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 11,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 2),

                    // Product price
                    Row(
                      children: [
                        if (product.discountPrice != null) ...[
                          Text(
                            '\$${product.discountPrice!.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColorConstants.primaryColor,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '\$${product.price.toStringAsFixed(2)}',
                            style: const TextStyle(
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey,
                              fontSize: 9,
                            ),
                          ),
                        ] else
                          Text(
                            '\$${product.price.toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColorConstants.primaryColor,
                              fontSize: 10,
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 1),

                    // Rating
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 10,
                        ),
                        const SizedBox(width: 1),
                        Text(
                          product.rating.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 9,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),

                    const Spacer(),

                    // Add to cart button
                    SizedBox(
                      width: double.infinity,
                      height: 20,
                      child: ElevatedButton(
                        onPressed: () => _addToCart(product),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                        child: const Text(
                          'Add to Cart',
                          style: TextStyle(fontSize: 9),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/data/models/product_model.dart';
import 'package:irecharge_pro/data/providers/shop_providers.dart';

/// Product details screen
class ProductDetailsScreen extends ConsumerStatefulWidget {
  final String productId;

  const ProductDetailsScreen({
    super.key,
    required this.productId,
  });

  @override
  ConsumerState<ProductDetailsScreen> createState() => _ProductDetailsScreenState();
}

class _ProductDetailsScreenState extends ConsumerState<ProductDetailsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _quantity = 1;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Handle add to cart
  void _addToCart(Product product) {
    ref.read(cartProvider.notifier).addToCart(product, quantity: _quantity);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? '${product.name} added to cart'
              : '${product.name} কার্টে যোগ করা হয়েছে',
        ),
        action: SnackBarAction(
          label: ref.watch(languageProvider) == AppLanguage.english
              ? 'View Cart'
              : 'কার্ট দেখুন',
          onPressed: () {
            // Show cart bottom sheet
            _showCartBottomSheet(context);
          },
        ),
      ),
    );
  }

  /// Handle buy now
  void _buyNow(Product product) {
    // First add to cart
    ref.read(cartProvider.notifier).addToCart(product, quantity: _quantity);

    // Then show checkout dialog
    _showCheckoutDialog(context);
  }

  /// Show cart bottom sheet
  void _showCartBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return Consumer(
              builder: (context, ref, child) {
                final cart = ref.watch(cartProvider);
                final cartTotal = ref.read(cartProvider.notifier).totalPrice;

                return cart.isEmpty
                    ? Center(
                        child: Text(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Your cart is empty'
                              : 'আপনার কার্ট খালি',
                        ),
                      )
                    : Column(
                        children: [
                          // Header
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 5,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Text(
                                  ref.watch(languageProvider) == AppLanguage.english
                                      ? 'Your Cart'
                                      : 'আপনার কার্ট',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const Spacer(),
                                TextButton.icon(
                                  onPressed: () {
                                    ref.read(cartProvider.notifier).clearCart();
                                  },
                                  icon: const Icon(Icons.delete_outline, size: 16),
                                  label: Text(
                                    ref.watch(languageProvider) == AppLanguage.english
                                        ? 'Clear'
                                        : 'মুছুন',
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Cart items
                          Expanded(
                            child: ListView.builder(
                              controller: scrollController,
                              itemCount: cart.length,
                              itemBuilder: (context, index) {
                                final item = cart[index];
                                return ListTile(
                                  leading: Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade200,
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: const Icon(Icons.image),
                                  ),
                                  title: Text(
                                    item.product.name,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  subtitle: Text(
                                    '\$${item.product.actualPrice.toStringAsFixed(2)} × ${item.quantity}',
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Icons.remove_circle_outline),
                                        onPressed: () {
                                          if (item.quantity > 1) {
                                            ref.read(cartProvider.notifier).updateQuantity(
                                                  item.product.id,
                                                  item.quantity - 1,
                                                );
                                          } else {
                                            ref.read(cartProvider.notifier).removeFromCart(
                                                  item.product.id,
                                                );
                                          }
                                        },
                                      ),
                                      Text('${item.quantity}'),
                                      IconButton(
                                        icon: const Icon(Icons.add_circle_outline),
                                        onPressed: () {
                                          ref.read(cartProvider.notifier).updateQuantity(
                                                item.product.id,
                                                item.quantity + 1,
                                              );
                                        },
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),

                          // Checkout section
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, -5),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      ref.watch(languageProvider) == AppLanguage.english
                                          ? 'Subtotal'
                                          : 'সাবটোটাল',
                                    ),
                                    Text('\$${cartTotal.toStringAsFixed(2)}'),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      ref.watch(languageProvider) == AppLanguage.english
                                          ? 'Shipping'
                                          : 'শিপিং',
                                    ),
                                    const Text('\$5.00'),
                                  ],
                                ),
                                const Divider(height: 24),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      ref.watch(languageProvider) == AppLanguage.english
                                          ? 'Total'
                                          : 'মোট',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                    Text(
                                      '\$${(cartTotal + 5.0).toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                      _showCheckoutDialog(context);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(vertical: 16),
                                      backgroundColor: ColorConstants.primaryColor,
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text(
                                      ref.watch(languageProvider) == AppLanguage.english
                                          ? 'Proceed to Checkout'
                                          : 'চেকআউট করুন',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      );
              },
            );
          },
        );
      },
    );
  }

  /// Show checkout dialog with shipping information
  void _showCheckoutDialog(BuildContext context) {
    // Form controllers
    final nameController = TextEditingController();
    final addressController = TextEditingController();
    final cityController = TextEditingController();
    final zipController = TextEditingController();
    final phoneController = TextEditingController();

    // Selected shipping method
    String selectedShippingMethod = 'standard';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Shipping Information'
                    : 'শিপিং তথ্য',
                style: const TextStyle(fontSize: 18),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name field
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: ref.watch(languageProvider) == AppLanguage.english
                            ? 'Full Name'
                            : 'পুরো নাম',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 12),

                    // Address field
                    TextField(
                      controller: addressController,
                      decoration: InputDecoration(
                        labelText: ref.watch(languageProvider) == AppLanguage.english
                            ? 'Address'
                            : 'ঠিকানা',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 12),

                    // City and ZIP code in a row
                    Row(
                      children: [
                        // City field
                        Expanded(
                          flex: 3,
                          child: TextField(
                            controller: cityController,
                            decoration: InputDecoration(
                              labelText: ref.watch(languageProvider) == AppLanguage.english
                                  ? 'City'
                                  : 'শহর',
                              border: const OutlineInputBorder(),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 12,
                              ),
                            ),
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                        const SizedBox(width: 8),

                        // ZIP code field
                        Expanded(
                          flex: 2,
                          child: TextField(
                            controller: zipController,
                            decoration: InputDecoration(
                              labelText: ref.watch(languageProvider) == AppLanguage.english
                                  ? 'ZIP Code'
                                  : 'পোস্ট কোড',
                              border: const OutlineInputBorder(),
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 12,
                              ),
                            ),
                            keyboardType: TextInputType.number,
                            style: const TextStyle(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Phone field
                    TextField(
                      controller: phoneController,
                      decoration: InputDecoration(
                        labelText: ref.watch(languageProvider) == AppLanguage.english
                            ? 'Phone Number'
                            : 'ফোন নম্বর',
                        border: const OutlineInputBorder(),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                      ),
                      keyboardType: TextInputType.phone,
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),

                    // Shipping method selection
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Shipping Method'
                          : 'শিপিং পদ্ধতি',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Standard shipping option
                    RadioListTile<String>(
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Standard Shipping (3-5 days)'
                                : 'স্ট্যান্ডার্ড শিপিং (৩-৫ দিন)',
                            style: const TextStyle(fontSize: 13),
                          ),
                          const Text(
                            '\$5.00',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                      value: 'standard',
                      groupValue: selectedShippingMethod,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 0,
                      ),
                      onChanged: (value) {
                        setState(() {
                          selectedShippingMethod = value!;
                        });
                      },
                    ),

                    // Express shipping option
                    RadioListTile<String>(
                      title: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Express Shipping (1-2 days)'
                                : 'এক্সপ্রেস শিপিং (১-২ দিন)',
                            style: const TextStyle(fontSize: 13),
                          ),
                          const Text(
                            '\$15.00',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                      value: 'express',
                      groupValue: selectedShippingMethod,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 0,
                      ),
                      onChanged: (value) {
                        setState(() {
                          selectedShippingMethod = value!;
                        });
                      },
                    ),

                    const SizedBox(height: 8),

                    // Demo app notice
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'This is a demo app. No actual purchase will be made.'
                            : 'এটি একটি ডেমো অ্যাপ। কোন প্রকৃত ক্রয় করা হবে না।',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Cancel'
                        : 'বাতিল',
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Validate form
                    if (nameController.text.isEmpty ||
                        addressController.text.isEmpty ||
                        cityController.text.isEmpty ||
                        zipController.text.isEmpty ||
                        phoneController.text.isEmpty) {
                      // Show error message
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Please fill in all fields'
                                : 'সব ক্ষেত্র পূরণ করুন',
                          ),
                          backgroundColor: Colors.red,
                        ),
                      );
                      return;
                    }

                    // Close dialog and show order confirmation
                    Navigator.pop(context);
                    _showOrderConfirmation(context);
                  },
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Place Order'
                        : 'অর্ডার করুন',
                  ),
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      // Dispose controllers when dialog is closed
      nameController.dispose();
      addressController.dispose();
      cityController.dispose();
      zipController.dispose();
      phoneController.dispose();
    });
  }

  /// Show order confirmation
  void _showOrderConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Order Placed'
              : 'অর্ডার সম্পন্ন হয়েছে',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Your order has been placed successfully!'
                  : 'আপনার অর্ডার সফলভাবে করা হয়েছে!',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Order #12345'
                  : 'অর্ডার #12345',
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Clear cart after successful order
              ref.read(cartProvider.notifier).clearCart();
            },
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Continue Shopping'
                  : 'শপিং চালিয়ে যান',
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get product by ID
    final product = DemoProducts.getById(widget.productId);

    if (product == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Product Not Found'
                : 'পণ্য পাওয়া যায়নি',
          ),
        ),
        body: Center(
          child: Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'The requested product could not be found.'
                : 'অনুরোধকৃত পণ্য পাওয়া যায়নি।',
          ),
        ),
      );
    }

    // Get related products
    final relatedProducts = DemoProducts.getByCategory(product.categoryId)
        .where((p) => p.id != product.id)
        .take(4)
        .toList();

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Product image
                  Container(
                    color: Colors.grey.shade200,
                    child: const Center(
                      child: Icon(
                        Icons.image,
                        size: 100,
                        color: Colors.grey,
                      ),
                    ),
                  ),

                  // Gradient overlay for better text visibility
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    height: 80,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [
                            Colors.black.withOpacity(0.5),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Discount badge
                  if (product.discountPrice != null)
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: ColorConstants.errorColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '-${product.discountPercentage!.toStringAsFixed(0)}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Product details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    product.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Rating and reviews
                  Row(
                    children: [
                      // Rating stars
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < product.rating.floor()
                                ? Icons.star
                                : index < product.rating
                                    ? Icons.star_half
                                    : Icons.star_border,
                            color: Colors.amber,
                            size: 18,
                          );
                        }),
                      ),

                      const SizedBox(width: 8),

                      // Rating value
                      Text(
                        '${product.rating}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Review count
                      Text(
                        '(${product.reviewCount} ${ref.watch(languageProvider) == AppLanguage.english ? 'reviews' : 'রিভিউ'})',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Price
                  Row(
                    children: [
                      // Current price
                      Text(
                        '\$${product.actualPrice.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: ColorConstants.primaryColor,
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Original price if discounted
                      if (product.discountPrice != null)
                        Text(
                          '\$${product.price.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Quantity selector
                  Row(
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Quantity:'
                            : 'পরিমাণ:',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Decrease button
                      InkWell(
                        onTap: () {
                          if (_quantity > 1) {
                            setState(() {
                              _quantity--;
                            });
                          }
                        },
                        borderRadius: BorderRadius.circular(4),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.remove,
                            size: 16,
                          ),
                        ),
                      ),

                      // Quantity display
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '$_quantity',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      // Increase button
                      InkWell(
                        onTap: () {
                          setState(() {
                            _quantity++;
                          });
                        },
                        borderRadius: BorderRadius.circular(4),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.add,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Action buttons
                  Row(
                    children: [
                      // Add to cart button
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _addToCart(product),
                          icon: const Icon(Icons.shopping_cart),
                          label: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Add to Cart'
                                : 'কার্টে যোগ করুন',
                          ),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            side: const BorderSide(
                              color: ColorConstants.primaryColor,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Buy now button
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _buyNow(product),
                          icon: const Icon(Icons.flash_on),
                          label: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Buy Now'
                                : 'এখনই কিনুন',
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            backgroundColor: ColorConstants.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Tab bar
                  TabBar(
                    controller: _tabController,
                    labelColor: ColorConstants.primaryColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: ColorConstants.primaryColor,
                    tabs: [
                      Tab(
                        text: ref.watch(languageProvider) == AppLanguage.english
                            ? 'Description'
                            : 'বর্ণনা',
                      ),
                      Tab(
                        text: ref.watch(languageProvider) == AppLanguage.english
                            ? 'Reviews'
                            : 'রিভিউ',
                      ),
                      Tab(
                        text: ref.watch(languageProvider) == AppLanguage.english
                            ? 'Shipping'
                            : 'শিপিং',
                      ),
                    ],
                  ),

                  // Tab content
                  SizedBox(
                    height: 200,
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Description tab
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Text(product.description),
                        ),

                        // Reviews tab
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'Customer Reviews (${product.reviewCount})'
                                    : 'গ্রাহক রিভিউ (${product.reviewCount})',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'This is a demo app. No actual reviews available.'
                                    : 'এটি একটি ডেমো অ্যাপ। কোন প্রকৃত রিভিউ উপলব্ধ নেই।',
                              ),
                            ],
                          ),
                        ),

                        // Shipping tab
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'Shipping Information'
                                    : 'শিপিং তথ্য',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'This is a demo app. No actual shipping available.'
                                    : 'এটি একটি ডেমো অ্যাপ। কোন প্রকৃত শিপিং উপলব্ধ নেই।',
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Related products
                  if (relatedProducts.isNotEmpty) ...[
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Related Products'
                          : 'সম্পর্কিত পণ্য',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),

                    const SizedBox(height: 16),

                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: relatedProducts.length,
                        itemBuilder: (context, index) {
                          final relatedProduct = relatedProducts[index];
                          return GestureDetector(
                            onTap: () {
                              // Navigate to related product
                              context.push('/shop/products/${relatedProduct.id}');
                            },
                            child: Container(
                              width: 140,
                              margin: const EdgeInsets.only(right: 16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade200),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Product image
                                  Container(
                                    height: 100,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.shade200,
                                      borderRadius: const BorderRadius.vertical(
                                        top: Radius.circular(8),
                                      ),
                                    ),
                                    child: const Center(
                                      child: Icon(
                                        Icons.image,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ),

                                  // Product details
                                  Padding(
                                    padding: const EdgeInsets.all(8),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        // Product name
                                        Text(
                                          relatedProduct.name,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),

                                        const SizedBox(height: 4),

                                        // Product price
                                        Text(
                                          '\$${relatedProduct.actualPrice.toStringAsFixed(2)}',
                                          style: const TextStyle(
                                            color: ColorConstants.primaryColor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/providers/theme_provider.dart';
import 'package:irecharge_pro/core/services/auth_service.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Settings screen
class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  // General settings
  bool _notificationsEnabled = true;
  bool _biometricEnabled = false;
  bool _locationEnabled = true;
  bool _autoUpdateEnabled = true;
  bool _isPro = true;

  // Security settings
  bool _otpEnabled = true;
  bool _pinEnabled = false;
  Set<String> _enabledOtpMethods = {'phone', 'email'};
  bool _googleAuthSetup = false;

  // Privacy settings
  bool _dataCollectionEnabled = true;
  bool _analyticsEnabled = true;
  bool _personalizationEnabled = true;

  // Notification settings
  bool _emailNotificationsEnabled = true;
  bool _pushNotificationsEnabled = true;
  bool _smsNotificationsEnabled = false;
  bool _transactionAlertsEnabled = true;
  bool _promotionalNotificationsEnabled = false;

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeModeProvider);
    final language = ref.watch(languageProvider);
    final localizations = AppLocalizations.of(context);
    final isDarkMode = themeMode == ThemeMode.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('settings')),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // User profile card
          Card(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 22,
                    backgroundColor: ColorConstants.primaryColor.withAlpha(25),
                    child: Text(
                      'JD',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: ColorConstants.primaryColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'John Doe',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Text(
                          '<EMAIL>',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  OutlinedButton(
                    onPressed: () {
                      context.push(RouteNames.profile);
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 0,
                      ),
                      minimumSize: const Size(0, 28),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Edit'
                          : 'সম্পাদনা',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Pro mode card
          Card(
            color: isDarkMode ? Colors.grey.shade800 : ColorConstants.primaryColor,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.workspace_premium,
                        color: Colors.amber,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'iRecharge Pro Mode'
                                  : 'আইরিচার্জ প্রো মোড',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: isDarkMode ? Colors.white : Colors.white,
                              ),
                            ),
                            Text(
                              _isPro
                                  ? (ref.watch(languageProvider) == AppLanguage.english
                                      ? 'Enabled'
                                      : 'সক্রিয়')
                                  : (ref.watch(languageProvider) == AppLanguage.english
                                      ? 'Disabled'
                                      : 'নিষ্ক্রিয়'),
                              style: TextStyle(
                                color: isDarkMode ? Colors.white70 : Colors.white70,
                                fontSize: 11,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Switch(
                        value: _isPro,
                        activeColor: Colors.amber,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        onChanged: (value) {
                          setState(() {
                            _isPro = value;
                          });
                        },
                      ),
                    ],
                  ),
                  if (_isPro) ...[
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildProFeatureChip(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'No fees'
                              : 'কোন ফি নেই'
                        ),
                        _buildProFeatureChip(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Priority support'
                              : 'অগ্রাধিকার সহায়তা'
                        ),
                        _buildProFeatureChip(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Exclusive offers'
                              : 'এক্সক্লুসিভ অফার'
                        ),
                        _buildProFeatureChip(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Higher limits'
                              : 'উচ্চতর সীমা'
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Appearance section
          _buildSectionHeader(context, localizations.translate('appearance')),
          const SizedBox(height: 8),

          // Theme settings
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.palette),
                  title: Text(localizations.translate('theme')),
                  trailing: DropdownButton<ThemeMode>(
                    value: themeMode,
                    underline: const SizedBox(),
                    onChanged: (ThemeMode? newValue) {
                      if (newValue != null) {
                        ref.read(themeModeProvider.notifier).setThemeMode(newValue);
                      }
                    },
                    items: [
                      DropdownMenuItem(
                        value: ThemeMode.system,
                        child: Text(localizations.translate('system_theme')),
                      ),
                      DropdownMenuItem(
                        value: ThemeMode.light,
                        child: Text(localizations.translate('light_mode')),
                      ),
                      DropdownMenuItem(
                        value: ThemeMode.dark,
                        child: Text(localizations.translate('dark_mode')),
                      ),
                    ],
                  ),
                ),

                // Theme mode toggle
                SwitchListTile(
                  secondary: Icon(
                    themeMode == ThemeMode.dark ? Icons.dark_mode : Icons.light_mode,
                  ),
                  title: Text(
                    themeMode == ThemeMode.dark
                        ? localizations.translate('dark_mode')
                        : localizations.translate('light_mode'),
                  ),
                  value: themeMode == ThemeMode.dark,
                  onChanged: (bool value) {
                    ref.read(themeModeProvider.notifier).setThemeMode(
                          value ? ThemeMode.dark : ThemeMode.light,
                        );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Language settings
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.language),
                  title: Text(localizations.translate('language')),
                  trailing: DropdownButton<AppLanguage>(
                    value: language,
                    underline: const SizedBox(),
                    onChanged: (AppLanguage? newValue) {
                      if (newValue != null) {
                        ref.read(languageProvider.notifier).setLanguage(newValue);
                      }
                    },
                    items: AppLanguage.values.map((AppLanguage language) {
                      return DropdownMenuItem<AppLanguage>(
                        value: language,
                        child: Text(localizations.translate(language.name.toLowerCase())),
                      );
                    }).toList(),
                  ),
                ),

                // Language selection
                for (final lang in AppLanguage.values)
                  RadioListTile<AppLanguage>(
                    title: Text(lang.name),
                    subtitle: Text(lang.code.toUpperCase()),
                    value: lang,
                    groupValue: language,
                    onChanged: (AppLanguage? value) {
                      if (value != null) {
                        ref.read(languageProvider.notifier).setLanguage(value);
                      }
                    },
                  ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Notifications section
          _buildSectionHeader(context, 'Notifications'),
          const SizedBox(height: 8),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: const Text('Enable Notifications'),
                  subtitle: const Text('Receive alerts and updates'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationsEnabled = value;
                    });
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.notifications_active),
                  title: const Text('Notification Preferences'),
                  subtitle: const Text('Customize notification types'),
                  trailing: const Icon(Icons.chevron_right),
                  enabled: _notificationsEnabled,
                  onTap: _notificationsEnabled
                      ? () {
                          // TODO: Navigate to notification preferences
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Notification Preferences - Coming Soon')),
                          );
                        }
                      : null,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Security section
          _buildSectionHeader(
            context,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Security'
                : 'নিরাপত্তা'
          ),
          const SizedBox(height: 8),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.fingerprint),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Biometric Authentication'
                        : 'বায়োমেট্রিক প্রমাণীকরণ'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Use fingerprint or face ID'
                        : 'আঙুলের ছাপ বা মুখ আইডি ব্যবহার করুন'
                  ),
                  value: _biometricEnabled,
                  onChanged: (value) {
                    setState(() {
                      _biometricEnabled = value;
                    });
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.pin),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'PIN Authentication'
                        : 'পিন প্রমাণীকরণ'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Use a PIN code to secure your account'
                        : 'আপনার অ্যাকাউন্ট সুরক্ষিত করতে পিন কোড ব্যবহার করুন'
                  ),
                  value: _pinEnabled,
                  onChanged: (value) {
                    setState(() {
                      _pinEnabled = value;
                    });

                    if (value) {
                      // Show PIN setup dialog
                      _showPinSetupDialog(context);
                    }
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.security),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Two-Factor Authentication (2FA)'
                        : 'দুই-ফ্যাক্টর প্রমাণীকরণ (2FA)'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Secure your account with OTP verification'
                        : 'OTP যাচাইকরণ দিয়ে আপনার অ্যাকাউন্ট সুরক্ষিত করুন'
                  ),
                  value: _otpEnabled,
                  onChanged: (value) {
                    setState(() {
                      _otpEnabled = value;
                    });
                  },
                ),
                if (_otpEnabled) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'OTP Verification Methods',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Select how you want to receive verification codes',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Phone number option
                        _buildOtpMethodTile(
                          title: 'Phone Number',
                          subtitle: '+880 1712-345678',
                          icon: Icons.phone,
                          method: 'phone',
                        ),

                        // Email option
                        _buildOtpMethodTile(
                          title: 'Email',
                          subtitle: '<EMAIL>',
                          icon: Icons.email,
                          method: 'email',
                        ),

                        // WhatsApp option
                        _buildOtpMethodTile(
                          title: 'WhatsApp',
                          subtitle: '+880 1712-345678',
                          icon: FontAwesomeIcons.whatsapp,
                          method: 'whatsapp',
                          isIconFontAwesome: true,
                        ),

                        // Telegram option
                        _buildOtpMethodTile(
                          title: 'Telegram',
                          subtitle: '@username',
                          icon: FontAwesomeIcons.telegram,
                          method: 'telegram',
                          isIconFontAwesome: true,
                        ),

                        // Google Authenticator option
                        _buildOtpMethodTile(
                          title: 'Google Authenticator',
                          subtitle: _googleAuthSetup ? 'Configured' : 'Not configured',
                          icon: FontAwesomeIcons.google,
                          method: 'google_auth',
                          isIconFontAwesome: true,
                          onTap: () {
                            if (!_enabledOtpMethods.contains('google_auth')) {
                              _setupGoogleAuthenticator();
                            } else if (_enabledOtpMethods.length > 1) {
                              setState(() {
                                _enabledOtpMethods.remove('google_auth');
                              });
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('At least one OTP method must be enabled'),
                                ),
                              );
                            }
                          },
                        ),

                        const SizedBox(height: 8),
                        OutlinedButton.icon(
                          onPressed: () {
                            _showManageOtpMethodsDialog();
                          },
                          icon: const Icon(Icons.settings),
                          label: const Text('Manage OTP Methods'),
                          style: OutlinedButton.styleFrom(
                            minimumSize: const Size(double.infinity, 40),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.password),
                  title: const Text('Change Password'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: Navigate to change password
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Change Password - Coming Soon')),
                    );
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.security),
                  title: const Text('Security Questions'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // TODO: Navigate to security questions
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Security Questions - Coming Soon')),
                    );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Privacy section
          _buildSectionHeader(context, 'Privacy'),
          const SizedBox(height: 8),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.location_on),
                  title: const Text('Location Services'),
                  subtitle: const Text('Allow app to access your location'),
                  value: _locationEnabled,
                  onChanged: (value) {
                    setState(() {
                      _locationEnabled = value;
                    });
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.privacy_tip),
                  title: const Text('Privacy Policy'),
                  trailing: const Icon(Icons.open_in_new),
                  onTap: () {
                    // TODO: Open privacy policy
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Privacy Policy - Coming Soon')),
                    );
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.delete),
                  title: const Text('Delete Account'),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    // Show delete account confirmation
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Delete Account'),
                        content: const Text(
                          'Are you sure you want to delete your account? This action cannot be undone.',
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('Cancel'),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                              // TODO: Implement account deletion
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Account deletion - Coming Soon')),
                              );
                            },
                            style: TextButton.styleFrom(
                              foregroundColor: Colors.red,
                            ),
                            child: const Text('Delete'),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Privacy section
          _buildSectionHeader(
            context,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Privacy'
                : 'গোপনীয়তা'
          ),
          const SizedBox(height: 8),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.data_usage),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Data Collection'
                        : 'ডাটা সংগ্রহ'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Allow app to collect usage data'
                        : 'অ্যাপকে ব্যবহারের ডাটা সংগ্রহ করতে দিন'
                  ),
                  value: _dataCollectionEnabled,
                  onChanged: (value) {
                    setState(() {
                      _dataCollectionEnabled = value;
                      if (!value) {
                        _analyticsEnabled = false;
                        _personalizationEnabled = false;
                      }
                    });
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.analytics),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Analytics'
                        : 'অ্যানালিটিক্স'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Allow app to analyze usage patterns'
                        : 'অ্যাপকে ব্যবহারের প্যাটার্ন বিশ্লেষণ করতে দিন'
                  ),
                  value: _analyticsEnabled,

                  onChanged: _dataCollectionEnabled
                      ? (value) {
                          setState(() {
                            _analyticsEnabled = value;
                          });
                        }
                      : null,
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.person),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Personalization'
                        : 'ব্যক্তিগতকরণ'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Allow app to personalize your experience'
                        : 'অ্যাপকে আপনার অভিজ্ঞতা ব্যক্তিগতকরণ করতে দিন'
                  ),
                  value: _personalizationEnabled,

                  onChanged: _dataCollectionEnabled
                      ? (value) {
                          setState(() {
                            _personalizationEnabled = value;
                          });
                        }
                      : null,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Notification preferences section
          _buildSectionHeader(
            context,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Notification Preferences'
                : 'নোটিফিকেশন পছন্দসমূহ'
          ),
          const SizedBox(height: 8),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.email),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Email Notifications'
                        : 'ইমেইল নোটিফিকেশন'
                  ),
                  value: _emailNotificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _emailNotificationsEnabled = value;
                    });
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.notifications),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Push Notifications'
                        : 'পুশ নোটিফিকেশন'
                  ),
                  value: _pushNotificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _pushNotificationsEnabled = value;
                    });
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.sms),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'SMS Notifications'
                        : 'এসএমএস নোটিফিকেশন'
                  ),
                  value: _smsNotificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _smsNotificationsEnabled = value;
                    });
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.payment),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Transaction Alerts'
                        : 'লেনদেন সতর্কতা'
                  ),
                  value: _transactionAlertsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _transactionAlertsEnabled = value;
                    });
                  },
                ),
                const Divider(),
                SwitchListTile(
                  secondary: const Icon(Icons.local_offer),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Promotional Notifications'
                        : 'প্রচারমূলক নোটিফিকেশন'
                  ),
                  value: _promotionalNotificationsEnabled,
                  onChanged: (value) {
                    setState(() {
                      _promotionalNotificationsEnabled = value;
                    });
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // About section
          _buildSectionHeader(
            context,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'About'
                : 'সম্পর্কে'
          ),
          const SizedBox(height: 8),

          Card(
            child: Column(
              children: [
                SwitchListTile(
                  secondary: const Icon(Icons.system_update),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Auto Update'
                        : 'অটো আপডেট'
                  ),
                  subtitle: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Automatically update the app'
                        : 'স্বয়ংক্রিয়ভাবে অ্যাপ আপডেট করুন'
                  ),
                  value: _autoUpdateEnabled,
                  onChanged: (value) {
                    setState(() {
                      _autoUpdateEnabled = value;
                    });
                  },
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.info),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'App Version'
                        : 'অ্যাপ সংস্করণ'
                  ),
                  subtitle: const Text('1.0.0 (Build 100)'),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.support_agent),
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Support'
                        : 'সাপোর্ট'
                  ),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () {
                    context.push(RouteNames.support);
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Logout button
          ElevatedButton.icon(
            onPressed: () {
              // Show logout confirmation
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Logout'),
                  content: const Text('Are you sure you want to logout?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);

                        // Get the auth service
                        final authService = ref.read(authServiceProvider);

                        // Call the logout method
                        authService.logout().then((_) {
                          // Check if the widget is still mounted before using context
                          if (mounted) {
                            // Show success message
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Logged out successfully')),
                            );

                            // Navigate to login screen
                            // Use context.go instead of context.push to replace the current route stack
                            // This prevents going back to protected screens after logout
                            context.go(RouteNames.login);
                          }
                        });
                      },
                      child: const Text('Logout'),
                    ),
                  ],
                ),
              );
            },
            icon: const Icon(Icons.logout),
            label: const Text('Logout'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  /// Build a pro feature item
  Widget _buildProFeature(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.amber,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            feature,
            style: const TextStyle(
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a pro feature chip
  Widget _buildProFeatureChip(String feature) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.amber.withAlpha(50),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.amber,
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            feature,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build OTP method tile
  Widget _buildOtpMethodTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required String method,
    bool isIconFontAwesome = false,
    VoidCallback? onTap,
  }) {
    final isEnabled = _enabledOtpMethods.contains(method);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Material(
        borderRadius: BorderRadius.circular(8),
        color: isEnabled
            ? ColorConstants.primaryColor.withAlpha(25)
            : Colors.grey.withAlpha(13),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onTap ?? () {
            setState(() {
              if (isEnabled) {
                // Don't allow removing the last method
                if (_enabledOtpMethods.length > 1) {
                  _enabledOtpMethods.remove(method);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('At least one OTP method must be enabled'),
                    ),
                  );
                }
              } else {
                _enabledOtpMethods.add(method);
              }
            });
          },
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isEnabled
                        ? ColorConstants.primaryColor.withAlpha(50)
                        : Colors.grey.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: isIconFontAwesome
                        ? FaIcon(
                            icon,
                            color: isEnabled
                                ? ColorConstants.primaryColor
                                : Colors.grey,
                            size: 20,
                          )
                        : Icon(
                            icon,
                            color: isEnabled
                                ? ColorConstants.primaryColor
                                : Colors.grey,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontWeight: isEnabled ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: isEnabled ? ColorConstants.primaryColor : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: isEnabled,
                  activeColor: ColorConstants.primaryColor,
                  onChanged: (value) {
                    setState(() {
                      if (value) {
                        _enabledOtpMethods.add(method);
                      } else {
                        // Don't allow removing the last method
                        if (_enabledOtpMethods.length > 1) {
                          _enabledOtpMethods.remove(method);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('At least one OTP method must be enabled'),
                            ),
                          );
                        }
                      }
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Show dialog to manage OTP methods
  void _showManageOtpMethodsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Manage OTP Methods'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildOtpMethodEditTile(
                title: 'Phone Number',
                value: '+880 1712-345678',
                icon: Icons.phone,
                onEdit: () {
                  Navigator.pop(context);
                  _showEditOtpMethodDialog(
                    title: 'Edit Phone Number',
                    currentValue: '+880 1712-345678',
                    icon: Icons.phone,
                    method: 'phone',
                  );
                },
              ),
              _buildOtpMethodEditTile(
                title: 'Email',
                value: '<EMAIL>',
                icon: Icons.email,
                onEdit: () {
                  Navigator.pop(context);
                  _showEditOtpMethodDialog(
                    title: 'Edit Email Address',
                    currentValue: '<EMAIL>',
                    icon: Icons.email,
                    method: 'email',
                  );
                },
              ),
              _buildOtpMethodEditTile(
                title: 'WhatsApp',
                value: '+880 1712-345678',
                icon: FontAwesomeIcons.whatsapp,
                isIconFontAwesome: true,
                onEdit: () {
                  Navigator.pop(context);
                  _showEditOtpMethodDialog(
                    title: 'Edit WhatsApp Number',
                    currentValue: '+880 1712-345678',
                    icon: FontAwesomeIcons.whatsapp,
                    method: 'whatsapp',
                    isIconFontAwesome: true,
                  );
                },
              ),
              _buildOtpMethodEditTile(
                title: 'Telegram',
                value: '@username',
                icon: FontAwesomeIcons.telegram,
                isIconFontAwesome: true,
                onEdit: () {
                  Navigator.pop(context);
                  _showEditOtpMethodDialog(
                    title: 'Edit Telegram Username',
                    currentValue: '@username',
                    icon: FontAwesomeIcons.telegram,
                    method: 'telegram',
                    isIconFontAwesome: true,
                  );
                },
              ),
              if (_enabledOtpMethods.contains('google_auth')) ...[
                _buildOtpMethodEditTile(
                  title: 'Google Authenticator',
                  value: 'Configured',
                  icon: FontAwesomeIcons.google,
                  isIconFontAwesome: true,
                  onEdit: () {
                    Navigator.pop(context);
                    _setupGoogleAuthenticator(isReset: true);
                  },
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Build OTP method edit tile
  Widget _buildOtpMethodEditTile({
    required String title,
    required String value,
    required IconData icon,
    required VoidCallback onEdit,
    bool isIconFontAwesome = false,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: ColorConstants.primaryColor.withAlpha(25),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: isIconFontAwesome
              ? FaIcon(
                  icon,
                  color: ColorConstants.primaryColor,
                  size: 20,
                )
              : Icon(
                  icon,
                  color: ColorConstants.primaryColor,
                ),
        ),
      ),
      title: Text(title),
      subtitle: Text(value),
      trailing: IconButton(
        icon: const Icon(Icons.edit),
        onPressed: onEdit,
      ),
    );
  }

  /// Show dialog to edit OTP method
  void _showEditOtpMethodDialog({
    required String title,
    required String currentValue,
    required IconData icon,
    required String method,
    bool isIconFontAwesome = false,
  }) {
    final TextEditingController controller = TextEditingController(text: currentValue);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: ColorConstants.primaryColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: isIconFontAwesome
                        ? FaIcon(
                            icon,
                            color: ColorConstants.primaryColor,
                            size: 20,
                          )
                        : Icon(
                            icon,
                            color: ColorConstants.primaryColor,
                          ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: controller,
                    decoration: InputDecoration(
                      labelText: method == 'email'
                          ? 'Email Address'
                          : method == 'telegram'
                              ? 'Username'
                              : 'Phone Number',
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'You will receive a verification code to confirm this change',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // In a real app, you would send a verification code
              // and update the value after verification
              Navigator.pop(context);

              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Verification code sent to ${controller.text}'),
                ),
              );

              // Simulate verification dialog
              Future.delayed(const Duration(seconds: 1), () {
                _showVerificationDialog(method: method);
              });
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  /// Show verification dialog
  void _showVerificationDialog({required String method}) {
    final TextEditingController otpController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Verify OTP'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter the verification code sent to your device',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: otpController,
              decoration: const InputDecoration(
                labelText: 'Verification Code',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              maxLength: 6,
            ),
            const SizedBox(height: 8),
            const Text(
              'For demo purposes, enter any 6 digits',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);

              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${method.capitalize()} updated successfully'),
                ),
              );
            },
            child: const Text('Verify'),
          ),
        ],
      ),
    );
  }

  /// Setup Google Authenticator
  void _setupGoogleAuthenticator({bool isReset = false}) {
    // Generate a random secret key (in a real app, this would be securely generated)
    final secretKey = 'ABCDEFGHIJKLMNOP';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isReset ? 'Reset Google Authenticator' : 'Setup Google Authenticator'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Scan this QR code with the Google Authenticator app:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Image.network(
                    'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/iRecharge:<EMAIL>?secret=$secretKey&issuer=iRecharge',
                    width: 180,
                    height: 180,
                    errorBuilder: (context, error, stackTrace) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.qr_code, size: 80, color: Colors.grey),
                          const SizedBox(height: 8),
                          const Text('QR Code', style: TextStyle(color: Colors.grey)),
                        ],
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Or enter this code manually:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withAlpha(25),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      secretKey,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.5,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy, size: 16),
                      onPressed: () {
                        // Copy to clipboard
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Secret key copied to clipboard')),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              const Text(
                'After scanning, enter the 6-digit code from the app:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Verification Code',
                  border: OutlineInputBorder(),
                  hintText: '000000',
                ),
                keyboardType: TextInputType.number,
                maxLength: 6,
              ),
              const SizedBox(height: 8),
              const Text(
                'For demo purposes, enter any 6 digits',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);

              // Enable Google Authenticator
              setState(() {
                _googleAuthSetup = true;
                _enabledOtpMethods.add('google_auth');
              });

              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(isReset
                    ? 'Google Authenticator reset successfully'
                    : 'Google Authenticator configured successfully'),
                ),
              );
            },
            child: const Text('Verify'),
          ),
        ],
      ),
    );
  }

  /// Show PIN setup dialog
  void _showPinSetupDialog(BuildContext context) {
    final pinController = TextEditingController();
    final confirmPinController = TextEditingController();
    bool obscurePin = true;
    bool obscureConfirmPin = true;
    String? errorText;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Set PIN'
                    : 'পিন সেট করুন',
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // PIN field
                  TextField(
                    controller: pinController,
                    obscureText: obscurePin,
                    keyboardType: TextInputType.number,
                    maxLength: 6,
                    decoration: InputDecoration(
                      labelText: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Enter PIN'
                          : 'পিন লিখুন',
                      errorText: errorText,
                      suffixIcon: IconButton(
                        icon: Icon(
                          obscurePin ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            obscurePin = !obscurePin;
                          });
                        },
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Confirm PIN field
                  TextField(
                    controller: confirmPinController,
                    obscureText: obscureConfirmPin,
                    keyboardType: TextInputType.number,
                    maxLength: 6,
                    decoration: InputDecoration(
                      labelText: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Confirm PIN'
                          : 'পিন নিশ্চিত করুন',
                      suffixIcon: IconButton(
                        icon: Icon(
                          obscureConfirmPin ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            obscureConfirmPin = !obscureConfirmPin;
                          });
                        },
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // Disable PIN if canceled
                    this.setState(() {
                      _pinEnabled = false;
                    });
                  },
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Cancel'
                        : 'বাতিল',
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Validate PIN
                    if (pinController.text.isEmpty || pinController.text.length < 4) {
                      setState(() {
                        errorText = ref.watch(languageProvider) == AppLanguage.english
                            ? 'PIN must be at least 4 digits'
                            : 'পিন অন্তত ৪ সংখ্যার হতে হবে';
                      });
                      return;
                    }

                    if (pinController.text != confirmPinController.text) {
                      setState(() {
                        errorText = ref.watch(languageProvider) == AppLanguage.english
                            ? 'PINs do not match'
                            : 'পিন মিলছে না';
                      });
                      return;
                    }

                    // PIN is valid, save it
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'PIN set successfully'
                              : 'পিন সফলভাবে সেট করা হয়েছে',
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Set PIN'
                        : 'পিন সেট করুন',
                  ),
                ),
              ],
            );
          },
        );
      },
    ).then((_) {
      // Dispose controllers when dialog is closed
      pinController.dispose();
      confirmPinController.dispose();
    });
  }

  /// Build section header
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }
}

/// Extension to capitalize first letter of a string
extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return this;
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}

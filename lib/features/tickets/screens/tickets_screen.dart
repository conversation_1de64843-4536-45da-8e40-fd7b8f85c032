import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Ticket type enum
enum TicketType {
  air,
  bus,
  train,
}

/// Ticket provider model
class TicketProvider {
  final String id;
  final String name;
  final String logoPath;
  final Color color;
  final List<TicketType> supportedTicketTypes;

  const TicketProvider({
    required this.id,
    required this.name,
    required this.logoPath,
    required this.color,
    required this.supportedTicketTypes,
  });
}

/// Tickets screen
class TicketsScreen extends ConsumerStatefulWidget {
  const TicketsScreen({super.key});

  @override
  ConsumerState<TicketsScreen> createState() => _TicketsScreenState();
}

class _TicketsScreenState extends ConsumerState<TicketsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Selected ticket type
  TicketType _selectedTicketType = TicketType.air;

  // Form controllers
  final _fromController = TextEditingController();
  final _toController = TextEditingController();
  final _dateController = TextEditingController();
  final _passengersController = TextEditingController(text: '1');

  // Form key
  final _formKey = GlobalKey<FormState>();

  // Mock ticket providers
  final List<TicketProvider> _ticketProviders = [
    TicketProvider(
      id: 'biman',
      name: 'Biman Bangladesh',
      logoPath: 'assets/images/tickets/biman.png',
      color: Colors.green,
      supportedTicketTypes: [TicketType.air],
    ),
    TicketProvider(
      id: 'usairways',
      name: 'US Airways',
      logoPath: 'assets/images/tickets/usairways.png',
      color: Colors.blue,
      supportedTicketTypes: [TicketType.air],
    ),
    TicketProvider(
      id: 'emirates',
      name: 'Emirates',
      logoPath: 'assets/images/tickets/emirates.png',
      color: Colors.red,
      supportedTicketTypes: [TicketType.air],
    ),
    TicketProvider(
      id: 'hanif',
      name: 'Hanif Enterprise',
      logoPath: 'assets/images/tickets/hanif.png',
      color: Colors.orange,
      supportedTicketTypes: [TicketType.bus],
    ),
    TicketProvider(
      id: 'shyamoli',
      name: 'Shyamoli Paribahan',
      logoPath: 'assets/images/tickets/shyamoli.png',
      color: Colors.purple,
      supportedTicketTypes: [TicketType.bus],
    ),
    TicketProvider(
      id: 'greenline',
      name: 'Green Line',
      logoPath: 'assets/images/tickets/greenline.png',
      color: Colors.green,
      supportedTicketTypes: [TicketType.bus],
    ),
    TicketProvider(
      id: 'bangladesh_railway',
      name: 'Bangladesh Railway',
      logoPath: 'assets/images/tickets/railway.png',
      color: Colors.indigo,
      supportedTicketTypes: [TicketType.train],
    ),
  ];

  // Recent bookings
  final List<Map<String, dynamic>> _recentBookings = [
    {
      'provider': 'Biman Bangladesh',
      'from': 'Dhaka (DAC)',
      'to': 'Chittagong (CGP)',
      'date': DateTime.now().add(const Duration(days: 15)),
      'passengers': 1,
      'ticketType': TicketType.air,
      'status': 'Confirmed',
      'price': 3500.0,
    },
    {
      'provider': 'Hanif Enterprise',
      'from': 'Dhaka',
      'to': 'Sylhet',
      'date': DateTime.now().add(const Duration(days: 5)),
      'passengers': 2,
      'ticketType': TicketType.bus,
      'status': 'Confirmed',
      'price': 1200.0,
    },
    {
      'provider': 'Bangladesh Railway',
      'from': 'Dhaka',
      'to': 'Rajshahi',
      'date': DateTime.now().add(const Duration(days: 10)),
      'passengers': 3,
      'ticketType': TicketType.train,
      'status': 'Pending',
      'price': 1800.0,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Add listener to tab controller
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          switch (_tabController.index) {
            case 0:
              _selectedTicketType = TicketType.air;
              break;
            case 1:
              _selectedTicketType = TicketType.bus;
              break;
            case 2:
              _selectedTicketType = TicketType.train;
              break;
          }
        });
      }
    });

    // Set initial date to tomorrow
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    _dateController.text = '${tomorrow.day}/${tomorrow.month}/${tomorrow.year}';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fromController.dispose();
    _toController.dispose();
    _dateController.dispose();
    _passengersController.dispose();
    super.dispose();
  }

  /// Get filtered ticket providers
  List<TicketProvider> _getFilteredProviders() {
    return _ticketProviders.where((provider) {
      return provider.supportedTicketTypes.contains(_selectedTicketType);
    }).toList();
  }

  /// Get ticket type icon
  IconData _getTicketTypeIcon(TicketType type) {
    switch (type) {
      case TicketType.air:
        return Icons.flight;
      case TicketType.bus:
        return Icons.directions_bus;
      case TicketType.train:
        return Icons.train;
    }
  }

  /// Get ticket type name
  String _getTicketTypeName(TicketType type) {
    switch (type) {
      case TicketType.air:
        return 'Air';
      case TicketType.bus:
        return 'Bus';
      case TicketType.train:
        return 'Train';
    }
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Show date picker
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: ColorConstants.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dateController.text = _formatDate(picked);
      });
    }
  }

  /// Search for tickets
  void _searchTickets() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show search results
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Searching for ${_getTicketTypeName(_selectedTicketType)} tickets...'),
      ),
    );

    // In a real app, this would navigate to a search results page
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('tickets')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Air Tickets'),
            Tab(text: 'Bus Tickets'),
            Tab(text: 'Train Tickets'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Air Tickets tab
          _buildTicketTab(),

          // Bus Tickets tab
          _buildTicketTab(),

          // Train Tickets tab
          _buildTicketTab(),
        ],
      ),
    );
  }

  /// Build ticket tab
  Widget _buildTicketTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search form
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Search Tickets',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // From field
                    CustomTextField(
                      controller: _fromController,
                      labelText: 'From',
                      hintText: 'Enter departure city',
                      prefixIcon: const Icon(Icons.flight_takeoff),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter departure city';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // To field
                    CustomTextField(
                      controller: _toController,
                      labelText: 'To',
                      hintText: 'Enter destination city',
                      prefixIcon: const Icon(Icons.flight_land),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter destination city';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Date field
                    GestureDetector(
                      onTap: _selectDate,
                      child: AbsorbPointer(
                        child: CustomTextField(
                          controller: _dateController,
                          labelText: 'Date',
                          hintText: 'Select date',
                          prefixIcon: const Icon(Icons.calendar_today),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a date';
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Passengers field
                    CustomTextField(
                      controller: _passengersController,
                      labelText: 'Passengers',
                      hintText: 'Number of passengers',
                      keyboardType: TextInputType.number,
                      prefixIcon: const Icon(Icons.person),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter number of passengers';
                        }
                        if (int.tryParse(value) == null || int.parse(value) < 1) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),

                    // Search button
                    CustomButton(
                      text: 'Search Tickets',
                      onPressed: _searchTickets,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Available providers
          const Text(
            'Available Providers',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Provider grid
          _getFilteredProviders().isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('No providers available for this ticket type'),
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                  ),
                  itemCount: _getFilteredProviders().length,
                  itemBuilder: (context, index) {
                    final provider = _getFilteredProviders()[index];
                    return Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundColor: provider.color.withAlpha(30),
                            child: Icon(
                              _getTicketTypeIcon(_selectedTicketType),
                              color: provider.color,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            provider.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    );
                  },
                ),

          const SizedBox(height: 24),

          // Recent bookings
          if (_recentBookings.isNotEmpty) ...[
            const Text(
              'Recent Bookings',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Recent bookings list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentBookings.length,
              itemBuilder: (context, index) {
                final booking = _recentBookings[index];
                final ticketType = booking['ticketType'] as TicketType;

                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _getTicketTypeIcon(ticketType),
                                  color: ColorConstants.primaryColor,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  booking['provider'],
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: booking['status'] == 'Confirmed'
                                    ? Colors.green.withAlpha(30)
                                    : Colors.orange.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                booking['status'],
                                style: TextStyle(
                                  color: booking['status'] == 'Confirmed'
                                      ? Colors.green
                                      : Colors.orange,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'From',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    booking['from'],
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Icon(
                              Icons.arrow_forward,
                              color: Colors.grey,
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  const Text(
                                    'To',
                                    style: TextStyle(
                                      color: Colors.grey,
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    booking['to'],
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Date',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                Text(
                                  _formatDate(booking['date']),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                const Text(
                                  'Passengers',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                Text(
                                  '${booking['passengers']}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Price',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              '৳${booking['price'].toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: ColorConstants.primaryColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton.icon(
                              onPressed: () {
                                // TODO: View ticket details
                              },
                              icon: const Icon(Icons.visibility),
                              label: const Text('View Ticket'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/constants/asset_constants.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Onboarding screen shown to first-time users
class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  // Onboarding data
  final List<Map<String, String>> _onboardingData = [
    {
      'title': 'Mobile Recharge & Packages',
      'description': 'Recharge any mobile number and activate data, minutes, or combo packages with ease.',
      'image': AssetConstants.onboarding1,
    },
    {
      'title': 'Mobile Banking & Bill Payments',
      'description': 'Send money, pay bills, and manage your finances securely from one place.',
      'image': AssetConstants.onboarding2,
    },
    {
      'title': 'Shop & Earn Rewards',
      'description': 'Shop for your favorite products and earn rewards with every transaction.',
      'image': AssetConstants.onboarding3,
    },
  ];
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: TextButton(
                onPressed: () => _navigateToLogin(),
                child: const Text('Skip'),
              ),
            ),
            
            // Page view
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _onboardingData.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return _buildOnboardingPage(
                    title: _onboardingData[index]['title']!,
                    description: _onboardingData[index]['description']!,
                    image: _onboardingData[index]['image']!,
                  );
                },
              ),
            ),
            
            // Page indicator
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _onboardingData.length,
                (index) => _buildDotIndicator(index),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Navigation buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button (hidden on first page)
                  _currentPage > 0
                      ? CustomButton(
                          text: 'Back',
                          onPressed: () {
                            _pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                            );
                          },
                          isOutlined: true,
                          isFullWidth: false,
                          width: 100,
                        )
                      : const SizedBox(width: 100),
                  
                  // Next/Get Started button
                  CustomButton(
                    text: _currentPage == _onboardingData.length - 1
                        ? 'Get Started'
                        : 'Next',
                    onPressed: () {
                      if (_currentPage == _onboardingData.length - 1) {
                        _navigateToLogin();
                      } else {
                        _pageController.nextPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      }
                    },
                    isFullWidth: false,
                    width: 150,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build an onboarding page
  Widget _buildOnboardingPage({
    required String title,
    required String description,
    required String image,
  }) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image
          Image.asset(
            image,
            height: 250,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 250,
                width: 250,
                decoration: BoxDecoration(
                  color: ColorConstants.primaryLightColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.image,
                  size: 80,
                  color: ColorConstants.primaryColor,
                ),
              );
            },
          ),
          
          const SizedBox(height: 40),
          
          // Title
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorConstants.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // Description
          Text(
            description,
            style: const TextStyle(
              fontSize: 16,
              color: ColorConstants.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  /// Build a dot indicator for the page view
  Widget _buildDotIndicator(int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: _currentPage == index ? 16 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: _currentPage == index
            ? ColorConstants.primaryColor
            : ColorConstants.textLightColor,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
  
  /// Navigate to the login screen
  void _navigateToLogin() {
    // TODO: Save that onboarding has been shown
    context.go(RouteNames.login);
  }
}

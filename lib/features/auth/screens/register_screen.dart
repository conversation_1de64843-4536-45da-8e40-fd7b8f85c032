import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Verification method enum
enum VerificationMethod { phone, email, whatsapp, telegram }

/// Register screen
class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _telegramController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _otpController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;
  bool _acceptTerms = false;

  // OTP verification
  bool _otpSent = false;

  // Selected verification method
  VerificationMethod _selectedVerificationMethod = VerificationMethod.phone;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _telegramController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorConstants.errorColor,
      ),
    );
  }

  /// Show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Handle request OTP button press
  Future<void> _handleRequestOTP() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_acceptTerms) {
      _showErrorSnackBar('Please accept the terms and conditions');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // In a real app, you would call the auth service to request OTP
      // For now, we'll just simulate success
      final success = true; // Simulating successful OTP request

      if (!mounted) return;

      if (success) {
        setState(() {
          _otpSent = true;
          _isLoading = false;
        });

        String destination = '';
        switch (_selectedVerificationMethod) {
          case VerificationMethod.phone:
            destination = 'phone number';
            break;
          case VerificationMethod.email:
            destination = 'email address';
            break;
          case VerificationMethod.whatsapp:
            destination = 'WhatsApp number';
            break;
          case VerificationMethod.telegram:
            destination = 'Telegram account';
            break;
        }

        _showSuccessSnackBar('OTP sent to your $destination');
      } else {
        _showErrorSnackBar('Failed to send OTP. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('An error occurred. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Build verification method card
  Widget _buildVerificationMethodCard({
    required VerificationMethod method,
    required IconData icon,
    required String label,
    bool isFontAwesome = false,
  }) {
    final isSelected = _selectedVerificationMethod == method;

    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.20, // Even smaller width
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedVerificationMethod = method;
          });
        },
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorConstants.primaryColor.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isSelected
                  ? ColorConstants.primaryColor
                  : Colors.transparent,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              isFontAwesome
                  ? FaIcon(
                      icon,
                      size: 16,
                      color: isSelected
                          ? ColorConstants.primaryColor
                          : Colors.grey,
                    )
                  : Icon(
                      icon,
                      size: 16,
                      color: isSelected
                          ? ColorConstants.primaryColor
                          : Colors.grey,
                    ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected
                      ? ColorConstants.primaryColor
                      : Colors.grey,
                  fontWeight: isSelected
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show terms and conditions dialog
  void _showTermsAndConditionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.description, color: ColorConstants.primaryColor),
            const SizedBox(width: 8),
            const Text(
              'Terms and Conditions',
              style: TextStyle(
                color: ColorConstants.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTermsSection(
                  title: '1. Acceptance of Terms',
                  content:
                      'By accessing and using iRecharge Pro, you agree to be bound by these Terms and Conditions and our Privacy Policy. If you do not agree to these terms, please do not use our services.',
                ),
                _buildTermsSection(
                  title: '2. User Accounts',
                  content:
                      'You are responsible for maintaining the confidentiality of your account information and password. You agree to accept responsibility for all activities that occur under your account.',
                ),
                _buildTermsSection(
                  title: '3. Service Usage',
                  content:
                      'Our services are provided "as is" without warranties of any kind. We reserve the right to modify, suspend, or discontinue any part of our services at any time.',
                ),
                _buildTermsSection(
                  title: '4. User Conduct',
                  content:
                      'You agree not to use our services for any illegal or unauthorized purpose. You must not attempt to gain unauthorized access to any part of our services.',
                ),
                _buildTermsSection(
                  title: '5. Limitation of Liability',
                  content:
                      'We shall not be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use of or inability to use our services.',
                ),
                _buildTermsSection(
                  title: '6. Governing Law',
                  content:
                      'These Terms shall be governed by and construed in accordance with the laws of Bangladesh, without regard to its conflict of law provisions.',
                ),
                _buildTermsSection(
                  title: '7. Changes to Terms',
                  content:
                      'We reserve the right to modify these Terms at any time. We will provide notice of any material changes by updating the "Last Updated" date at the top of this page.',
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.check_circle),
            label: const Text('I Understand'),
            style: TextButton.styleFrom(
              foregroundColor: ColorConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Build a section for terms and privacy policy
  Widget _buildTermsSection({required String title, required String content}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 15,
              color: ColorConstants.primaryColor,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            content,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// Show privacy policy dialog
  void _showPrivacyPolicyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.privacy_tip, color: ColorConstants.primaryColor),
            const SizedBox(width: 8),
            const Text(
              'Privacy Policy',
              style: TextStyle(
                color: ColorConstants.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.6,
          ),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTermsSection(
                  title: '1. Information We Collect',
                  content:
                      'We collect personal information such as your name, email address, phone number, and payment information when you register and use our services.',
                ),
                _buildTermsSection(
                  title: '2. How We Use Your Information',
                  content:
                      'We use your information to provide and improve our services, process transactions, communicate with you, and ensure security of your account.',
                ),
                _buildTermsSection(
                  title: '3. Information Sharing',
                  content:
                      'We do not sell or rent your personal information to third parties. We may share your information with service providers who help us deliver our services.',
                ),
                _buildTermsSection(
                  title: '4. Data Security',
                  content:
                      'We implement appropriate security measures to protect your personal information from unauthorized access, alteration, disclosure, or destruction.',
                ),
                _buildTermsSection(
                  title: '5. Your Rights',
                  content:
                      'You have the right to access, correct, or delete your personal information. You can manage your communication preferences and opt out of marketing communications.',
                ),
                _buildTermsSection(
                  title: '6. Cookies and Tracking',
                  content:
                      'We use cookies and similar tracking technologies to track activity on our service and hold certain information to improve and analyze our service.',
                ),
                _buildTermsSection(
                  title: '7. Children\'s Privacy',
                  content:
                      'Our service is not intended for use by children under the age of 13. We do not knowingly collect personal information from children under 13.',
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.check_circle),
            label: const Text('I Understand'),
            style: TextButton.styleFrom(
              foregroundColor: ColorConstants.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle verify OTP button press
  Future<void> _handleVerifyOTP() async {
    if (_otpController.text.isEmpty) {
      _showErrorSnackBar('Please enter the OTP');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // In a real app, you would verify the OTP with the backend
      // For now, we'll just simulate success if OTP is "123456"
      final success = _otpController.text == "123456";

      if (!mounted) return;

      if (success) {
        setState(() {
          _isLoading = false;
        });
        _showSuccessSnackBar('OTP verified successfully');

        // Proceed with registration
        _handleRegister();
      } else {
        _showErrorSnackBar('Invalid OTP. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('An error occurred. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Handle register button press
  Future<void> _handleRegister() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final userData = {
        'name': _nameController.text,
        'phone': _phoneController.text,
        'email': _emailController.text,
        'password': _passwordController.text,
      };

      final success = await authService.register(userData);

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Registration successful! Please login.'),
            backgroundColor: ColorConstants.successColor,
          ),
        );
        context.go(RouteNames.login);
      } else {
        _showErrorSnackBar('Registration failed. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('An error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageNotifier = ref.watch(languageProvider.notifier);
    final currentLanguage = ref.watch(languageProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Register'),
        actions: [
          // Language selector
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: PopupMenuButton<AppLanguage>(
              initialValue: currentLanguage,
              onSelected: (AppLanguage language) {
                languageNotifier.setLanguage(language);
              },
              itemBuilder: (BuildContext context) => AppLanguage.values
                  .map((language) => PopupMenuItem<AppLanguage>(
                        value: language,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(language.name),
                            const SizedBox(width: 8),
                            if (language == currentLanguage)
                              const Icon(
                                Icons.check,
                                color: ColorConstants.primaryColor,
                                size: 16,
                              ),
                          ],
                        ),
                      ))
                  .toList(),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.language, size: 16),
                    const SizedBox(width: 4),
                    Text(currentLanguage.name),
                    const SizedBox(width: 4),
                    const Icon(Icons.arrow_drop_down, size: 16),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // App logo
                const Center(
                  child: Icon(
                    Icons.account_balance_wallet,
                    size: 60,
                    color: ColorConstants.primaryColor,
                  ),
                ),

                const SizedBox(height: 16),

                // App name
                const Center(
                  child: Text(
                    AppConstants.appName,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.primaryColor,
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Registration form
                Text(
                  _otpSent ? 'Verify Your Account' : 'Create a new account',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 16),

                Text(
                  _otpSent
                      ? 'Enter the OTP sent to your selected contact method'
                      : 'Fill in your details to create an account',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                if (!_otpSent) ...[
                  // Full name field
                  CustomTextField(
                    controller: _nameController,
                    labelText: 'Full Name',
                    hintText: 'Enter your full name',
                    keyboardType: TextInputType.name,
                    prefixIcon: const Icon(Icons.person),
                    validator: (value) => Validators.validateRequired(value, 'Full name'),
                  ),

                  const SizedBox(height: 16),

                  // Phone number field
                  CustomTextField(
                    controller: _phoneController,
                    labelText: 'Phone Number',
                    hintText: 'Enter your phone number',
                    keyboardType: TextInputType.phone,
                    prefixIcon: const Icon(Icons.phone),
                    validator: Validators.validateBangladeshPhone,
                  ),

                  const SizedBox(height: 16),

                  // Email field
                  CustomTextField(
                    controller: _emailController,
                    labelText: 'Email',
                    hintText: 'Enter your email',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: const Icon(Icons.email),
                    validator: Validators.validateEmail,
                  ),

                  const SizedBox(height: 16),

                  // Password field
                  CustomTextField(
                    controller: _passwordController,
                    labelText: 'Password',
                    hintText: 'Enter your password',
                    obscureText: _obscurePassword,
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    validator: Validators.validatePassword,
                  ),

                  const SizedBox(height: 16),

                  // Confirm password field
                  CustomTextField(
                    controller: _confirmPasswordController,
                    labelText: 'Confirm Password',
                    hintText: 'Confirm your password',
                    obscureText: _obscureConfirmPassword,
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your password';
                      }
                      if (value != _passwordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 24),

                  // Verification method selection
                  const Text(
                    'Select Verification Method',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Verification methods in a grid
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildVerificationMethodCard(
                        method: VerificationMethod.phone,
                        icon: Icons.phone,
                        label: 'Phone',
                      ),
                      _buildVerificationMethodCard(
                        method: VerificationMethod.email,
                        icon: Icons.email,
                        label: 'Email',
                      ),
                      _buildVerificationMethodCard(
                        method: VerificationMethod.whatsapp,
                        icon: FontAwesomeIcons.whatsapp,
                        label: 'WhatsApp',
                        isFontAwesome: true,
                      ),
                      _buildVerificationMethodCard(
                        method: VerificationMethod.telegram,
                        icon: FontAwesomeIcons.telegram,
                        label: 'Telegram',
                        isFontAwesome: true,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Terms and conditions checkbox with improved layout
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Transform.scale(
                        scale: 0.9,
                        child: Checkbox(
                          value: _acceptTerms,
                          onChanged: (value) {
                            setState(() {
                              _acceptTerms = value ?? false;
                            });
                          },
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                      Expanded(
                        child: Wrap(
                          alignment: WrapAlignment.start,
                          crossAxisAlignment: WrapCrossAlignment.center,
                          children: [
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  _acceptTerms = !_acceptTerms;
                                });
                              },
                              child: const Text(
                                'I accept the ',
                                style: TextStyle(fontSize: 13),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                _showTermsAndConditionsDialog();
                              },
                              child: const Text(
                                'Terms and Conditions',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: ColorConstants.primaryColor,
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                            const Text(
                              ' and ',
                              style: TextStyle(fontSize: 13),
                            ),
                            GestureDetector(
                              onTap: () {
                                _showPrivacyPolicyDialog();
                              },
                              child: const Text(
                                'Privacy Policy',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: ColorConstants.primaryColor,
                                  fontWeight: FontWeight.bold,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // OTP verification UI
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: ColorConstants.primaryColor.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: ColorConstants.primaryColor.withOpacity(0.2),
                      ),
                    ),
                    child: Column(
                      children: [
                        // OTP field
                        CustomTextField(
                          controller: _otpController,
                          labelText: 'OTP',
                          hintText: 'Enter the OTP sent to you',
                          keyboardType: TextInputType.number,
                          prefixIcon: const Icon(Icons.sms),
                          maxLength: 6,
                        ),

                        const SizedBox(height: 8),

                        // Resend OTP button
                        TextButton.icon(
                          onPressed: _isLoading ? null : _handleRequestOTP,
                          icon: const Icon(Icons.refresh, size: 16),
                          label: const Text('Resend OTP'),
                        ),

                        const SizedBox(height: 8),

                        // Verification method info
                        Row(
                          children: [
                            _selectedVerificationMethod == VerificationMethod.phone
                                ? const Icon(Icons.phone, size: 16, color: Colors.grey)
                                : _selectedVerificationMethod == VerificationMethod.email
                                    ? const Icon(Icons.email, size: 16, color: Colors.grey)
                                    : _selectedVerificationMethod == VerificationMethod.whatsapp
                                        ? const FaIcon(FontAwesomeIcons.whatsapp, size: 16, color: Colors.grey)
                                        : const FaIcon(FontAwesomeIcons.telegram, size: 16, color: Colors.grey),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _selectedVerificationMethod == VerificationMethod.phone
                                    ? 'OTP sent to: ${_phoneController.text}'
                                    : _selectedVerificationMethod == VerificationMethod.email
                                        ? 'OTP sent to: ${_emailController.text}'
                                        : _selectedVerificationMethod == VerificationMethod.whatsapp
                                            ? 'OTP sent to WhatsApp: ${_phoneController.text}'
                                            : 'OTP sent to Telegram: ${_telegramController.text}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Hint for demo
                        const Text(
                          'Hint: Use "123456" as the OTP for this demo',
                          style: TextStyle(
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                const SizedBox(height: 24),

                // Action button
                CustomButton(
                  text: _otpSent ? 'Verify OTP' : 'Request OTP',
                  onPressed: _otpSent ? _handleVerifyOTP : _handleRequestOTP,
                  isLoading: _isLoading,
                ),

                const SizedBox(height: 24),

                // Login link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Already have an account?'),
                    TextButton(
                      onPressed: () {
                        context.go(RouteNames.login);
                      },
                      child: const Text('Login'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

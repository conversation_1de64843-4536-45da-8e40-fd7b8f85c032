import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/utils/logger_util.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Splash screen shown when the app starts
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToNextScreen();
  }

  /// Navigate to the appropriate screen after splash
  Future<void> _navigateToNextScreen() async {
    try {
      // Simulate loading time
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      // Check if user is logged in
      final authService = ref.read(authServiceProvider);
      final isLoggedIn = await authService.isLoggedIn();

      if (!mounted) return;

      // Navigate to the appropriate screen
      if (isLoggedIn) {
        context.go(RouteNames.home);
      } else {
        // Check if first time opening the app
        final isFirstTime = await _isFirstTimeOpeningApp();
        if (!mounted) return;

        if (isFirstTime) {
          context.go(RouteNames.onboarding);
        } else {
          context.go(RouteNames.login);
        }
      }
    } catch (e) {
      LoggerUtil.e('Error in splash navigation', e);
      if (mounted) {
        context.go(RouteNames.login);
      }
    }
  }

  /// Check if this is the first time opening the app
  Future<bool> _isFirstTimeOpeningApp() async {
    // TODO: Implement with StorageService
    return true; // For now, always show onboarding
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App logo
            const Icon(
              Icons.account_balance_wallet,
              size: 100,
              color: Colors.white,
            ),
            const SizedBox(height: 24),
            // App name
            const Text(
              AppConstants.appName,
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            // App version
            Text(
              'v${AppConstants.appVersion}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 48),
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}



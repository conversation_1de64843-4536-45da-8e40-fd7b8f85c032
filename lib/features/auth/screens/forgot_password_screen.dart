import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Recovery method enum
enum RecoveryMethod { phone, email, whatsapp, telegram }

/// Forgot password screen
class ForgotPasswordScreen extends ConsumerStatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  ConsumerState<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends ConsumerState<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _whatsappController = TextEditingController();
  final _telegramController = TextEditingController();
  bool _isLoading = false;
  bool _otpSent = false;
  final _otpController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // Selected recovery method
  RecoveryMethod _selectedRecoveryMethod = RecoveryMethod.phone;

  @override
  void dispose() {
    _phoneController.dispose();
    _emailController.dispose();
    _whatsappController.dispose();
    _telegramController.dispose();
    _otpController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Handle request OTP button press
  Future<void> _handleRequestOTP() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      String contactInfo = '';

      // Get the appropriate contact info based on selected recovery method
      switch (_selectedRecoveryMethod) {
        case RecoveryMethod.phone:
          contactInfo = _phoneController.text;
          break;
        case RecoveryMethod.email:
          contactInfo = _emailController.text;
          break;
        case RecoveryMethod.whatsapp:
          contactInfo = _whatsappController.text;
          break;
        case RecoveryMethod.telegram:
          contactInfo = _telegramController.text;
          break;
      }

      // In a real app, you would pass the recovery method to the backend
      // For now, we'll just use the existing method
      final success = await authService.requestPasswordReset(contactInfo);

      if (!mounted) return;

      if (success) {
        setState(() {
          _otpSent = true;
          _isLoading = false;
        });
        String destination = '';
        switch (_selectedRecoveryMethod) {
          case RecoveryMethod.phone:
            destination = 'phone number';
            break;
          case RecoveryMethod.email:
            destination = 'email address';
            break;
          case RecoveryMethod.whatsapp:
            destination = 'WhatsApp number';
            break;
          case RecoveryMethod.telegram:
            destination = 'Telegram account';
            break;
        }
        _showSuccessSnackBar('OTP sent to your $destination');
      } else {
        _showErrorSnackBar('Failed to send OTP. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('An error occurred. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Handle reset password button press
  Future<void> _handleResetPassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_newPasswordController.text != _confirmPasswordController.text) {
      _showErrorSnackBar('Passwords do not match');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      String contactInfo = '';

      // Get the appropriate contact info based on selected recovery method
      switch (_selectedRecoveryMethod) {
        case RecoveryMethod.phone:
          contactInfo = _phoneController.text;
          break;
        case RecoveryMethod.email:
          contactInfo = _emailController.text;
          break;
        case RecoveryMethod.whatsapp:
          contactInfo = _whatsappController.text;
          break;
        case RecoveryMethod.telegram:
          contactInfo = _telegramController.text;
          break;
      }

      // In a real app, you would pass the recovery method to the backend
      // For now, we'll just use the existing method
      final success = await authService.resetPassword(
        contactInfo,
        _otpController.text,
        _newPasswordController.text,
      );

      if (!mounted) return;

      if (success) {
        _showSuccessSnackBar('Password reset successful');

        // Navigate back to login screen after a short delay
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            context.go(RouteNames.login);
          }
        });
      } else {
        _showErrorSnackBar('Failed to reset password. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('An error occurred. Please try again.');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorConstants.errorColor,
      ),
    );
  }

  /// Show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Build recovery method card
  Widget _buildRecoveryMethodCard({
    required RecoveryMethod method,
    required IconData icon,
    required String label,
    bool isFontAwesome = false,
  }) {
    final isSelected = _selectedRecoveryMethod == method;

    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.20, // Even smaller width
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedRecoveryMethod = method;
          });
        },
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorConstants.primaryColor.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isSelected
                  ? ColorConstants.primaryColor
                  : Colors.transparent,
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              isFontAwesome
                  ? FaIcon(
                      icon,
                      size: 16,
                      color: isSelected
                          ? ColorConstants.primaryColor
                          : Colors.grey,
                    )
                  : Icon(
                      icon,
                      size: 16,
                      color: isSelected
                          ? ColorConstants.primaryColor
                          : Colors.grey,
                    ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected
                      ? ColorConstants.primaryColor
                      : Colors.grey,
                  fontWeight: isSelected
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Forgot Password'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header icon
                const Center(
                  child: CircleAvatar(
                    radius: 50,
                    backgroundColor: Color(0xFFE3F2FD),
                    child: Icon(
                      Icons.lock_reset,
                      size: 50,
                      color: ColorConstants.primaryColor,
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Title and description
                Text(
                  _otpSent ? 'Reset Your Password' : 'Forgot Password?',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                Text(
                  _otpSent
                      ? 'Enter the OTP sent to your selected contact method and create a new password'
                      : 'Select a recovery method and we\'ll send you an OTP to reset your password',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                if (!_otpSent) ...[
                  // Recovery method selection
                  const Text(
                    'Select Recovery Method',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Recovery method options
                  // Recovery methods in a grid
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildRecoveryMethodCard(
                        method: RecoveryMethod.phone,
                        icon: Icons.phone,
                        label: 'Phone',
                      ),
                      _buildRecoveryMethodCard(
                        method: RecoveryMethod.email,
                        icon: Icons.email,
                        label: 'Email',
                      ),
                      _buildRecoveryMethodCard(
                        method: RecoveryMethod.whatsapp,
                        icon: FontAwesomeIcons.whatsapp,
                        label: 'WhatsApp',
                        isFontAwesome: true,
                      ),
                      _buildRecoveryMethodCard(
                        method: RecoveryMethod.telegram,
                        icon: FontAwesomeIcons.telegram,
                        label: 'Telegram',
                        isFontAwesome: true,
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),
                ],

                // Contact input fields based on selected method
                if (_selectedRecoveryMethod == RecoveryMethod.phone) ...[
                  CustomTextField(
                    controller: _phoneController,
                    labelText: 'Phone Number',
                    hintText: 'Enter your phone number',
                    keyboardType: TextInputType.phone,
                    prefixIcon: const Icon(Icons.phone),
                    validator: Validators.validateBangladeshPhone,
                    enabled: !_otpSent, // Disable after OTP is sent
                  ),
                ] else if (_selectedRecoveryMethod == RecoveryMethod.email) ...[
                  CustomTextField(
                    controller: _emailController,
                    labelText: 'Email Address',
                    hintText: 'Enter your email address',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: const Icon(Icons.email),
                    validator: Validators.validateEmail,
                    enabled: !_otpSent, // Disable after OTP is sent
                  ),
                ] else if (_selectedRecoveryMethod == RecoveryMethod.whatsapp) ...[
                  CustomTextField(
                    controller: _whatsappController,
                    labelText: 'WhatsApp Number',
                    hintText: 'Enter your WhatsApp number',
                    keyboardType: TextInputType.phone,
                    prefixIcon: const FaIcon(FontAwesomeIcons.whatsapp),
                    validator: Validators.validateBangladeshPhone,
                    enabled: !_otpSent, // Disable after OTP is sent
                  ),
                ] else if (_selectedRecoveryMethod == RecoveryMethod.telegram) ...[
                  CustomTextField(
                    controller: _telegramController,
                    labelText: 'Telegram Username',
                    hintText: 'Enter your Telegram username',
                    keyboardType: TextInputType.text,
                    prefixIcon: const FaIcon(FontAwesomeIcons.telegram),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your Telegram username';
                      }
                      if (!value.startsWith('@') && value != value.toLowerCase()) {
                        return 'Username should start with @ and be lowercase';
                      }
                      return null;
                    },
                    enabled: !_otpSent, // Disable after OTP is sent
                  ),
                ],

                if (_otpSent) ...[
                  const SizedBox(height: 16),

                  // OTP field
                  CustomTextField(
                    controller: _otpController,
                    labelText: 'OTP',
                    hintText: 'Enter the OTP sent to your phone',
                    keyboardType: TextInputType.number,
                    prefixIcon: const Icon(Icons.sms),
                    validator: (value) => Validators.validateRequired(value, 'OTP'),
                    maxLength: 6,
                  ),

                  const SizedBox(height: 16),

                  // New password field
                  CustomTextField(
                    controller: _newPasswordController,
                    labelText: 'New Password',
                    hintText: 'Enter your new password',
                    obscureText: _obscurePassword,
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscurePassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    validator: Validators.validatePassword,
                  ),

                  const SizedBox(height: 16),

                  // Confirm password field
                  CustomTextField(
                    controller: _confirmPasswordController,
                    labelText: 'Confirm Password',
                    hintText: 'Confirm your new password',
                    obscureText: _obscureConfirmPassword,
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          _obscureConfirmPassword = !_obscureConfirmPassword;
                        });
                      },
                    ),
                    validator: (value) {
                      if (value != _newPasswordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                ],

                const SizedBox(height: 32),

                // Action button
                CustomButton(
                  text: _otpSent ? 'Reset Password' : 'Send OTP',
                  onPressed: _otpSent ? _handleResetPassword : _handleRequestOTP,
                  isLoading: _isLoading,
                ),

                const SizedBox(height: 16),

                // Back to login link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Remember your password?'),
                    TextButton(
                      onPressed: () {
                        context.go(RouteNames.login);
                      },
                      child: const Text('Login'),
                    ),
                  ],
                ),

                if (_otpSent) ...[
                  const SizedBox(height: 16),

                  // Resend OTP button
                  TextButton.icon(
                    onPressed: _isLoading ? null : _handleRequestOTP,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Resend OTP'),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

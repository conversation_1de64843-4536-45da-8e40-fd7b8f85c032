import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/providers/theme_provider.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Login screen
class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _loginIdController = TextEditingController(text: '01712345678');
  final _passwordController = TextEditingController(text: 'Password123');
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _showDemoCredentials = true;
  bool _isEmail = false;

  @override
  void dispose() {
    _loginIdController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Handle login button press
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final success = await authService.login(
        _loginIdController.text,
        _passwordController.text,
      );

      if (!mounted) return;

      if (success) {
        context.go(RouteNames.home);
      } else {
        _showErrorSnackBar('Invalid credentials. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('An error occurred. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorConstants.errorColor,
      ),
    );
  }

  /// Check if biometric authentication is available
  Future<void> _checkBiometricAvailability() async {
    try {
      final authService = ref.read(authServiceProvider);
      final isAvailable = await authService.isBiometricAvailable();

      if (isAvailable && mounted) {
        _showBiometricDialog();
      }
    } catch (e) {
      // Ignore errors, biometric auth is optional
    }
  }

  /// Show biometric authentication dialog
  void _showBiometricDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Biometric Login'),
        content: const Text('Would you like to use biometric authentication?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _authenticateWithBiometrics();
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }

  /// Authenticate with biometrics
  Future<void> _authenticateWithBiometrics() async {
    try {
      final authService = ref.read(authServiceProvider);
      final success = await authService.authenticateWithBiometrics();

      if (success && mounted) {
        context.go(RouteNames.home);
      }
    } catch (e) {
      // Ignore errors, biometric auth is optional
    }
  }

  @override
  void initState() {
    super.initState();
    // Check for biometric authentication after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkBiometricAvailability();
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageNotifier = ref.watch(languageProvider.notifier);
    final currentLanguage = ref.watch(languageProvider);

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Language selector
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<AppLanguage>(
                      initialValue: currentLanguage,
                      onSelected: (AppLanguage language) {
                        languageNotifier.setLanguage(language);
                      },
                      itemBuilder: (BuildContext context) => AppLanguage.values
                          .map((language) => PopupMenuItem<AppLanguage>(
                                value: language,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(language.name),
                                    const SizedBox(width: 8),
                                    if (language == currentLanguage)
                                      const Icon(
                                        Icons.check,
                                        color: ColorConstants.primaryColor,
                                        size: 16,
                                      ),
                                  ],
                                ),
                              ))
                          .toList(),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(Icons.language, size: 16),
                            const SizedBox(width: 4),
                            Text(currentLanguage.name),
                            const SizedBox(width: 4),
                            const Icon(Icons.arrow_drop_down, size: 16),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // App logo and name
                Column(
                  children: [
                    // Logo with gradient background
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [ColorConstants.primaryColor, ColorConstants.secondaryColor],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(50),
                        boxShadow: [
                          BoxShadow(
                            color: ColorConstants.primaryColor.withOpacity(0.3),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.account_balance_wallet,
                        size: 50,
                        color: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // App name with gradient text
                    ShaderMask(
                      shaderCallback: (bounds) => const LinearGradient(
                        colors: [ColorConstants.primaryColor, ColorConstants.secondaryColor],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds),
                      child: Text(
                        AppConstants.appName,
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Tagline
                    const Text(
                      'Your Complete Digital Wallet',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 40),

                // Login form
                Card(
                  elevation: 4,
                  shadowColor: Colors.black26,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Welcome Back',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: ColorConstants.primaryColor,
                          ),
                        ),

                        const Text(
                          'Login to your account',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Login ID field (Email or Phone)
                        CustomTextField(
                          controller: _loginIdController,
                          labelText: ref.watch(languageProvider) == AppLanguage.english
                              ? 'Email or Phone Number'
                              : 'ইমেইল বা ফোন নম্বর',
                          hintText: ref.watch(languageProvider) == AppLanguage.english
                              ? 'Enter your email or phone number'
                              : 'আপনার ইমেইল বা ফোন নম্বর লিখুন',
                          keyboardType: _isEmail
                              ? TextInputType.emailAddress
                              : TextInputType.phone,
                          textInputAction: TextInputAction.next,
                          prefixIcon: Icon(_isEmail ? Icons.email : Icons.phone),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return ref.watch(languageProvider) == AppLanguage.english
                                  ? 'Please enter your email or phone number'
                                  : 'অনুগ্রহ করে আপনার ইমেইল বা ফোন নম্বর লিখুন';
                            }

                            // Check if input is an email
                            final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                            if (emailRegex.hasMatch(value)) {
                              if (!_isEmail) {
                                setState(() {
                                  _isEmail = true;
                                });
                              }
                              return null; // Valid email
                            }

                            // If not email, validate as phone number
                            if (!_isEmail) {
                              return Validators.validateBangladeshPhone(value);
                            }

                            // Switch back to phone mode if email is invalid
                            setState(() {
                              _isEmail = false;
                            });
                            return Validators.validateBangladeshPhone(value);
                          },
                          onChanged: (value) {
                            // Auto-detect if input is email or phone
                            final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                            final isEmail = emailRegex.hasMatch(value);

                            if (isEmail != _isEmail) {
                              setState(() {
                                _isEmail = isEmail;
                              });
                            }
                          },
                        ),

                        const SizedBox(height: 16),

                        // Password field
                        CustomTextField(
                          controller: _passwordController,
                          labelText: ref.watch(languageProvider) == AppLanguage.english
                              ? 'Password'
                              : 'পাসওয়ার্ড',
                          hintText: ref.watch(languageProvider) == AppLanguage.english
                              ? 'Enter your password'
                              : 'আপনার পাসওয়ার্ড লিখুন',
                          obscureText: _obscurePassword,
                          textInputAction: TextInputAction.done,
                          keyboardType: TextInputType.visiblePassword,
                          prefixIcon: const Icon(Icons.lock),
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword ? Icons.visibility : Icons.visibility_off,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          validator: (value) => Validators.validateRequired(
                            value,
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Password'
                                : 'পাসওয়ার্ড'
                          ),
                          onSubmitted: (_) => _handleLogin(),
                        ),

                        const SizedBox(height: 8),

                        // Forgot password link
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: () {
                              context.push(RouteNames.forgotPassword);
                            },
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(50, 30),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'Forgot Password?'
                                  : 'পাসওয়ার্ড ভুলে গেছেন?',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Login button
                        CustomButton(
                          text: ref.watch(languageProvider) == AppLanguage.english
                              ? 'Login'
                              : 'লগইন',
                          onPressed: _handleLogin,
                          isLoading: _isLoading,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Demo credentials info
                if (_showDemoCredentials)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          ColorConstants.infoColor.withOpacity(0.1),
                          ColorConstants.infoColor.withOpacity(0.05),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: ColorConstants.infoColor.withOpacity(0.5)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.info_outline,
                                  color: ColorConstants.infoColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Demo Credentials',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: ColorConstants.infoColor,
                                  ),
                                ),
                              ],
                            ),
                            IconButton(
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              icon: const Icon(
                                Icons.close,
                                size: 16,
                                color: ColorConstants.infoColor,
                              ),
                              onPressed: () {
                                setState(() {
                                  _showDemoCredentials = false;
                                });
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            const Text(
                              'Login ID: ',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text('01712345678 or <EMAIL>'),
                            const Spacer(),
                            IconButton(
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              icon: const Icon(
                                Icons.copy,
                                size: 16,
                                color: ColorConstants.infoColor,
                              ),
                              onPressed: () {
                                Clipboard.setData(const ClipboardData(text: '01712345678'));
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Login ID copied to clipboard'),
                                    duration: Duration(seconds: 1),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Text(
                              'Password: ',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text('Password123'),
                            const Spacer(),
                            IconButton(
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              icon: const Icon(
                                Icons.copy,
                                size: 16,
                                color: ColorConstants.infoColor,
                              ),
                              onPressed: () {
                                Clipboard.setData(const ClipboardData(text: 'Password123'));
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Password copied to clipboard'),
                                    duration: Duration(seconds: 1),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 24),

                // Register link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? "Don't have an account?"
                          : "অ্যাকাউন্ট নেই?",
                      style: const TextStyle(color: Colors.grey),
                    ),
                    TextButton(
                      onPressed: () {
                        context.push(RouteNames.register);
                      },
                      child: Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Register'
                            : 'নিবন্ধন',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                // Biometric login option
                Center(
                  child: TextButton.icon(
                    onPressed: _authenticateWithBiometrics,
                    icon: const Icon(Icons.fingerprint),
                    label: Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Login with Biometrics'
                          : 'বায়োমেট্রিক্স দিয়ে লগইন করুন'
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

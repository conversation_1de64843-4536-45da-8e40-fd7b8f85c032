import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/providers/agent_customer_provider.dart';

/// Agent Customer Form Screen
class AgentCustomerFormScreen extends ConsumerStatefulWidget {
  final bool isEditing;
  final AgentCustomer? customer;

  const AgentCustomerFormScreen({
    super.key,
    this.isEditing = false,
    this.customer,
  });

  @override
  ConsumerState<AgentCustomerFormScreen> createState() => _AgentCustomerFormScreenState();
}

class _AgentCustomerFormScreenState extends ConsumerState<AgentCustomerFormScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _balanceController = TextEditingController();
  final _transactionsController = TextEditingController();
  final _notesController = TextEditingController();

  // Form values
  AgentCustomerStatus _status = AgentCustomerStatus.active;
  AgentCustomerType _type = AgentCustomerType.customer;

  @override
  void initState() {
    super.initState();

    // If editing, populate form with customer data
    if (widget.isEditing && widget.customer != null) {
      _nameController.text = widget.customer!.name;
      _phoneController.text = widget.customer!.phone;
      _emailController.text = widget.customer!.email ?? '';
      _addressController.text = widget.customer!.address ?? '';
      _balanceController.text = widget.customer!.balance.toString();
      _transactionsController.text = widget.customer!.totalTransactions.toString();
      _notesController.text = widget.customer!.notes ?? '';
      _status = widget.customer!.status;
      _type = widget.customer!.type;
    } else {
      // Default values for new customer
      _balanceController.text = '0.0';
      _transactionsController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _balanceController.dispose();
    _transactionsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// Save customer
  void _saveCustomer() {
    if (_formKey.currentState!.validate()) {
      final notifier = ref.read(agentCustomersProvider.notifier);

      // Create customer object
      final customer = AgentCustomer(
        id: widget.isEditing ? widget.customer!.id : notifier.generateCustomerId(),
        name: _nameController.text,
        phone: _phoneController.text,
        email: _emailController.text.isNotEmpty ? _emailController.text : null,
        address: _addressController.text.isNotEmpty ? _addressController.text : null,
        createdAt: widget.isEditing ? widget.customer!.createdAt : DateTime.now(),
        lastTransaction: widget.isEditing ? widget.customer!.lastTransaction : null,
        balance: double.parse(_balanceController.text),
        totalTransactions: int.parse(_transactionsController.text),
        status: _status,
        type: _type,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      // Save customer
      if (widget.isEditing) {
        notifier.updateCustomer(customer);
        ref.read(selectedAgentCustomerProvider.notifier).state = customer;
      } else {
        notifier.addCustomer(customer);
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            widget.isEditing
                ? ref.watch(languageProvider) == AppLanguage.english
                    ? 'Customer updated successfully'
                    : 'গ্রাহক সফলভাবে আপডেট করা হয়েছে'
                : ref.watch(languageProvider) == AppLanguage.english
                    ? 'Customer added successfully'
                    : 'গ্রাহক সফলভাবে যোগ করা হয়েছে',
          ),
          backgroundColor: Colors.green,
        ),
      );

      // Go back
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isEditing
              ? ref.watch(languageProvider) == AppLanguage.english
                  ? 'Edit Customer'
                  : 'গ্রাহক সম্পাদনা করুন'
              : ref.watch(languageProvider) == AppLanguage.english
                  ? 'Add Customer'
                  : 'গ্রাহক যোগ করুন',
        ),
        actions: [
          TextButton.icon(
            onPressed: _saveCustomer,
            icon: const Icon(Icons.save),
            label: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Save'
                  : 'সংরক্ষণ করুন',
            ),
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Information
              _buildSectionHeader(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Basic Information'
                    : 'মৌলিক তথ্য',
              ),
              const SizedBox(height: 16),

              // Name
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Name'
                      : 'নাম',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter a name'
                        : 'অনুগ্রহ করে একটি নাম লিখুন';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Phone
              TextFormField(
                controller: _phoneController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Phone'
                      : 'ফোন',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter a phone number'
                        : 'অনুগ্রহ করে একটি ফোন নম্বর লিখুন';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Email
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Email (Optional)'
                      : 'ইমেইল (ঐচ্ছিক)',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    // Simple email validation
                    if (!value.contains('@') || !value.contains('.')) {
                      return ref.watch(languageProvider) == AppLanguage.english
                          ? 'Please enter a valid email'
                          : 'অনুগ্রহ করে একটি বৈধ ইমেইল লিখুন';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Address
              TextFormField(
                controller: _addressController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Address (Optional)'
                      : 'ঠিকানা (ঐচ্ছিক)',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.location_on),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 24),

              // Account Information
              _buildSectionHeader(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Account Information'
                    : 'অ্যাকাউন্টের তথ্য',
              ),
              const SizedBox(height: 16),

              // Customer Type
              DropdownButtonFormField<AgentCustomerType>(
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Customer Type'
                      : 'গ্রাহকের ধরন',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.category),
                ),
                value: _type,
                items: AgentCustomerType.values.map((type) {
                  return DropdownMenuItem<AgentCustomerType>(
                    value: type,
                    child: Text(AgentCustomer.getTypeText(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _type = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Status
              DropdownButtonFormField<AgentCustomerStatus>(
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Status'
                      : 'স্ট্যাটাস',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.verified_user),
                ),
                value: _status,
                items: AgentCustomerStatus.values.map((status) {
                  return DropdownMenuItem<AgentCustomerStatus>(
                    value: status,
                    child: Text(AgentCustomer.getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _status = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Balance
              TextFormField(
                controller: _balanceController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Balance'
                      : 'ব্যালেন্স',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.account_balance_wallet),
                  prefixText: '৳',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter a balance'
                        : 'অনুগ্রহ করে একটি ব্যালেন্স লিখুন';
                  }
                  try {
                    double.parse(value);
                  } catch (e) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter a valid number'
                        : 'অনুগ্রহ করে একটি বৈধ সংখ্যা লিখুন';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Total Transactions
              TextFormField(
                controller: _transactionsController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Total Transactions'
                      : 'মোট লেনদেন',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.swap_horiz),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter total transactions'
                        : 'অনুগ্রহ করে মোট লেনদেন লিখুন';
                  }
                  try {
                    int.parse(value);
                  } catch (e) {
                    return ref.watch(languageProvider) == AppLanguage.english
                        ? 'Please enter a valid number'
                        : 'অনুগ্রহ করে একটি বৈধ সংখ্যা লিখুন';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Notes
              _buildSectionHeader(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Additional Information'
                    : 'অতিরিক্ত তথ্য',
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: _notesController,
                decoration: InputDecoration(
                  labelText: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Notes (Optional)'
                      : 'নোট (ঐচ্ছিক)',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.note),
                  alignLabelWithHint: true,
                ),
                maxLines: 5,
              ),

              const SizedBox(height: 32),

              // Save button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saveCustomer,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Save Customer'
                        : 'গ্রাহক সংরক্ষণ করুন',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build section header
  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: 50,
          height: 3,
          decoration: BoxDecoration(
            color: ColorConstants.primaryColor,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ],
    );
  }
}

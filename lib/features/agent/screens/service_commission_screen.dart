import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/models/service_commission_model.dart';
import 'package:irecharge_pro/data/providers/service_commission_provider.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Service Commission Management Screen
class ServiceCommissionScreen extends ConsumerStatefulWidget {
  const ServiceCommissionScreen({super.key});

  @override
  ConsumerState<ServiceCommissionScreen> createState() => _ServiceCommissionScreenState();
}

class _ServiceCommissionScreenState extends ConsumerState<ServiceCommissionScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isFilterExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Add listener to search controller
    _searchController.addListener(() {
      final filter = ref.read(serviceCommissionFilterProvider);
      ref.read(serviceCommissionFilterProvider.notifier).state = filter.copyWith(
        searchQuery: _searchController.text,
      );
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// Toggle filter panel
  void _toggleFilterPanel() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
    });
  }

  /// Clear filters
  void _clearFilters() {
    setState(() {
      _searchController.clear();
      ref.read(serviceCommissionFilterProvider.notifier).state = const ServiceCommissionFilter();
    });
  }

  /// Show service commission details dialog
  void _showServiceCommissionDetails(ServiceCommission commission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Service Commission Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Basic information
              _buildSectionHeader('Basic Information'),
              _buildDetailRow('ID', commission.id),
              _buildDetailRow('Service Type', ServiceCommission.getServiceTypeText(commission.serviceType)),
              _buildDetailRow('Service Name', commission.serviceName),
              if (commission.operatorId != null)
                _buildDetailRow('Operator ID', commission.operatorId!),
              if (commission.operatorName != null)
                _buildDetailRow('Operator Name', commission.operatorName!),
              _buildDetailRow('User Type', AgentCustomer.getTypeText(commission.userType)),
              _buildDetailRow('Status', commission.isActive ? 'Active' : 'Inactive'),

              const SizedBox(height: 16),

              // Commission information
              _buildSectionHeader('Commission Information'),
              _buildDetailRow('Commission Type', ServiceCommission.getCommissionTypeText(commission.commissionType)),
              _buildDetailRow('Commission Rate', commission.formattedCommissionRate),
              _buildDetailRow('Charge Type', ServiceCommission.getChargeTypeText(commission.chargeType)),
              if (commission.serviceCharge != null && commission.chargeType != ChargeType.none)
                _buildDetailRow('Service Charge', commission.formattedServiceCharge),

              const SizedBox(height: 16),

              // Transaction limits
              if (commission.minTransactionAmount != null || commission.maxTransactionAmount != null) ...[
                _buildSectionHeader('Transaction Limits'),
                if (commission.minTransactionAmount != null)
                  _buildDetailRow('Minimum Amount', '৳${commission.minTransactionAmount}'),
                if (commission.maxTransactionAmount != null)
                  _buildDetailRow('Maximum Amount', '৳${commission.maxTransactionAmount}'),

                const SizedBox(height: 16),
              ],

              // Timestamps
              _buildSectionHeader('Timestamps'),
              _buildDetailRow('Created At', _formatDateTime(commission.createdAt)),
              if (commission.updatedAt != null)
                _buildDetailRow('Updated At', _formatDateTime(commission.updatedAt!)),

              const SizedBox(height: 16),

              // Example calculation
              _buildSectionHeader('Example Calculation'),
              _buildExampleCalculation(commission),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _showEditServiceCommissionDialog(commission);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  /// Build section header
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: ColorConstants.primaryColor,
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }

  /// Build example calculation
  Widget _buildExampleCalculation(ServiceCommission commission) {
    const double exampleAmount = 1000;
    final commissionAmount = commission.calculateCommission(exampleAmount);
    final chargeAmount = commission.calculateServiceCharge(exampleAmount);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withAlpha(50)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'For a transaction of ৳$exampleAmount:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildCalculationRow('Commission', '৳${commissionAmount.toStringAsFixed(2)}'),
          if (commission.chargeType != ChargeType.none)
            _buildCalculationRow('Service Charge', '৳${chargeAmount.toStringAsFixed(2)}'),
          const Divider(),
          _buildCalculationRow(
            'Net Amount',
            '৳${(exampleAmount - chargeAmount).toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  /// Build calculation row
  Widget _buildCalculationRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? ColorConstants.primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  /// Format date time
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Show add service commission dialog
  void _showAddServiceCommissionDialog() {
    ServiceType selectedServiceType = ServiceType.recharge;
    AgentCustomerType selectedUserType = AgentCustomerType.subAgent;
    CommissionType selectedCommissionType = CommissionType.percentage;
    ChargeType selectedChargeType = ChargeType.percentage;

    final serviceNameController = TextEditingController();
    final operatorNameController = TextEditingController();
    final operatorIdController = TextEditingController();
    final commissionRateController = TextEditingController();
    final serviceChargeController = TextEditingController();
    final minAmountController = TextEditingController();
    final maxAmountController = TextEditingController();

    bool isActive = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Service Commission'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Service type
                DropdownButtonFormField<ServiceType>(
                  value: selectedServiceType,
                  decoration: const InputDecoration(
                    labelText: 'Service Type',
                    border: OutlineInputBorder(),
                  ),
                  items: ServiceType.values.map((type) {
                    return DropdownMenuItem<ServiceType>(
                      value: type,
                      child: Text(ServiceCommission.getServiceTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedServiceType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Service name
                TextFormField(
                  controller: serviceNameController,
                  decoration: const InputDecoration(
                    labelText: 'Service Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Operator ID
                TextFormField(
                  controller: operatorIdController,
                  decoration: const InputDecoration(
                    labelText: 'Operator ID (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Operator Name
                TextFormField(
                  controller: operatorNameController,
                  decoration: const InputDecoration(
                    labelText: 'Operator Name (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Commission section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Commission Settings',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Commission type
                      DropdownButtonFormField<CommissionType>(
                        value: selectedCommissionType,
                        decoration: const InputDecoration(
                          labelText: 'Commission Type',
                          border: OutlineInputBorder(),
                        ),
                        items: CommissionType.values.map((type) {
                          return DropdownMenuItem<CommissionType>(
                            value: type,
                            child: Text(ServiceCommission.getCommissionTypeText(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedCommissionType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 12),

                      // Commission rate
                      TextFormField(
                        controller: commissionRateController,
                        decoration: InputDecoration(
                          labelText: selectedCommissionType == CommissionType.percentage
                              ? 'Commission Rate (%)'
                              : 'Commission Amount (৳)',
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Service Charge section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Service Charge Settings',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Charge type
                      DropdownButtonFormField<ChargeType>(
                        value: selectedChargeType,
                        decoration: const InputDecoration(
                          labelText: 'Charge Type',
                          border: OutlineInputBorder(),
                        ),
                        items: ChargeType.values.map((type) {
                          return DropdownMenuItem<ChargeType>(
                            value: type,
                            child: Text(ServiceCommission.getChargeTypeText(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedChargeType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 12),

                      // Service charge
                      if (selectedChargeType != ChargeType.none)
                        TextFormField(
                          controller: serviceChargeController,
                          decoration: InputDecoration(
                            labelText: selectedChargeType == ChargeType.percentage
                                ? 'Service Charge (%)'
                                : 'Service Charge Amount (৳)',
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Transaction Limits section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Transaction Limits (Optional)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Min amount
                      TextFormField(
                        controller: minAmountController,
                        decoration: const InputDecoration(
                          labelText: 'Minimum Transaction Amount (৳)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 12),

                      // Max amount
                      TextFormField(
                        controller: maxAmountController,
                        decoration: const InputDecoration(
                          labelText: 'Maximum Transaction Amount (৳)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // User type
                DropdownButtonFormField<AgentCustomerType>(
                  value: selectedUserType,
                  decoration: const InputDecoration(
                    labelText: 'User Type',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    AgentCustomerType.subAgent,
                    AgentCustomerType.customer,
                  ].map((type) {
                    return DropdownMenuItem<AgentCustomerType>(
                      value: type,
                      child: Text(AgentCustomer.getTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedUserType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Active status
                SwitchListTile(
                  title: const Text('Active'),
                  value: isActive,
                  onChanged: (value) {
                    setState(() {
                      isActive = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _disposeControllers([
                  serviceNameController,
                  operatorNameController,
                  operatorIdController,
                  commissionRateController,
                  serviceChargeController,
                  minAmountController,
                  maxAmountController,
                ]);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Validate inputs
                if (serviceNameController.text.isEmpty || commissionRateController.text.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please fill in all required fields'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final commissionRate = double.tryParse(commissionRateController.text);
                if (commissionRate == null || commissionRate <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid commission rate'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Parse service charge if applicable
                double? serviceCharge;
                if (selectedChargeType != ChargeType.none && serviceChargeController.text.isNotEmpty) {
                  serviceCharge = double.tryParse(serviceChargeController.text);
                  if (serviceCharge == null || serviceCharge < 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter a valid service charge'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                // Parse transaction limits if provided
                double? minAmount;
                if (minAmountController.text.isNotEmpty) {
                  minAmount = double.tryParse(minAmountController.text);
                  if (minAmount == null || minAmount < 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter a valid minimum amount'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                double? maxAmount;
                if (maxAmountController.text.isNotEmpty) {
                  maxAmount = double.tryParse(maxAmountController.text);
                  if (maxAmount == null || maxAmount < 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter a valid maximum amount'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                // Validate min/max relationship
                if (minAmount != null && maxAmount != null && minAmount > maxAmount) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Minimum amount cannot be greater than maximum amount'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Create new service commission
                final commission = ServiceCommission(
                  id: ref.read(serviceCommissionsProvider.notifier).generateServiceCommissionId(),
                  serviceType: selectedServiceType,
                  serviceName: serviceNameController.text,
                  operatorId: operatorIdController.text.isNotEmpty ? operatorIdController.text : null,
                  operatorName: operatorNameController.text.isNotEmpty ? operatorNameController.text : null,
                  commissionRate: commissionRate,
                  commissionType: selectedCommissionType,
                  serviceCharge: serviceCharge,
                  chargeType: selectedChargeType,
                  userType: selectedUserType,
                  isActive: isActive,
                  createdAt: DateTime.now(),
                  minTransactionAmount: minAmount,
                  maxTransactionAmount: maxAmount,
                );

                // Add service commission
                ref.read(serviceCommissionsProvider.notifier).addServiceCommission(commission);

                Navigator.pop(context);
                _disposeControllers([
                  serviceNameController,
                  operatorNameController,
                  operatorIdController,
                  commissionRateController,
                  serviceChargeController,
                  minAmountController,
                  maxAmountController,
                ]);

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Service commission added successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  /// Dispose multiple controllers
  void _disposeControllers(List<TextEditingController> controllers) {
    for (final controller in controllers) {
      controller.dispose();
    }
  }

  /// Show edit service commission dialog
  void _showEditServiceCommissionDialog(ServiceCommission commission) {
    ServiceType selectedServiceType = commission.serviceType;
    AgentCustomerType selectedUserType = commission.userType;
    CommissionType selectedCommissionType = commission.commissionType;
    ChargeType selectedChargeType = commission.chargeType;

    final serviceNameController = TextEditingController(text: commission.serviceName);
    final operatorNameController = TextEditingController(text: commission.operatorName ?? '');
    final operatorIdController = TextEditingController(text: commission.operatorId ?? '');
    final commissionRateController = TextEditingController(text: commission.commissionRate.toString());
    final serviceChargeController = TextEditingController(
      text: commission.serviceCharge != null ? commission.serviceCharge.toString() : ''
    );
    final minAmountController = TextEditingController(
      text: commission.minTransactionAmount != null ? commission.minTransactionAmount.toString() : ''
    );
    final maxAmountController = TextEditingController(
      text: commission.maxTransactionAmount != null ? commission.maxTransactionAmount.toString() : ''
    );

    bool isActive = commission.isActive;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Edit Service Commission'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Service type
                DropdownButtonFormField<ServiceType>(
                  value: selectedServiceType,
                  decoration: const InputDecoration(
                    labelText: 'Service Type',
                    border: OutlineInputBorder(),
                  ),
                  items: ServiceType.values.map((type) {
                    return DropdownMenuItem<ServiceType>(
                      value: type,
                      child: Text(ServiceCommission.getServiceTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedServiceType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Service name
                TextFormField(
                  controller: serviceNameController,
                  decoration: const InputDecoration(
                    labelText: 'Service Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Operator ID
                TextFormField(
                  controller: operatorIdController,
                  decoration: const InputDecoration(
                    labelText: 'Operator ID (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Operator Name
                TextFormField(
                  controller: operatorNameController,
                  decoration: const InputDecoration(
                    labelText: 'Operator Name (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Commission section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Commission Settings',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Commission type
                      DropdownButtonFormField<CommissionType>(
                        value: selectedCommissionType,
                        decoration: const InputDecoration(
                          labelText: 'Commission Type',
                          border: OutlineInputBorder(),
                        ),
                        items: CommissionType.values.map((type) {
                          return DropdownMenuItem<CommissionType>(
                            value: type,
                            child: Text(ServiceCommission.getCommissionTypeText(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedCommissionType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 12),

                      // Commission rate
                      TextFormField(
                        controller: commissionRateController,
                        decoration: InputDecoration(
                          labelText: selectedCommissionType == CommissionType.percentage
                              ? 'Commission Rate (%)'
                              : 'Commission Amount (৳)',
                          border: const OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Service Charge section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Service Charge Settings',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Charge type
                      DropdownButtonFormField<ChargeType>(
                        value: selectedChargeType,
                        decoration: const InputDecoration(
                          labelText: 'Charge Type',
                          border: OutlineInputBorder(),
                        ),
                        items: ChargeType.values.map((type) {
                          return DropdownMenuItem<ChargeType>(
                            value: type,
                            child: Text(ServiceCommission.getChargeTypeText(type)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedChargeType = value;

                              // Clear service charge if type is none
                              if (value == ChargeType.none) {
                                serviceChargeController.clear();
                              }
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 12),

                      // Service charge
                      if (selectedChargeType != ChargeType.none)
                        TextFormField(
                          controller: serviceChargeController,
                          decoration: InputDecoration(
                            labelText: selectedChargeType == ChargeType.percentage
                                ? 'Service Charge (%)'
                                : 'Service Charge Amount (৳)',
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Transaction Limits section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Transaction Limits (Optional)',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Min amount
                      TextFormField(
                        controller: minAmountController,
                        decoration: const InputDecoration(
                          labelText: 'Minimum Transaction Amount (৳)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 12),

                      // Max amount
                      TextFormField(
                        controller: maxAmountController,
                        decoration: const InputDecoration(
                          labelText: 'Maximum Transaction Amount (৳)',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // User type
                DropdownButtonFormField<AgentCustomerType>(
                  value: selectedUserType,
                  decoration: const InputDecoration(
                    labelText: 'User Type',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    AgentCustomerType.subAgent,
                    AgentCustomerType.customer,
                  ].map((type) {
                    return DropdownMenuItem<AgentCustomerType>(
                      value: type,
                      child: Text(AgentCustomer.getTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedUserType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Active status
                SwitchListTile(
                  title: const Text('Active'),
                  value: isActive,
                  onChanged: (value) {
                    setState(() {
                      isActive = value;
                    });
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _disposeControllers([
                  serviceNameController,
                  operatorNameController,
                  operatorIdController,
                  commissionRateController,
                  serviceChargeController,
                  minAmountController,
                  maxAmountController,
                ]);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Validate inputs
                if (serviceNameController.text.isEmpty || commissionRateController.text.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please fill in all required fields'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final commissionRate = double.tryParse(commissionRateController.text);
                if (commissionRate == null || commissionRate <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid commission rate'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Parse service charge if applicable
                double? serviceCharge;
                if (selectedChargeType != ChargeType.none && serviceChargeController.text.isNotEmpty) {
                  serviceCharge = double.tryParse(serviceChargeController.text);
                  if (serviceCharge == null || serviceCharge < 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter a valid service charge'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                // Parse transaction limits if provided
                double? minAmount;
                if (minAmountController.text.isNotEmpty) {
                  minAmount = double.tryParse(minAmountController.text);
                  if (minAmount == null || minAmount < 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter a valid minimum amount'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                double? maxAmount;
                if (maxAmountController.text.isNotEmpty) {
                  maxAmount = double.tryParse(maxAmountController.text);
                  if (maxAmount == null || maxAmount < 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter a valid maximum amount'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                }

                // Validate min/max relationship
                if (minAmount != null && maxAmount != null && minAmount > maxAmount) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Minimum amount cannot be greater than maximum amount'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Update service commission
                final updatedCommission = commission.copyWith(
                  serviceType: selectedServiceType,
                  serviceName: serviceNameController.text,
                  operatorId: operatorIdController.text.isNotEmpty ? operatorIdController.text : null,
                  operatorName: operatorNameController.text.isNotEmpty ? operatorNameController.text : null,
                  commissionRate: commissionRate,
                  commissionType: selectedCommissionType,
                  serviceCharge: serviceCharge,
                  chargeType: selectedChargeType,
                  userType: selectedUserType,
                  isActive: isActive,
                  updatedAt: DateTime.now(),
                  minTransactionAmount: minAmount,
                  maxTransactionAmount: maxAmount,
                );

                // Update service commission
                ref.read(serviceCommissionsProvider.notifier).updateServiceCommission(updatedCommission);

                Navigator.pop(context);
                _disposeControllers([
                  serviceNameController,
                  operatorNameController,
                  operatorIdController,
                  commissionRateController,
                  serviceChargeController,
                  minAmountController,
                  maxAmountController,
                ]);

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Service commission updated successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build detail row for service commission details
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// Build service commission card
  Widget _buildServiceCommissionCard(ServiceCommission commission) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showServiceCommissionDetails(commission),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Service type and name
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: ServiceCommission.getServiceTypeColor(commission.serviceType).withAlpha(50),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            ServiceCommission.getServiceTypeIcon(commission.serviceType),
                            color: ServiceCommission.getServiceTypeColor(commission.serviceType),
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                commission.serviceName,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                children: [
                                  Text(
                                    ServiceCommission.getServiceTypeText(commission.serviceType),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  if (commission.operatorName != null) ...[
                                    Text(
                                      ' • ',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    Text(
                                      commission.operatorName!,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Commission rate
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: ColorConstants.primaryColor.withAlpha(50),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      commission.formattedCommissionRate,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: ColorConstants.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Service charge if applicable
              if (commission.chargeType != ChargeType.none && commission.serviceCharge != null)
                Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber.withAlpha(30),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.amber.withAlpha(100), width: 1),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.payments_outlined,
                        size: 14,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Charge: ${commission.formattedServiceCharge}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.amber,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

              // User type and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // User type
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(50),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      AgentCustomer.getTypeText(commission.userType),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.blue,
                      ),
                    ),
                  ),

                  // Status
                  Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: commission.isActive ? Colors.green : Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        commission.isActive ? 'Active' : 'Inactive',
                        style: TextStyle(
                          fontSize: 12,
                          color: commission.isActive ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Toggle button
                      InkWell(
                        onTap: () {
                          ref.read(serviceCommissionsProvider.notifier).toggleServiceCommissionStatus(commission.id);
                        },
                        borderRadius: BorderRadius.circular(4),
                        child: Padding(
                          padding: const EdgeInsets.all(4),
                          child: Icon(
                            commission.isActive ? Icons.toggle_on : Icons.toggle_off,
                            color: commission.isActive ? Colors.green : Colors.red,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filter = ref.watch(serviceCommissionFilterProvider);
    final filteredCommissions = ref.read(serviceCommissionsProvider.notifier).getFilteredServiceCommissions(filter);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Service Commission'
              : 'সার্ভিস কমিশন',
          style: const TextStyle(
            color: Colors.amber,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          // Advanced Settings button
          TextButton.icon(
            onPressed: () {
              // Show a dialog explaining the feature
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Advanced Commission Settings'
                        : 'উন্নত কমিশন সেটিংস',
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'This feature allows you to:'
                            : 'এই ফিচারটি আপনাকে অনুমতি দেয়:',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      _buildFeaturePoint(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Set commission rates based on service, operator, and user type'
                            : 'সার্ভিস, অপারেটর এবং ব্যবহারকারী টাইপ অনুসারে কমিশন হার সেট করুন',
                      ),
                      _buildFeaturePoint(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Configure service charges for transactions'
                            : 'লেনদেনের জন্য সার্ভিস চার্জ কনফিগার করুন',
                      ),
                      _buildFeaturePoint(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Automatically apply commission during transactions'
                            : 'লেনদেনের সময় স্বয়ংক্রিয়ভাবে কমিশন প্রয়োগ করুন',
                      ),
                      _buildFeaturePoint(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Set transaction amount limits for commission rules'
                            : 'কমিশন নিয়মের জন্য লেনদেন পরিমাণ সীমা সেট করুন',
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Close'
                            : 'বন্ধ করুন',
                      ),
                    ),
                  ],
                ),
              );
            },
            icon: const Icon(Icons.settings, color: Colors.green),
            label: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Advanced'
                  : 'উন্নত',
              style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
            ),
          ),
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _toggleFilterPanel,
            tooltip: _isFilterExpanded ? 'Hide Filters' : 'Show Filters',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Sub Agents'
                  : 'সাব এজেন্ট',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Customers'
                  : 'কাস্টমার',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Advanced commission management button
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Card(
              color: ColorConstants.primaryColor,
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: InkWell(
                onTap: () {
                  // Show a dialog explaining the feature
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Advanced Commission Settings'
                            : 'উন্নত কমিশন সেটিংস',
                      ),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'This feature allows you to:'
                                : 'এই ফিচারটি আপনাকে অনুমতি দেয়:',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          _buildFeaturePoint(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Set commission rates based on service, operator, and user type'
                                : 'সার্ভিস, অপারেটর এবং ব্যবহারকারী টাইপ অনুসারে কমিশন হার সেট করুন',
                          ),
                          _buildFeaturePoint(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Configure service charges for transactions'
                                : 'লেনদেনের জন্য সার্ভিস চার্জ কনফিগার করুন',
                          ),
                          _buildFeaturePoint(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Automatically apply commission during transactions'
                                : 'লেনদেনের সময় স্বয়ংক্রিয়ভাবে কমিশন প্রয়োগ করুন',
                          ),
                          _buildFeaturePoint(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Set transaction amount limits for commission rules'
                                : 'কমিশন নিয়মের জন্য লেনদেন পরিমাণ সীমা সেট করুন',
                          ),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Close'
                                : 'বন্ধ করুন',
                          ),
                        ),
                      ],
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(40),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.settings,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'Advanced Commission Settings'
                                  : 'উন্নত কমিশন সেটিংস',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'Configure automatic commission application for transactions'
                                  : 'লেনদেনের জন্য স্বয়ংক্রিয় কমিশন অ্যাপ্লিকেশন কনফিগার করুন',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 24,
                        color: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Filter panel
          if (_isFilterExpanded)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey.shade100,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Search field
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Search service commissions...'
                          : 'সার্ভিস কমিশন অনুসন্ধান করুন...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Filter options
                  Row(
                    children: [
                      // Service type filter
                      Expanded(
                        child: DropdownButtonFormField<ServiceType?>(
                          value: filter.serviceType,
                          decoration: InputDecoration(
                            labelText: ref.watch(languageProvider) == AppLanguage.english
                                ? 'Service Type'
                                : 'সার্ভিস টাইপ',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem<ServiceType?>(
                              value: null,
                              child: Text('All Types'),
                            ),
                            ...ServiceType.values.map((type) {
                              return DropdownMenuItem<ServiceType?>(
                                value: type,
                                child: Text(ServiceCommission.getServiceTypeText(type)),
                              );
                            }),
                          ],
                          onChanged: (value) {
                            ref.read(serviceCommissionFilterProvider.notifier).state = filter.copyWith(
                              serviceType: value,
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),

                      // Status filter
                      Expanded(
                        child: DropdownButtonFormField<bool?>(
                          value: filter.isActive,
                          decoration: InputDecoration(
                            labelText: ref.watch(languageProvider) == AppLanguage.english
                                ? 'Status'
                                : 'স্ট্যাটাস',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: [
                            const DropdownMenuItem<bool?>(
                              value: null,
                              child: Text('All'),
                            ),
                            const DropdownMenuItem<bool?>(
                              value: true,
                              child: Text('Active'),
                            ),
                            const DropdownMenuItem<bool?>(
                              value: false,
                              child: Text('Inactive'),
                            ),
                          ],
                          onChanged: (value) {
                            ref.read(serviceCommissionFilterProvider.notifier).state = filter.copyWith(
                              isActive: value,
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),

                      // Clear filters button
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: _clearFilters,
                        tooltip: 'Clear Filters',
                      ),
                    ],
                  ),
                ],
              ),
            ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Sub Agents tab
                _buildServiceCommissionList(
                  filteredCommissions.where((c) => c.userType == AgentCustomerType.subAgent).toList(),
                ),

                // Customers tab
                _buildServiceCommissionList(
                  filteredCommissions.where((c) => c.userType == AgentCustomerType.customer).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddServiceCommissionDialog,
        backgroundColor: ColorConstants.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build feature point for dialog
  Widget _buildFeaturePoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle,
            color: ColorConstants.primaryColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(text),
          ),
        ],
      ),
    );
  }

  /// Build service commission list
  Widget _buildServiceCommissionList(List<ServiceCommission> commissions) {
    if (commissions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.money_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No service commissions found'
                  : 'কোন সার্ভিস কমিশন পাওয়া যায়নি',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            if (_searchController.text.isNotEmpty ||
                ref.watch(serviceCommissionFilterProvider).serviceType != null ||
                ref.watch(serviceCommissionFilterProvider).isActive != null)
              TextButton.icon(
                onPressed: _clearFilters,
                icon: const Icon(Icons.filter_alt_off),
                label: Text(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Clear Filters'
                      : 'ফিল্টার মুছুন',
                ),
              ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: commissions.length,
      itemBuilder: (context, index) {
        final commission = commissions[index];
        return _buildServiceCommissionCard(commission);
      },
    );
  }
}

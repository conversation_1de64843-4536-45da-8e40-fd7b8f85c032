import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/models/commission_model.dart';
import 'package:irecharge_pro/data/providers/agent_customer_provider.dart';
import 'package:irecharge_pro/data/providers/commission_provider.dart';
import 'package:irecharge_pro/features/agent/screens/service_commission_screen.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Commission Management Screen
class CommissionManagementScreen extends ConsumerStatefulWidget {
  const CommissionManagementScreen({super.key});

  @override
  ConsumerState<CommissionManagementScreen> createState() => _CommissionManagementScreenState();
}

class _CommissionManagementScreenState extends ConsumerState<CommissionManagementScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isFilterExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Add listener to search controller
    _searchController.addListener(() {
      final filter = ref.read(commissionFilterProvider);
      ref.read(commissionFilterProvider.notifier).state = filter.copyWith(
        searchQuery: _searchController.text,
      );
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// Toggle filter panel
  void _toggleFilterPanel() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
    });
  }

  /// Clear filters
  void _clearFilters() {
    setState(() {
      _searchController.clear();
      ref.read(commissionFilterProvider.notifier).state = const CommissionFilter();
    });
  }

  /// Show commission details dialog
  void _showCommissionDetails(Commission commission) {
    final agent = ref.read(agentCustomersProvider.notifier).getAgentById(commission.agentId);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Commission Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('ID', commission.id),
              _buildDetailRow('Agent', agent?.name ?? 'Unknown'),
              _buildDetailRow('Type', Commission.getTypeText(commission.type)),
              _buildDetailRow('Amount', commission.formattedAmount),
              _buildDetailRow('Rate', '${commission.rate}%'),
              _buildDetailRow('Date', '${commission.formattedDate} ${commission.formattedTime}'),
              _buildDetailRow('Status', Commission.getStatusText(commission.status)),
              if (commission.transactionId != null)
                _buildDetailRow('Transaction ID', commission.transactionId!),
              _buildDetailRow('Description', commission.description),
              if (commission.notes != null && commission.notes!.isNotEmpty)
                _buildDetailRow('Notes', commission.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (commission.status != CommissionStatus.paid && commission.status != CommissionStatus.rejected)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showUpdateStatusDialog(commission);
              },
              child: const Text('Update Status'),
            ),
        ],
      ),
    );
  }

  /// Show update status dialog
  void _showUpdateStatusDialog(Commission commission) {
    CommissionStatus selectedStatus = commission.status;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Update Commission Status'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Select the new status for this commission:'),
              const SizedBox(height: 16),
              DropdownButtonFormField<CommissionStatus>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                ),
                items: CommissionStatus.values.map((status) {
                  return DropdownMenuItem<CommissionStatus>(
                    value: status,
                    child: Text(Commission.getStatusText(status)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      selectedStatus = value;
                    });
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                ref.read(commissionsProvider.notifier).updateCommissionStatus(
                  commission.id,
                  selectedStatus,
                );
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Commission status updated to ${Commission.getStatusText(selectedStatus)}'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }

  /// Show add commission dialog
  void _showAddCommissionDialog() {
    final agents = ref.read(agentCustomersProvider).where(
      (agent) => agent.type == AgentCustomerType.subAgent || agent.type == AgentCustomerType.business
    ).toList();

    if (agents.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No eligible agents found. Add sub-agents or business customers first.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    String? selectedAgentId = agents.first.id;
    CommissionType selectedType = CommissionType.recharge;
    final amountController = TextEditingController();
    final rateController = TextEditingController();
    final descriptionController = TextEditingController();
    final notesController = TextEditingController();
    final transactionIdController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add New Commission'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Agent selection
                DropdownButtonFormField<String>(
                  value: selectedAgentId,
                  decoration: const InputDecoration(
                    labelText: 'Agent',
                    border: OutlineInputBorder(),
                  ),
                  items: agents.map((agent) {
                    return DropdownMenuItem<String>(
                      value: agent.id,
                      child: Text('${agent.name} (${AgentCustomer.getTypeText(agent.type)})'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedAgentId = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Commission type
                DropdownButtonFormField<CommissionType>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Commission Type',
                    border: OutlineInputBorder(),
                  ),
                  items: CommissionType.values.map((type) {
                    return DropdownMenuItem<CommissionType>(
                      value: type,
                      child: Text(Commission.getTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // Amount
                TextFormField(
                  controller: amountController,
                  decoration: const InputDecoration(
                    labelText: 'Amount (৳)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),

                // Rate
                TextFormField(
                  controller: rateController,
                  decoration: const InputDecoration(
                    labelText: 'Rate (%)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // Transaction ID (optional)
                TextFormField(
                  controller: transactionIdController,
                  decoration: const InputDecoration(
                    labelText: 'Transaction ID (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // Notes (optional)
                TextFormField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                amountController.dispose();
                rateController.dispose();
                descriptionController.dispose();
                notesController.dispose();
                transactionIdController.dispose();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Validate inputs
                if (amountController.text.isEmpty ||
                    rateController.text.isEmpty ||
                    descriptionController.text.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please fill in all required fields'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                final amount = double.tryParse(amountController.text);
                final rate = double.tryParse(rateController.text);

                if (amount == null || amount <= 0 || rate == null || rate <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter valid amount and rate'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Create new commission
                final commission = Commission(
                  id: ref.read(commissionsProvider.notifier).generateCommissionId(),
                  agentId: selectedAgentId!,
                  type: selectedType,
                  amount: amount,
                  rate: rate,
                  date: DateTime.now(),
                  description: descriptionController.text,
                  status: CommissionStatus.pending,
                  transactionId: transactionIdController.text.isNotEmpty
                      ? transactionIdController.text
                      : null,
                  notes: notesController.text.isNotEmpty
                      ? notesController.text
                      : null,
                );

                // Add commission
                ref.read(commissionsProvider.notifier).addCommission(commission);

                Navigator.pop(context);
                amountController.dispose();
                rateController.dispose();
                descriptionController.dispose();
                notesController.dispose();
                transactionIdController.dispose();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Commission added successfully'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build detail row for commission details
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// Build commission card
  Widget _buildCommissionCard(Commission commission) {
    final agent = ref.read(agentCustomersProvider.notifier).getAgentById(commission.agentId);
    final statusColor = Commission.getStatusColor(commission.status);
    final typeColor = Commission.getTypeColor(commission.type);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: statusColor.withAlpha(50),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () => _showCommissionDetails(commission),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status indicator
            Container(
              decoration: BoxDecoration(
                color: statusColor.withAlpha(20),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Commission ID
                  Row(
                    children: [
                      Icon(
                        Commission.getTypeIcon(commission.type),
                        size: 16,
                        color: typeColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        commission.id,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ],
                  ),

                  // Status
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: statusColor.withAlpha(100), width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: statusColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          Commission.getStatusText(commission.status),
                          style: TextStyle(
                            fontSize: 12,
                            color: statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Type and amount row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Commission type
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: typeColor.withAlpha(30),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          Commission.getTypeText(commission.type),
                          style: TextStyle(
                            fontSize: 12,
                            color: typeColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),

                      // Commission amount
                      Row(
                        children: [
                          Text(
                            'Rate: ${commission.rate}%',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: ColorConstants.primaryColor.withAlpha(20),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: ColorConstants.primaryColor.withAlpha(50),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              commission.formattedAmount,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: ColorConstants.primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Agent info
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.blue.withAlpha(30),
                        child: Text(
                          agent?.name.substring(0, 1) ?? 'U',
                          style: const TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              agent?.name ?? 'Unknown Agent',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (agent != null)
                              Text(
                                AgentCustomer.getTypeText(agent.type),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Description
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.withAlpha(20),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Description:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          commission.description,
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Transaction ID and date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Transaction ID if available
                      if (commission.transactionId != null)
                        Expanded(
                          child: Row(
                            children: [
                              const Icon(
                                Icons.receipt,
                                size: 14,
                                color: Colors.grey,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  'TX: ${commission.transactionId}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Date
                      Row(
                        children: [
                          const Icon(
                            Icons.access_time,
                            size: 14,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${commission.formattedDate} ${commission.formattedTime}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Action buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // View details button
                  TextButton.icon(
                    onPressed: () => _showCommissionDetails(commission),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('Details'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.blue,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),

                  // Update status button (if not paid or rejected)
                  if (commission.status != CommissionStatus.paid && commission.status != CommissionStatus.rejected)
                    TextButton.icon(
                      onPressed: () => _showUpdateStatusDialog(commission),
                      icon: const Icon(Icons.update, size: 16),
                      label: const Text('Update'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.orange,
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build commission summary card
  Widget _buildCommissionSummaryCard() {
    final commissions = ref.watch(commissionsProvider);
    final filteredCommissions = ref.read(commissionsProvider.notifier).getFilteredCommissions(
      ref.watch(commissionFilterProvider)
    );

    // Calculate totals
    final totalCommission = commissions.fold(0.0, (sum, commission) => sum + commission.amount);
    final pendingCommission = commissions
        .where((c) => c.status == CommissionStatus.pending)
        .fold(0.0, (sum, commission) => sum + commission.amount);
    final approvedCommission = commissions
        .where((c) => c.status == CommissionStatus.approved)
        .fold(0.0, (sum, commission) => sum + commission.amount);
    final paidCommission = commissions
        .where((c) => c.status == CommissionStatus.paid)
        .fold(0.0, (sum, commission) => sum + commission.amount);

    // Calculate filtered totals
    final filteredTotalCommission = filteredCommissions.fold(0.0, (sum, commission) => sum + commission.amount);

    // Calculate percentages
    final pendingPercentage = totalCommission > 0 ? (pendingCommission / totalCommission) * 100.0 : 0.0;
    final approvedPercentage = totalCommission > 0 ? (approvedCommission / totalCommission) * 100.0 : 0.0;
    final paidPercentage = totalCommission > 0 ? (paidCommission / totalCommission) * 100.0 : 0.0;

    // Count by type
    final rechargeCount = commissions.where((c) => c.type == CommissionType.recharge).length;
    final mobileBankingCount = commissions.where((c) => c.type == CommissionType.mobileBanking).length;
    final billPaymentCount = commissions.where((c) => c.type == CommissionType.billPayment).length;
    final otherCount = commissions.length - rechargeCount - mobileBankingCount - billPaymentCount;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: ColorConstants.primaryColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Commission Summary',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(30),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${commissions.length} Transactions',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Main content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Amount cards
                Row(
                  children: [
                    Expanded(
                      child: _buildAmountCard(
                        'Total Commission',
                        '৳${totalCommission.toStringAsFixed(2)}',
                        ColorConstants.primaryColor,
                        Icons.monetization_on,
                        subtitle: filteredCommissions.length < commissions.length
                            ? 'Filtered: ৳${filteredTotalCommission.toStringAsFixed(2)}'
                            : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Status breakdown
                Row(
                  children: [
                    Expanded(
                      child: _buildStatusCard(
                        'Pending',
                        '৳${pendingCommission.toStringAsFixed(2)}',
                        Colors.amber,
                        Icons.hourglass_empty,
                        percentage: pendingPercentage,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatusCard(
                        'Approved',
                        '৳${approvedCommission.toStringAsFixed(2)}',
                        Colors.green,
                        Icons.check_circle_outline,
                        percentage: approvedPercentage,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildStatusCard(
                        'Paid',
                        '৳${paidCommission.toStringAsFixed(2)}',
                        Colors.blue,
                        Icons.payments,
                        percentage: paidPercentage,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Type distribution
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(20),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Commission by Type',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildTypeIndicator(
                            'Recharge',
                            rechargeCount,
                            Colors.purple,
                            Icons.phone_android,
                          ),
                          _buildTypeIndicator(
                            'Mobile Banking',
                            mobileBankingCount,
                            Colors.teal,
                            Icons.account_balance_wallet,
                          ),
                          _buildTypeIndicator(
                            'Bill Payment',
                            billPaymentCount,
                            Colors.indigo,
                            Icons.receipt_long,
                          ),
                          _buildTypeIndicator(
                            'Other',
                            otherCount,
                            Colors.grey,
                            Icons.more_horiz,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Export and print buttons
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        onPressed: () {
                          // TODO: Implement export functionality
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Export functionality coming soon'),
                            ),
                          );
                        },
                        icon: const Icon(Icons.download, size: 16),
                        label: const Text('Export'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                      const SizedBox(width: 16),
                      TextButton.icon(
                        onPressed: () {
                          // TODO: Implement print functionality
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Print functionality coming soon'),
                            ),
                          );
                        },
                        icon: const Icon(Icons.print, size: 16),
                        label: const Text('Print'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.green,
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build amount card
  Widget _buildAmountCard(String title, String amount, Color color, IconData icon, {String? subtitle}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(50), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  amount,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build status card
  Widget _buildStatusCard(String title, String amount, Color color, IconData icon, {double percentage = 0}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withAlpha(50), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            amount,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          // Progress indicator
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.grey.withAlpha(30),
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 4,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build type indicator
  Widget _buildTypeIndicator(String label, int count, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(30),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// Navigate to service commission screen
  void _navigateToServiceCommission() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ServiceCommissionScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filter = ref.watch(commissionFilterProvider);
    final commissions = ref.watch(commissionsProvider);
    final filteredCommissions = ref.read(commissionsProvider.notifier).getFilteredCommissions(filter);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Commission Transactions'
              : 'কমিশন ট্রানজেকশন',
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          // Service Commission button
          TextButton.icon(
            onPressed: _navigateToServiceCommission,
            icon: const Icon(Icons.settings),
            label: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Service Commission'
                  : 'সার্ভিস কমিশন',
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
            ),
          ),
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _toggleFilterPanel,
            tooltip: _isFilterExpanded ? 'Hide Filters' : 'Show Filters',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            const Tab(text: 'All'),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Pending'),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      commissions.where((c) => c.status == CommissionStatus.pending).length.toString(),
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Tab(text: 'Approved'),
            const Tab(text: 'Paid'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Filter panel
          if (_isFilterExpanded)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.shade200,
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Filter title
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Filter Commissions'
                            : 'কমিশন ফিল্টার করুন',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton.icon(
                        onPressed: _clearFilters,
                        icon: const Icon(Icons.clear_all, size: 16),
                        label: Text(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Clear All'
                              : 'সব মুছুন',
                          style: const TextStyle(fontSize: 12),
                        ),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Search field
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Search by ID, agent, or description...'
                          : 'আইডি, এজেন্ট, বা বিবরণ দ্বারা অনুসন্ধান করুন...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                // This will trigger the listener which updates the filter
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: EdgeInsets.zero,
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Filter options
                  Row(
                    children: [
                      // Commission type filter
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'Commission Type'
                                  : 'কমিশন ধরন',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: DropdownButtonFormField<CommissionType?>(
                                value: filter.type,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  suffixIcon: filter.type != null
                                      ? IconButton(
                                          icon: const Icon(Icons.clear, size: 16),
                                          onPressed: () {
                                            ref.read(commissionFilterProvider.notifier).state = filter.copyWith(
                                              type: null,
                                            );
                                          },
                                        )
                                      : null,
                                ),
                                items: [
                                  const DropdownMenuItem<CommissionType?>(
                                    value: null,
                                    child: Text('All Types'),
                                  ),
                                  ...CommissionType.values.map((type) {
                                    final color = Commission.getTypeColor(type);
                                    return DropdownMenuItem<CommissionType?>(
                                      value: type,
                                      child: Row(
                                        children: [
                                          Icon(
                                            Commission.getTypeIcon(type),
                                            size: 16,
                                            color: color,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            Commission.getTypeText(type),
                                            style: TextStyle(
                                              color: color,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                                ],
                                onChanged: (value) {
                                  ref.read(commissionFilterProvider.notifier).state = filter.copyWith(
                                    type: value,
                                  );
                                },
                                isExpanded: true,
                                icon: const Icon(Icons.arrow_drop_down),
                                dropdownColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),

                      // Status filter
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'Status'
                                  : 'স্ট্যাটাস',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: DropdownButtonFormField<CommissionStatus?>(
                                value: filter.status,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  suffixIcon: filter.status != null
                                      ? IconButton(
                                          icon: const Icon(Icons.clear, size: 16),
                                          onPressed: () {
                                            ref.read(commissionFilterProvider.notifier).state = filter.copyWith(
                                              status: null,
                                            );
                                          },
                                        )
                                      : null,
                                ),
                                items: [
                                  const DropdownMenuItem<CommissionStatus?>(
                                    value: null,
                                    child: Text('All Statuses'),
                                  ),
                                  ...CommissionStatus.values.map((status) {
                                    final color = Commission.getStatusColor(status);
                                    return DropdownMenuItem<CommissionStatus?>(
                                      value: status,
                                      child: Row(
                                        children: [
                                          Container(
                                            width: 8,
                                            height: 8,
                                            decoration: BoxDecoration(
                                              color: color,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            Commission.getStatusText(status),
                                            style: TextStyle(
                                              color: color,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),
                                ],
                                onChanged: (value) {
                                  ref.read(commissionFilterProvider.notifier).state = filter.copyWith(
                                    status: value,
                                  );
                                },
                                isExpanded: true,
                                icon: const Icon(Icons.arrow_drop_down),
                                dropdownColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Date range filter
                  const SizedBox(height: 16),
                  Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Date Range (Coming Soon)'
                        : 'তারিখ সীমা (শীঘ্রই আসছে)',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.date_range,
                          color: Colors.grey,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Date filtering will be available soon'
                              : 'তারিখ ফিল্টারিং শীঘ্রই উপলব্ধ হবে',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Apply filter button
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            setState(() {
                              _isFilterExpanded = false;
                            });
                          },
                          icon: const Icon(Icons.filter_list),
                          label: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Apply Filters'
                                : 'ফিল্টার প্রয়োগ করুন',
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstants.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

          // Commission summary
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildCommissionSummaryCard(),
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All commissions
                _buildCommissionList(filteredCommissions),

                // Pending commissions
                _buildCommissionList(
                  filteredCommissions.where((c) => c.status == CommissionStatus.pending).toList(),
                ),

                // Approved commissions
                _buildCommissionList(
                  filteredCommissions.where((c) => c.status == CommissionStatus.approved).toList(),
                ),

                // Paid commissions
                _buildCommissionList(
                  filteredCommissions.where((c) => c.status == CommissionStatus.paid).toList(),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCommissionDialog,
        backgroundColor: ColorConstants.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Build commission list
  Widget _buildCommissionList(List<Commission> commissions) {
    if (commissions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.money_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No commissions found'
                  : 'কোন কমিশন পাওয়া যায়নি',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            if (_searchController.text.isNotEmpty || ref.watch(commissionFilterProvider).type != null)
              TextButton.icon(
                onPressed: _clearFilters,
                icon: const Icon(Icons.filter_alt_off),
                label: Text(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Clear Filters'
                      : 'ফিল্টার মুছুন',
                ),
              ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: commissions.length,
      itemBuilder: (context, index) {
        final commission = commissions[index];
        return _buildCommissionCard(commission);
      },
    );
  }
}

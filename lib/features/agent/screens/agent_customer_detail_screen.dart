import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/models/commission_model.dart';
import 'package:irecharge_pro/data/providers/agent_customer_provider.dart';
import 'package:irecharge_pro/data/providers/commission_provider.dart';
import 'package:irecharge_pro/features/agent/screens/agent_customer_form_screen.dart';
import 'package:irecharge_pro/features/agent/screens/commission_management_screen.dart';

/// Agent Customer Detail Screen
class AgentCustomerDetailScreen extends ConsumerStatefulWidget {
  const AgentCustomerDetailScreen({super.key});

  @override
  ConsumerState<AgentCustomerDetailScreen> createState() => _AgentCustomerDetailScreenState();
}

class _AgentCustomerDetailScreenState extends ConsumerState<AgentCustomerDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(AgentCustomer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Delete Customer'
              : 'গ্রাহক মুছুন',
        ),
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Are you sure you want to delete ${customer.name}? This action cannot be undone.'
              : 'আপনি কি নিশ্চিত যে আপনি ${customer.name} মুছতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Cancel'
                  : 'বাতিল',
            ),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(agentCustomersProvider.notifier).deleteCustomer(customer.id);
              Navigator.pop(context);
              Navigator.pop(context); // Go back to list screen
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Customer deleted successfully'
                        : 'গ্রাহক সফলভাবে মুছে ফেলা হয়েছে',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Delete'
                  : 'মুছুন',
            ),
          ),
        ],
      ),
    );
  }

  /// Show status change dialog
  void _showStatusChangeDialog(AgentCustomer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Change Status'
              : 'স্ট্যাটাস পরিবর্তন করুন',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AgentCustomerStatus.values.map((status) {
            return RadioListTile<AgentCustomerStatus>(
              title: Text(AgentCustomer.getStatusText(status)),
              value: status,
              groupValue: customer.status,
              onChanged: (value) {
                if (value != null) {
                  ref.read(agentCustomersProvider.notifier).updateCustomerStatus(customer.id, value);
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Status updated successfully'
                            : 'স্ট্যাটাস সফলভাবে আপডেট করা হয়েছে',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                  setState(() {}); // Refresh UI
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Cancel'
                  : 'বাতিল',
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final customer = ref.watch(selectedAgentCustomerProvider);

    if (customer == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Customer Details'
                : 'গ্রাহকের বিবরণ',
          ),
        ),
        body: Center(
          child: Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'No customer selected'
                : 'কোন গ্রাহক নির্বাচিত নেই',
          ),
        ),
      );
    }

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(customer.name),
                background: Container(
                  color: ColorConstants.primaryColor,
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // Background gradient
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              ColorConstants.primaryColor,
                              ColorConstants.primaryColor.withOpacity(0.7),
                            ],
                          ),
                        ),
                      ),

                      // Customer avatar
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircleAvatar(
                              radius: 50,
                              backgroundColor: Colors.white,
                              child: Text(
                                customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?',
                                style: TextStyle(
                                  fontSize: 40,
                                  fontWeight: FontWeight.bold,
                                  color: ColorConstants.primaryColor,
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                customer.id,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Status badge
                      Positioned(
                        top: 50,
                        right: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: AgentCustomer.getStatusColor(customer.status),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            AgentCustomer.getStatusText(customer.status),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AgentCustomerFormScreen(
                          isEditing: true,
                          customer: customer,
                        ),
                      ),
                    ).then((_) => setState(() {})); // Refresh UI when returning
                  },
                  tooltip: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Edit'
                      : 'সম্পাদনা',
                ),
                IconButton(
                  icon: const Icon(Icons.sync),
                  onPressed: () => _showStatusChangeDialog(customer),
                  tooltip: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Change Status'
                      : 'স্ট্যাটাস পরিবর্তন',
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _showDeleteConfirmation(customer),
                  tooltip: ref.watch(languageProvider) == AppLanguage.english
                      ? 'Delete'
                      : 'মুছুন',
                ),
              ],
              bottom: TabBar(
                controller: _tabController,
                tabs: [
                  Tab(
                    text: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Info'
                        : 'তথ্য',
                  ),
                  Tab(
                    text: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Transactions'
                        : 'লেনদেন',
                  ),
                  Tab(
                    text: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Commissions'
                        : 'কমিশন',
                  ),
                  Tab(
                    text: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Notes'
                        : 'নোট',
                  ),
                ],
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            // Info tab
            _buildInfoTab(customer),

            // Transactions tab
            _buildTransactionsTab(customer),

            // Commissions tab
            _buildCommissionsTab(customer),

            // Notes tab
            _buildNotesTab(customer),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // In a real app, this would navigate to a transaction screen
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'New transaction feature coming soon'
                    : 'নতুন লেনদেন বৈশিষ্ট্য শীঘ্রই আসছে',
              ),
            ),
          );
        },
        backgroundColor: ColorConstants.primaryColor,
        icon: const Icon(Icons.add),
        label: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'New Transaction'
              : 'নতুন লেনদেন',
        ),
      ),
    );
  }

  /// Build info tab
  Widget _buildInfoTab(AgentCustomer customer) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Customer type
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AgentCustomer.getTypeColor(customer.type).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AgentCustomer.getTypeColor(customer.type),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.star,
                  color: AgentCustomer.getTypeColor(customer.type),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Customer Type'
                          : 'গ্রাহকের ধরন',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      AgentCustomer.getTypeText(customer.type),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AgentCustomer.getTypeColor(customer.type),
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Stats cards
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Balance'
                      : 'ব্যালেন্স',
                  '৳${customer.balance.toStringAsFixed(2)}',
                  Icons.account_balance_wallet,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Transactions'
                      : 'লেনদেন',
                  '${customer.totalTransactions}',
                  Icons.swap_horiz,
                  Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Contact information
          _buildSectionHeader(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Contact Information'
                : 'যোগাযোগের তথ্য',
          ),
          const SizedBox(height: 8),
          _buildInfoItem(
            Icons.phone,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Phone'
                : 'ফোন',
            customer.phone,
          ),
          if (customer.email != null)
            _buildInfoItem(
              Icons.email,
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Email'
                  : 'ইমেইল',
              customer.email!,
            ),
          if (customer.address != null)
            _buildInfoItem(
              Icons.location_on,
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Address'
                  : 'ঠিকানা',
              customer.address!,
            ),

          const SizedBox(height: 24),

          // Account information
          _buildSectionHeader(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Account Information'
                : 'অ্যাকাউন্টের তথ্য',
          ),
          const SizedBox(height: 8),
          _buildInfoItem(
            Icons.person,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Customer ID'
                : 'গ্রাহক আইডি',
            customer.id,
          ),
          _buildInfoItem(
            Icons.calendar_today,
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Created On'
                : 'তৈরি করা হয়েছে',
            _formatDate(customer.createdAt),
          ),
          if (customer.lastTransaction != null)
            _buildInfoItem(
              Icons.access_time,
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Last Transaction'
                  : 'শেষ লেনদেন',
              _formatDate(customer.lastTransaction!),
            ),
        ],
      ),
    );
  }

  /// Build transactions tab
  Widget _buildTransactionsTab(AgentCustomer customer) {
    // In a real app, this would show actual transactions
    // For now, we'll show a placeholder
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.swap_horiz,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Transaction History'
                : 'লেনদেনের ইতিহাস',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Total Transactions: ${customer.totalTransactions}'
                : 'মোট লেনদেন: ${customer.totalTransactions}',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Transaction history will be shown here'
                : 'লেনদেনের ইতিহাস এখানে দেখানো হবে',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build commissions tab
  Widget _buildCommissionsTab(AgentCustomer customer) {
    // Only show commissions for sub-agents and business customers
    if (customer.type != AgentCustomerType.subAgent && customer.type != AgentCustomerType.business) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.money_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Commission system is only available for Sub Agents and Business customers'
                  : 'কমিশন সিস্টেম শুধুমাত্র সাব এজেন্ট এবং ব্যবসায়িক গ্রাহকদের জন্য উপলব্ধ',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    // Get commissions for this agent
    final commissions = ref.watch(commissionsProvider)
        .where((commission) => commission.agentId == customer.id)
        .toList();

    // Sort by date (newest first)
    commissions.sort((a, b) => b.date.compareTo(a.date));

    // Calculate totals
    final totalCommission = commissions.fold(0.0, (sum, commission) => sum + commission.amount);
    final pendingCommission = commissions
        .where((c) => c.status == CommissionStatus.pending)
        .fold(0.0, (sum, commission) => sum + commission.amount);
    final approvedCommission = commissions
        .where((c) => c.status == CommissionStatus.approved)
        .fold(0.0, (sum, commission) => sum + commission.amount);
    final paidCommission = commissions
        .where((c) => c.status == CommissionStatus.paid)
        .fold(0.0, (sum, commission) => sum + commission.amount);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Commission summary card
          Card(
            color: ColorConstants.primaryColor.withAlpha(25),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: ColorConstants.primaryColor.withAlpha(50)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.monetization_on,
                        color: ColorConstants.primaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Commission Summary'
                            : 'কমিশন সারাংশ',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildCommissionSummaryItem(
                        'Total',
                        '৳${totalCommission.toStringAsFixed(2)}',
                        ColorConstants.primaryColor,
                      ),
                      _buildCommissionSummaryItem(
                        'Pending',
                        '৳${pendingCommission.toStringAsFixed(2)}',
                        Colors.amber,
                      ),
                      _buildCommissionSummaryItem(
                        'Approved',
                        '৳${approvedCommission.toStringAsFixed(2)}',
                        Colors.green,
                      ),
                      _buildCommissionSummaryItem(
                        'Paid',
                        '৳${paidCommission.toStringAsFixed(2)}',
                        Colors.blue,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Commission actions
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _showAddCommissionDialog(customer),
                  icon: const Icon(Icons.add),
                  label: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Add Commission'
                        : 'কমিশন যোগ করুন',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Navigate to commission management screen
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CommissionManagementScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.open_in_new),
                  label: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'View All'
                        : 'সব দেখুন',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade200,
                    foregroundColor: Colors.black87,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Recent commissions
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Recent Commissions'
                : 'সাম্প্রতিক কমিশন',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Commission list
          Expanded(
            child: commissions.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.monetization_on_outlined,
                          size: 48,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'No commissions yet'
                              : 'এখনও কোন কমিশন নেই',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: commissions.length,
                    itemBuilder: (context, index) {
                      final commission = commissions[index];
                      return _buildCommissionCard(commission);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  /// Build commission summary item
  Widget _buildCommissionSummaryItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// Build commission card
  Widget _buildCommissionCard(Commission commission) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 1,
      child: InkWell(
        onTap: () => _showCommissionDetails(commission),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with type and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getCommissionTypeIcon(commission.type),
                        color: ColorConstants.primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        Commission.getTypeText(commission.type),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getCommissionStatusColor(commission.status).withAlpha(25),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      Commission.getStatusText(commission.status),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _getCommissionStatusColor(commission.status),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),
              const Divider(height: 1),
              const SizedBox(height: 8),

              // Amount and date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    commission.formattedAmount,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _formatDate(commission.date),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 4),

              // Description
              Text(
                commission.description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade700,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show commission details
  void _showCommissionDetails(Commission commission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Commission Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('ID', commission.id),
              _buildDetailRow('Type', Commission.getTypeText(commission.type)),
              _buildDetailRow('Amount', commission.formattedAmount),
              _buildDetailRow('Rate', '${commission.rate}%'),
              _buildDetailRow('Date', _formatDate(commission.date)),
              _buildDetailRow('Status', Commission.getStatusText(commission.status)),
              if (commission.transactionId != null)
                _buildDetailRow('Transaction ID', commission.transactionId!),
              _buildDetailRow('Description', commission.description),
              if (commission.notes != null && commission.notes!.isNotEmpty)
                _buildDetailRow('Notes', commission.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (commission.status != CommissionStatus.paid && commission.status != CommissionStatus.rejected)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _showUpdateStatusDialog(commission);
              },
              child: const Text('Update Status'),
            ),
        ],
      ),
    );
  }

  /// Show update status dialog
  void _showUpdateStatusDialog(Commission commission) {
    CommissionStatus selectedStatus = commission.status;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Update Commission Status'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ...CommissionStatus.values.map((status) =>
                RadioListTile<CommissionStatus>(
                  title: Text(Commission.getStatusText(status)),
                  value: status,
                  groupValue: selectedStatus,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedStatus = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                ref.read(commissionsProvider.notifier).updateCommissionStatus(
                  commission.id,
                  selectedStatus,
                );
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Commission status updated to ${Commission.getStatusText(selectedStatus)}'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }

  /// Show add commission dialog
  void _showAddCommissionDialog(AgentCustomer customer) {
    final amountController = TextEditingController();
    final rateController = TextEditingController();
    final descriptionController = TextEditingController();
    final notesController = TextEditingController();
    final transactionIdController = TextEditingController();

    CommissionType selectedType = CommissionType.recharge;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Add Commission'
                : 'কমিশন যোগ করুন',
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Commission type
                DropdownButtonFormField<CommissionType>(
                  value: selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Commission Type',
                    border: OutlineInputBorder(),
                  ),
                  items: CommissionType.values.map((type) {
                    return DropdownMenuItem<CommissionType>(
                      value: type,
                      child: Text(Commission.getTypeText(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedType = value;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),

                // Amount
                TextField(
                  controller: amountController,
                  decoration: const InputDecoration(
                    labelText: 'Amount (৳)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.monetization_on),
                  ),
                  keyboardType: TextInputType.number,
                ),

                const SizedBox(height: 16),

                // Rate
                TextField(
                  controller: rateController,
                  decoration: const InputDecoration(
                    labelText: 'Rate (%)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.percent),
                  ),
                  keyboardType: TextInputType.number,
                ),

                const SizedBox(height: 16),

                // Description
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 2,
                ),

                const SizedBox(height: 16),

                // Transaction ID (optional)
                TextField(
                  controller: transactionIdController,
                  decoration: const InputDecoration(
                    labelText: 'Transaction ID (Optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.receipt),
                  ),
                ),

                const SizedBox(height: 16),

                // Notes (optional)
                TextField(
                  controller: notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes (Optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.note),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                amountController.dispose();
                rateController.dispose();
                descriptionController.dispose();
                notesController.dispose();
                transactionIdController.dispose();
              },
              child: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Cancel'
                    : 'বাতিল',
              ),
            ),
            TextButton(
              onPressed: () {
                // Validate inputs
                final amount = double.tryParse(amountController.text) ?? 0;
                final rate = double.tryParse(rateController.text) ?? 0;

                if (amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid amount'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (rate <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid rate'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                if (descriptionController.text.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a description'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                // Create new commission
                final commission = Commission(
                  id: ref.read(commissionsProvider.notifier).generateCommissionId(),
                  agentId: customer.id,
                  type: selectedType,
                  amount: amount,
                  rate: rate,
                  date: DateTime.now(),
                  description: descriptionController.text,
                  status: CommissionStatus.pending,
                  transactionId: transactionIdController.text.isNotEmpty
                      ? transactionIdController.text
                      : null,
                  notes: notesController.text.isNotEmpty
                      ? notesController.text
                      : null,
                );

                // Add commission
                ref.read(commissionsProvider.notifier).addCommission(commission);

                Navigator.pop(context);
                amountController.dispose();
                rateController.dispose();
                descriptionController.dispose();
                notesController.dispose();
                transactionIdController.dispose();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Commission added successfully'),
                    backgroundColor: Colors.green,
                  ),
                );

                // Refresh UI
                setState(() {});
              },
              child: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Add'
                    : 'যোগ করুন',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build detail row for dialog
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// Get commission type icon
  IconData _getCommissionTypeIcon(CommissionType type) {
    switch (type) {
      case CommissionType.recharge:
        return Icons.phone_android;
      case CommissionType.mobileBanking:
        return Icons.account_balance_wallet;
      case CommissionType.billPayment:
        return Icons.receipt;
      case CommissionType.ticketSale:
        return Icons.confirmation_number;
      case CommissionType.productSale:
        return Icons.shopping_bag;
      case CommissionType.referral:
        return Icons.people;
      case CommissionType.other:
        return Icons.more_horiz;
    }
  }

  /// Get commission status color
  Color _getCommissionStatusColor(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.pending:
        return Colors.amber;
      case CommissionStatus.approved:
        return Colors.green;
      case CommissionStatus.paid:
        return Colors.blue;
      case CommissionStatus.rejected:
        return Colors.red;
    }
  }

  /// Build notes tab
  Widget _buildNotesTab(AgentCustomer customer) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Customer Notes'
                : 'গ্রাহকের নোট',
          ),
          const SizedBox(height: 16),
          if (customer.notes != null && customer.notes!.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(customer.notes!),
            )
          else
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.note_alt_outlined,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'No notes available'
                        : 'কোন নোট নেই',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build section header
  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: 50,
          height: 3,
          decoration: BoxDecoration(
            color: ColorConstants.primaryColor,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ],
    );
  }

  /// Build info item
  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.grey.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build stat card
  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(75)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

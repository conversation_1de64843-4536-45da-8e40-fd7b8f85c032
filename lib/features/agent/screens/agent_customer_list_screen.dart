import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';
import 'package:irecharge_pro/data/providers/agent_customer_provider.dart';
import 'package:irecharge_pro/features/agent/screens/agent_customer_detail_screen.dart';
import 'package:irecharge_pro/features/agent/screens/agent_customer_form_screen.dart';

/// Agent Customer List Screen
class AgentCustomerListScreen extends ConsumerStatefulWidget {
  const AgentCustomerListScreen({super.key});

  @override
  ConsumerState<AgentCustomerListScreen> createState() => _AgentCustomerListScreenState();
}

class _AgentCustomerListScreenState extends ConsumerState<AgentCustomerListScreen> {
  bool _isLoading = false;
  bool _isFilterExpanded = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Add listener to search controller
    _searchController.addListener(() {
      final currentFilter = ref.read(agentCustomerFilterProvider);
      ref.read(agentCustomerFilterProvider.notifier).state =
          currentFilter.copyWith(searchQuery: _searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Toggle filter panel
  void _toggleFilterPanel() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
    });
  }

  /// Clear all filters
  void _clearFilters() {
    _searchController.clear();
    ref.read(agentCustomerFilterProvider.notifier).state = const AgentCustomerFilter();
  }

  /// Navigate to customer detail screen
  void _navigateToCustomerDetail(AgentCustomer customer) {
    ref.read(selectedAgentCustomerProvider.notifier).state = customer;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AgentCustomerDetailScreen(),
      ),
    );
  }

  /// Navigate to add customer screen
  void _navigateToAddCustomer() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AgentCustomerFormScreen(),
      ),
    );
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation(AgentCustomer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Delete Customer'
              : 'গ্রাহক মুছুন',
        ),
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Are you sure you want to delete ${customer.name}? This action cannot be undone.'
              : 'আপনি কি নিশ্চিত যে আপনি ${customer.name} মুছতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Cancel'
                  : 'বাতিল',
            ),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(agentCustomersProvider.notifier).deleteCustomer(customer.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Customer deleted successfully'
                        : 'গ্রাহক সফলভাবে মুছে ফেলা হয়েছে',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Delete'
                  : 'মুছুন',
            ),
          ),
        ],
      ),
    );
  }

  /// Show status change dialog
  void _showStatusChangeDialog(AgentCustomer customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Change Status'
              : 'স্ট্যাটাস পরিবর্তন করুন',
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AgentCustomerStatus.values.map((status) {
            return RadioListTile<AgentCustomerStatus>(
              title: Text(AgentCustomer.getStatusText(status)),
              value: status,
              groupValue: customer.status,
              onChanged: (value) {
                if (value != null) {
                  ref.read(agentCustomersProvider.notifier).updateCustomerStatus(customer.id, value);
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Status updated successfully'
                            : 'স্ট্যাটাস সফলভাবে আপডেট করা হয়েছে',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Cancel'
                  : 'বাতিল',
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filter = ref.watch(agentCustomerFilterProvider);
    final customers = ref.watch(agentCustomersProvider);
    final filteredCustomers = ref.read(agentCustomersProvider.notifier).getFilteredCustomers(filter);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Agent Management'
              : 'এজেন্ট ব্যবস্থাপনা',
        ),
        actions: [
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _toggleFilterPanel,
            tooltip: _isFilterExpanded ? 'Hide Filters' : 'Show Filters',
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Search customers...'
                    : 'গ্রাহক অনুসন্ধান করুন...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0),
              ),
            ),
          ),

          // Filter panel
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isFilterExpanded ? null : 0,
            child: Card(
              margin: const EdgeInsets.all(16),
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          ref.watch(languageProvider) == AppLanguage.english
                              ? 'Filter Customers'
                              : 'গ্রাহক ফিল্টার করুন',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton.icon(
                          icon: const Icon(Icons.refresh, size: 16),
                          label: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Reset'
                                : 'রিসেট',
                            style: const TextStyle(fontSize: 12),
                          ),
                          onPressed: _clearFilters,
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 0,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Divider(),

                    // Status filter
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Status'
                          : 'স্ট্যাটাস',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        FilterChip(
                          label: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'All'
                                : 'সব',
                          ),
                          selected: filter.status == null,
                          onSelected: (selected) {
                            if (selected) {
                              ref.read(agentCustomerFilterProvider.notifier).state =
                                  filter.copyWith(clearStatus: true);
                            }
                          },
                        ),
                        ...AgentCustomerStatus.values.map((status) {
                          return FilterChip(
                            label: Text(AgentCustomer.getStatusText(status)),
                            selected: filter.status == status,
                            onSelected: (selected) {
                              ref.read(agentCustomerFilterProvider.notifier).state =
                                  filter.copyWith(status: selected ? status : null, clearStatus: !selected);
                            },
                            backgroundColor: Colors.grey.shade100,
                            selectedColor: AgentCustomer.getStatusColor(status).withOpacity(0.1),
                            checkmarkColor: AgentCustomer.getStatusColor(status),
                            labelStyle: TextStyle(
                              color: filter.status == status
                                  ? AgentCustomer.getStatusColor(status)
                                  : Colors.black,
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Customer type filter
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Customer Type'
                          : 'গ্রাহকের ধরন',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        FilterChip(
                          label: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'All'
                                : 'সব',
                          ),
                          selected: filter.type == null,
                          onSelected: (selected) {
                            if (selected) {
                              ref.read(agentCustomerFilterProvider.notifier).state =
                                  filter.copyWith(clearType: true);
                            }
                          },
                        ),
                        ...AgentCustomerType.values.map((type) {
                          return FilterChip(
                            label: Text(AgentCustomer.getTypeText(type)),
                            selected: filter.type == type,
                            onSelected: (selected) {
                              ref.read(agentCustomerFilterProvider.notifier).state =
                                  filter.copyWith(type: selected ? type : null, clearType: !selected);
                            },
                            backgroundColor: Colors.grey.shade100,
                            selectedColor: AgentCustomer.getTypeColor(type).withOpacity(0.1),
                            checkmarkColor: AgentCustomer.getTypeColor(type),
                            labelStyle: TextStyle(
                              color: filter.type == type
                                  ? AgentCustomer.getTypeColor(type)
                                  : Colors.black,
                            ),
                          );
                        }).toList(),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Sort options
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Sort By'
                          : 'সাজানোর ক্রম',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<AgentCustomerSortBy>(
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                      ),
                      value: filter.sortBy,
                      items: [
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.nameAsc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Name (A-Z)'
                                : 'নাম (A-Z)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.nameDesc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Name (Z-A)'
                                : 'নাম (Z-A)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.balanceDesc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Balance (High to Low)'
                                : 'ব্যালেন্স (উচ্চ থেকে নিম্ন)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.balanceAsc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Balance (Low to High)'
                                : 'ব্যালেন্স (নিম্ন থেকে উচ্চ)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.transactionsDesc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Transactions (High to Low)'
                                : 'লেনদেন (উচ্চ থেকে নিম্ন)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.dateCreatedDesc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Date Created (Newest)'
                                : 'তৈরির তারিখ (সর্বাধুনিক)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.dateCreatedAsc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Date Created (Oldest)'
                                : 'তৈরির তারিখ (পুরাতন)',
                          ),
                        ),
                        DropdownMenuItem(
                          value: AgentCustomerSortBy.lastTransactionDesc,
                          child: Text(
                            ref.watch(languageProvider) == AppLanguage.english
                                ? 'Last Transaction (Recent)'
                                : 'শেষ লেনদেন (সাম্প্রতিক)',
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          ref.read(agentCustomerFilterProvider.notifier).state =
                              filter.copyWith(sortBy: value);
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Customer count
          if (filteredCustomers.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? '${filteredCustomers.length} customers found'
                        : '${filteredCustomers.length}টি গ্রাহক পাওয়া গেছে',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Total Balance: ৳${_calculateTotalBalance(filteredCustomers).toStringAsFixed(2)}'
                        : 'মোট ব্যালেন্স: ৳${_calculateTotalBalance(filteredCustomers).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

          // Customer list
          Expanded(
            child: _isLoading
                ? const Center(child: LoadingIndicator())
                : filteredCustomers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.people_alt_outlined,
                              size: 64,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'No customers found'
                                  : 'কোন গ্রাহক পাওয়া যায়নি',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            if (_searchController.text.isNotEmpty || filter.status != null || filter.type != null)
                              TextButton.icon(
                                onPressed: _clearFilters,
                                icon: const Icon(Icons.filter_alt_off),
                                label: Text(
                                  ref.watch(languageProvider) == AppLanguage.english
                                      ? 'Clear Filters'
                                      : 'ফিল্টার মুছুন',
                                ),
                              ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredCustomers.length,
                        padding: const EdgeInsets.all(8),
                        itemBuilder: (context, index) {
                          final customer = filteredCustomers[index];
                          return _buildCustomerCard(customer);
                        },
                      ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddCustomer,
        backgroundColor: ColorConstants.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  /// Calculate total balance
  double _calculateTotalBalance(List<AgentCustomer> customers) {
    double total = 0;
    for (var customer in customers) {
      total += customer.balance;
    }
    return total;
  }

  /// Build customer card
  Widget _buildCustomerCard(AgentCustomer customer) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: InkWell(
        onTap: () => _navigateToCustomerDetail(customer),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Customer avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: ColorConstants.primaryColor.withOpacity(0.1),
                    child: Text(
                      customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: ColorConstants.primaryColor,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Customer details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                customer.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            _buildStatusBadge(customer.status),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          customer.phone,
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 14,
                          ),
                        ),
                        if (customer.email != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            customer.email!,
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 14,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),
              const Divider(height: 1),
              const SizedBox(height: 12),

              // Customer stats
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildStatItem(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Balance'
                        : 'ব্যালেন্স',
                    '৳${customer.balance.toStringAsFixed(2)}',
                    Icons.account_balance_wallet,
                  ),
                  _buildStatItem(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Transactions'
                        : 'লেনদেন',
                    '${customer.totalTransactions}',
                    Icons.swap_horiz,
                  ),
                  _buildStatItem(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Type'
                        : 'ধরন',
                    AgentCustomer.getTypeText(customer.type),
                    Icons.category,
                    color: AgentCustomer.getTypeColor(customer.type),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Edit button
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () {
                      ref.read(selectedAgentCustomerProvider.notifier).state = customer;
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AgentCustomerFormScreen(
                            isEditing: true,
                            customer: customer,
                          ),
                        ),
                      );
                    },
                    tooltip: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Edit'
                        : 'সম্পাদনা',
                    color: Colors.blue,
                  ),

                  // Status change button
                  IconButton(
                    icon: const Icon(Icons.sync, size: 20),
                    onPressed: () => _showStatusChangeDialog(customer),
                    tooltip: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Change Status'
                        : 'স্ট্যাটাস পরিবর্তন',
                    color: Colors.orange,
                  ),

                  // Delete button
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: () => _showDeleteConfirmation(customer),
                    tooltip: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Delete'
                        : 'মুছুন',
                    color: Colors.red,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build status badge
  Widget _buildStatusBadge(AgentCustomerStatus status) {
    final color = AgentCustomer.getStatusColor(status);
    final text = AgentCustomer.getStatusText(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build stat item
  Widget _buildStatItem(String label, String value, IconData icon, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          color: color ?? Colors.grey.shade700,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color ?? Colors.black,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}

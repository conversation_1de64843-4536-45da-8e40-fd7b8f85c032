import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/data/models/agent_customer_model.dart';

/// Agent Customer Search Delegate
class TransactionSearchDelegate extends SearchDelegate<AgentCustomer?> {
  final List<AgentCustomer> transactions;
  final Function(AgentCustomer) onTransactionSelected;
  final WidgetRef? ref;

  TransactionSearchDelegate({
    required this.transactions,
    required this.onTransactionSelected,
    this.ref,
  });

  @override
  String get searchFieldLabel => ref?.watch(languageProvider) == AppLanguage.english
      ? 'Search customers...'
      : 'গ্রাহক অনুসন্ধান করুন...';

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults();
  }

  Widget _buildSearchResults() {
    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              ref?.watch(languageProvider) == AppLanguage.english
                  ? 'Search for customers by name, phone, email or ID'
                  : 'নাম, ফোন, ইমেইল বা আইডি দ্বারা গ্রাহক অনুসন্ধান করুন',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final filteredCustomers = transactions.where((customer) {
      return customer.name.toLowerCase().contains(query.toLowerCase()) ||
             customer.phone.toLowerCase().contains(query.toLowerCase()) ||
             (customer.email?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
             customer.id.toLowerCase().contains(query.toLowerCase());
    }).toList();

    if (filteredCustomers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              ref?.watch(languageProvider) == AppLanguage.english
                  ? 'No customers found matching "$query"'
                  : '"$query" এর সাথে মিলে যাওয়া কোন গ্রাহক পাওয়া যায়নি',
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredCustomers.length,
      itemBuilder: (context, index) {
        final customer = filteredCustomers[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: ColorConstants.primaryColor.withOpacity(0.1),
            child: Text(
              customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '?',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: ColorConstants.primaryColor,
              ),
            ),
          ),
          title: Text(
            customer.name,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(customer.phone),
              if (customer.email != null)
                Text(
                  customer.email!,
                  style: const TextStyle(fontSize: 12),
                ),
            ],
          ),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AgentCustomer.getStatusColor(customer.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              AgentCustomer.getStatusText(customer.status),
              style: TextStyle(
                color: AgentCustomer.getStatusColor(customer.status),
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          onTap: () {
            close(context, customer);
            onTransactionSelected(customer);
          },
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';

/// Notification model
class Notification {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final NotificationType type;
  final bool isRead;
  final String? actionUrl;

  Notification({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.type,
    this.isRead = false,
    this.actionUrl,
  });
}

/// Notification type enum
enum NotificationType {
  transaction,
  promotion,
  account,
  system,
}

/// Notifications screen
class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key});

  @override
  ConsumerState<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  // Mock notifications
  final List<Notification> _notifications = [
    Notification(
      id: '1',
      title: 'Recharge Successful',
      message: 'Your mobile recharge of ৳100 to *********** was successful.',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      type: NotificationType.transaction,
    ),
    Notification(
      id: '2',
      title: 'Special Offer',
      message: 'Get 10% cashback on your next recharge! Offer valid until tomorrow.',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      type: NotificationType.promotion,
      isRead: true,
    ),
    Notification(
      id: '3',
      title: 'Account Update',
      message: 'Your profile information has been updated successfully.',
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
      type: NotificationType.account,
    ),
    Notification(
      id: '4',
      title: 'Money Received',
      message: 'You have received ৳500 from John Doe.',
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      type: NotificationType.transaction,
    ),
    Notification(
      id: '5',
      title: 'System Maintenance',
      message: 'Our system will be under maintenance from 2:00 AM to 4:00 AM tomorrow.',
      timestamp: DateTime.now().subtract(const Duration(days: 3)),
      type: NotificationType.system,
      isRead: true,
    ),
    Notification(
      id: '6',
      title: 'New Feature Available',
      message: 'You can now pay your electricity bills through our app!',
      timestamp: DateTime.now().subtract(const Duration(days: 4)),
      type: NotificationType.system,
    ),
    Notification(
      id: '7',
      title: 'Weekend Offer',
      message: 'Enjoy 5% discount on all transactions this weekend!',
      timestamp: DateTime.now().subtract(const Duration(days: 5)),
      type: NotificationType.promotion,
      isRead: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('notifications')),
        actions: [
          IconButton(
            icon: const Icon(Icons.done_all),
            onPressed: _markAllAsRead,
            tooltip: 'Mark all as read',
          ),
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            onPressed: _showClearConfirmation,
            tooltip: 'Clear all',
          ),
        ],
      ),
      body: _notifications.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              itemCount: _notifications.length,
              itemBuilder: (context, index) {
                final notification = _notifications[index];
                return _buildNotificationItem(notification);
              },
            ),
    );
  }
  
  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          const Text(
            'No Notifications',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You don\'t have any notifications yet',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build notification item
  Widget _buildNotificationItem(Notification notification) {
    return Dismissible(
      key: Key(notification.id),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) {
        setState(() {
          _notifications.removeWhere((item) => item.id == notification.id);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Notification removed'),
            action: SnackBarAction(
              label: 'Undo',
              onPressed: () {
                // TODO: Implement undo
              },
            ),
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        color: notification.isRead ? null : Colors.blue.shade50,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: _getNotificationColor(notification.type).withAlpha(30),
            child: Icon(
              _getNotificationIcon(notification.type),
              color: _getNotificationColor(notification.type),
            ),
          ),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(notification.message),
              const SizedBox(height: 4),
              Text(
                _formatDateTime(notification.timestamp),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          isThreeLine: true,
          trailing: notification.isRead
              ? null
              : Container(
                  width: 12,
                  height: 12,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.blue,
                  ),
                ),
          onTap: () => _markAsRead(notification),
        ),
      ),
    );
  }
  
  /// Get notification icon
  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.transaction:
        return Icons.payment;
      case NotificationType.promotion:
        return Icons.local_offer;
      case NotificationType.account:
        return Icons.account_circle;
      case NotificationType.system:
        return Icons.info;
    }
  }
  
  /// Get notification color
  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.transaction:
        return Colors.green;
      case NotificationType.promotion:
        return Colors.orange;
      case NotificationType.account:
        return Colors.blue;
      case NotificationType.system:
        return Colors.purple;
    }
  }
  
  /// Format date and time
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
  
  /// Mark notification as read
  void _markAsRead(Notification notification) {
    setState(() {
      final index = _notifications.indexWhere((item) => item.id == notification.id);
      if (index != -1) {
        _notifications[index] = Notification(
          id: notification.id,
          title: notification.title,
          message: notification.message,
          timestamp: notification.timestamp,
          type: notification.type,
          isRead: true,
          actionUrl: notification.actionUrl,
        );
      }
    });
  }
  
  /// Mark all notifications as read
  void _markAllAsRead() {
    setState(() {
      for (var i = 0; i < _notifications.length; i++) {
        final notification = _notifications[i];
        _notifications[i] = Notification(
          id: notification.id,
          title: notification.title,
          message: notification.message,
          timestamp: notification.timestamp,
          type: notification.type,
          isRead: true,
          actionUrl: notification.actionUrl,
        );
      }
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All notifications marked as read'),
      ),
    );
  }
  
  /// Show clear confirmation dialog
  void _showClearConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text('Are you sure you want to clear all notifications?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _notifications.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All notifications cleared'),
                ),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}

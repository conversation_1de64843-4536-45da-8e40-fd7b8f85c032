import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/data/models/mobile_banking_operator_model.dart';
import 'package:irecharge_pro/routes/route_names.dart';
import 'package:permission_handler/permission_handler.dart';

/// Mobile Banking screen
class MobileBankingScreen extends ConsumerStatefulWidget {
  const MobileBankingScreen({super.key});

  @override
  ConsumerState<MobileBankingScreen> createState() => _MobileBankingScreenState();
}

class _MobileBankingScreenState extends ConsumerState<MobileBankingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _pinController = TextEditingController();

  // Selected operator
  MobileBankingOperator? _selectedOperator;

  // Selected operator type
  String? _selectedOperatorType;

  // Loading state
  bool _isLoading = false;

  // Current step
  int _currentStep = 0;

  // Operator types
  final Map<String, List<String>> _operatorTypes = {
    'bkash': ['Personal', 'Agent', 'Merchant'],
    'nagad': ['Personal', 'Agent', 'Merchant'],
    'rocket': ['Personal', 'Agent', 'Merchant'],
    'upay': ['Personal', 'Agent'],
  };

  @override
  void dispose() {
    _phoneController.dispose();
    _amountController.dispose();
    _pinController.dispose();
    super.dispose();
  }

  /// Get operator from phone number - not used for mobile banking
  /// as we can't determine the mobile banking operator from the phone number
  MobileBankingOperator? _getOperatorFromPhone(String phone) {
    // Mobile banking operators can't be determined from phone number
    // This is kept for consistency with the interface
    return null;
  }

  /// Handle phone number change
  void _onPhoneChanged(String value) {
    if (value.length >= 3) {
      final operator = _getOperatorFromPhone(value);
      if (operator != null && _selectedOperator?.id != operator.id) {
        setState(() {
          _selectedOperator = operator;
          _selectedOperatorType = null;
        });
      }
    } else if (_selectedOperator != null) {
      setState(() {
        _selectedOperator = null;
        _selectedOperatorType = null;
      });
    }
  }

  /// Select operator
  void _selectOperator(MobileBankingOperator operator) {
    setState(() {
      _selectedOperator = operator;
      _selectedOperatorType = null;
    });
  }

  /// Select operator type
  void _selectOperatorType(String type) {
    setState(() {
      _selectedOperatorType = type;
    });
  }

  /// Pick a contact from the phone's contact list
  Future<void> _pickContact() async {
    try {
      // Show permission rationale first
      bool shouldProceed = await _showPermissionDialog();
      if (!shouldProceed) return;

      // Request contacts permission
      final status = await Permission.contacts.request();

      if (status.isGranted) {
        // Get all contacts (with a phone number)
        final contacts = await FlutterContacts.getContacts(
          withProperties: true,
          withPhoto: false,
        );

        if (!mounted) return;

        // Show contact picker dialog
        final contact = await _showContactsDialog(contacts);

        if (contact != null && contact.phones.isNotEmpty) {
          // Get the first phone number
          String phoneNumber = contact.phones.first.number;

          // Clean the phone number (remove spaces, dashes, etc.)
          phoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

          // If the number starts with +880 (Bangladesh country code), remove it
          if (phoneNumber.startsWith('+880')) {
            phoneNumber = '0${phoneNumber.substring(4)}';
          }

          // Update the phone controller
          setState(() {
            _phoneController.text = phoneNumber;
            _onPhoneChanged(phoneNumber);
          });

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Contact selected: ${contact.displayName}'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } else if (contact != null) {
          _showErrorSnackBar('No phone number found for this contact');
        }
      } else if (status.isPermanentlyDenied) {
        // Show settings dialog if permission is permanently denied
        await _showOpenSettingsDialog();
      } else {
        _showErrorSnackBar('Permission to access contacts denied');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error picking contact: $e');
      }
    }
  }

  /// Show permission rationale dialog
  Future<bool> _showPermissionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Permission'),
        content: const Text(
          'This app needs access to your contacts to select a recipient for mobile banking. '
          'Would you like to grant permission?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Not Now'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Continue'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show open settings dialog
  Future<void> _showOpenSettingsDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Permission Required'),
        content: const Text(
          'Contact permission is required to select contacts. '
          'Please enable it in app settings.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// Show contacts dialog
  Future<Contact?> _showContactsDialog(List<Contact> contacts) async {
    // Filter contacts to only include those with phone numbers
    final filteredContacts = contacts.where((c) => c.phones.isNotEmpty).toList();

    // Sort contacts alphabetically
    filteredContacts.sort((a, b) => a.displayName.compareTo(b.displayName));

    return showDialog<Contact>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.contacts, color: ColorConstants.primaryColor),
            const SizedBox(width: 8),
            const Text('Select Contact'),
            const Spacer(),
            // Search icon (for future implementation)
            IconButton(
              icon: const Icon(Icons.search, size: 20),
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Search coming soon')),
                );
              },
            ),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400, // Taller dialog
          child: filteredContacts.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.contact_phone, size: 48, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No contacts found with phone numbers',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // Contact count
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        '${filteredContacts.length} contacts',
                        style: const TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ),
                    // Divider
                    const Divider(),
                    // Contact list
                    Expanded(
                      child: ListView.builder(
                        itemCount: filteredContacts.length,
                        itemBuilder: (context, index) {
                          final contact = filteredContacts[index];
                          // Get first letter for grouping
                          final firstLetter = contact.displayName.isNotEmpty
                              ? contact.displayName[0].toUpperCase()
                              : '#';

                          // Show header if this is the first contact with this letter
                          final showHeader = index == 0 ||
                              firstLetter !=
                                  (filteredContacts[index - 1].displayName.isNotEmpty
                                      ? filteredContacts[index - 1].displayName[0].toUpperCase()
                                      : '#');

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (showHeader) ...[
                                Padding(
                                  padding: const EdgeInsets.only(left: 16, top: 8, bottom: 4),
                                  child: Text(
                                    firstLetter,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: ColorConstants.primaryColor,
                                    ),
                                  ),
                                ),
                              ],
                              ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: ColorConstants.primaryColor.withAlpha(30),
                                  child: Text(
                                    contact.displayName.isNotEmpty
                                        ? contact.displayName[0].toUpperCase()
                                        : '?',
                                    style: const TextStyle(
                                      color: ColorConstants.primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  contact.displayName,
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: contact.phones.isNotEmpty
                                    ? Text(contact.phones.first.number)
                                    : const Text('No phone number'),
                                onTap: () {
                                  Navigator.of(context).pop(contact);
                                },
                              ),
                              const Divider(height: 1, indent: 70),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// Continue to next step
  void _continueToNextStep() {
    if (_currentStep == 0) {
      if (!_formKey.currentState!.validate()) {
        return;
      }

      if (_selectedOperator == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select an operator'),
            backgroundColor: ColorConstants.errorColor,
          ),
        );
        return;
      }

      if (_selectedOperatorType == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please select an operator type'),
            backgroundColor: ColorConstants.errorColor,
          ),
        );
        return;
      }

      setState(() {
        _currentStep = 1;
      });
    } else if (_currentStep == 1) {
      if (_amountController.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please enter an amount'),
            backgroundColor: ColorConstants.errorColor,
          ),
        );
        return;
      }

      final amount = double.tryParse(_amountController.text);
      if (amount == null || amount <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please enter a valid amount'),
            backgroundColor: ColorConstants.errorColor,
          ),
        );
        return;
      }

      // Show PIN verification dialog
      _showPinVerificationDialog();
    }
  }

  /// Show PIN verification dialog
  void _showPinVerificationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Enter PIN'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please enter your 4-digit PIN to confirm the transaction'),
            const SizedBox(height: 16),
            TextField(
              controller: _pinController,
              keyboardType: TextInputType.number,
              maxLength: 4,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'PIN',
                border: OutlineInputBorder(),
                counterText: '',
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _pinController.clear();
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (_pinController.text.length != 4) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a 4-digit PIN'),
                    backgroundColor: ColorConstants.errorColor,
                  ),
                );
                return;
              }

              Navigator.pop(context);
              _processMobileBanking();
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  /// Process mobile banking
  void _processMobileBanking() {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show success dialog
      _showSuccessDialog();
    });
  }

  /// Show success dialog
  void _showSuccessDialog() {
    final amount = double.parse(_amountController.text);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
            const SizedBox(width: 8),
            const Text('Success'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your mobile banking transaction has been completed successfully.'),
            const SizedBox(height: 16),
            _buildDetailRow('Transaction ID', '${DateTime.now().millisecondsSinceEpoch}'),
            _buildDetailRow('Phone', _phoneController.text),
            _buildDetailRow('Operator', _selectedOperator!.name),
            _buildDetailRow('Type', _selectedOperatorType!),
            _buildDetailRow('Amount', '৳${amount.toStringAsFixed(2)}'),
            _buildDetailRow('Fee', '৳0.00'),
            _buildDetailRow('Total', '৳${amount.toStringAsFixed(2)}'),
            _buildDetailRow('Date', DateTime.now().toString().substring(0, 16)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset the form
              setState(() {
                _currentStep = 0;
                _phoneController.clear();
                _amountController.clear();
                _pinController.clear();
                _selectedOperator = null;
                _selectedOperatorType = null;
              });
            },
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Reset the form
              setState(() {
                _currentStep = 0;
                _phoneController.clear();
                _amountController.clear();
                _pinController.clear();
                _selectedOperator = null;
                _selectedOperatorType = null;
              });

              // Navigate to history
              context.push('/wallet/history');
            },
            child: const Text('View History'),
          ),
        ],
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('mobile_banking')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              context.go(RouteNames.history);
              // Navigate to the Mobile Banking tab in the history screen
            },
            tooltip: 'Mobile Banking History',
          ),
        ],
      ),
      body: _currentStep == 0 ? _buildStep1() : _buildStep2(),
    );
  }

  /// Build step 1: Enter phone number and select operator
  Widget _buildStep1() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Text(
              'Mobile Banking',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Send money to mobile banking accounts',
              style: TextStyle(
                color: ColorConstants.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 24),

            // Phone number input with contact picker
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomTextField(
                    controller: _phoneController,
                    labelText: 'Phone Number',
                    hintText: 'Enter recipient phone number',
                    keyboardType: TextInputType.phone,
                    prefixIcon: const Icon(Icons.phone_android),
                    validator: Validators.validateBangladeshPhone,
                    onChanged: _onPhoneChanged,
                  ),
                ),
                const SizedBox(width: 8),
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: InkWell(
                    onTap: _pickContact,
                    child: Container(
                      height: 56,
                      width: 56,
                      decoration: BoxDecoration(
                        color: ColorConstants.primaryColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.contacts,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Operator selection
            const Text(
              'Select Operator',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Operator grid - smaller cards in a row
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: MobileBankingOperator.getAllOperators().map((operator) {
                final isSelected = _selectedOperator?.id == operator.id;
                return InkWell(
                  onTap: () => _selectOperator(operator),
                  child: Container(
                    width: 70, // Fixed smaller width
                    height: 70, // Fixed smaller height
                    decoration: BoxDecoration(
                      color: isSelected
                          ? operator.color.withAlpha(30)
                          : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? operator.color : Colors.transparent,
                        width: 2,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: operator.color.withAlpha(76), // 0.3 * 255 = 76
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircleAvatar(
                          radius: 18, // Even smaller radius
                          backgroundColor: Colors.white,
                          child: Text(
                            operator.name,
                            style: TextStyle(
                              color: operator.color,
                              fontWeight: FontWeight.bold,
                              fontSize: operator.name.length > 5 ? 8 : 10, // Smaller font
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          operator.name,
                          style: TextStyle(
                            fontSize: 10, // Smaller text
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),

            const SizedBox(height: 24),

            // Operator type selection
            if (_selectedOperator != null) ...[
              const Text(
                'Select Account Type',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Operator type grid
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 3,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: (_operatorTypes[_selectedOperator!.id] ?? []).map((type) {
                  final isSelected = _selectedOperatorType == type;
                  return InkWell(
                    onTap: () => _selectOperatorType(type),
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? _selectedOperator!.color.withAlpha(30)
                            : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected ? _selectedOperator!.color : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            radius: 24,
                            backgroundColor: Colors.white,
                            child: Icon(
                              type == 'Personal'
                                  ? Icons.person
                                  : type == 'Agent'
                                      ? Icons.store
                                      : Icons.business,
                              color: _selectedOperator!.color,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            type,
                            style: TextStyle(
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),
            ],

            // Continue button
            CustomButton(
              text: 'Continue',
              onPressed: _continueToNextStep,
            ),
          ],
        ),
      ),
    );
  }

  /// Build step 2: Enter amount and send
  Widget _buildStep2() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Text(
            'Enter Amount',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Enter the amount you want to send',
            style: TextStyle(
              color: ColorConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 24),

          // Recipient info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Recipient',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: _selectedOperator!.color.withAlpha(30),
                        child: Icon(
                          Icons.account_balance_wallet,
                          color: _selectedOperator!.color,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${_selectedOperator!.name} - $_selectedOperatorType',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _phoneController.text,
                              style: const TextStyle(
                                color: ColorConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            _currentStep = 0;
                          });
                        },
                        icon: const Icon(Icons.edit),
                        label: const Text('Change'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Amount input
          CustomTextField(
            controller: _amountController,
            labelText: 'Amount',
            hintText: 'Enter amount',
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            prefixIcon: const Icon(Icons.attach_money),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
            ],
          ),

          const SizedBox(height: 8),

          // Quick amount buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildQuickAmountButton(500),
              _buildQuickAmountButton(1000),
              _buildQuickAmountButton(2000),
              _buildQuickAmountButton(5000),
            ],
          ),

          const SizedBox(height: 24),

          // Fee information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Transaction Details',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Amount'),
                      Text(
                        _amountController.text.isEmpty
                            ? '৳0.00'
                            : '৳${double.parse(_amountController.text).toStringAsFixed(2)}',
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Fee'),
                      Text('৳0.00'),
                    ],
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Total',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _amountController.text.isEmpty
                            ? '৳0.00'
                            : '৳${double.parse(_amountController.text).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Send button
          CustomButton(
            text: 'Send Now',
            onPressed: _continueToNextStep,
            isLoading: _isLoading,
          ),

          const SizedBox(height: 16),

          // Back button
          OutlinedButton(
            onPressed: () {
              setState(() {
                _currentStep = 0;
              });
            },
            style: OutlinedButton.styleFrom(
              minimumSize: const Size(double.infinity, 48),
            ),
            child: const Text('Back'),
          ),
        ],
      ),
    );
  }

  /// Build quick amount button
  Widget _buildQuickAmountButton(double amount) {
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _amountController.text = amount.toString();
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey.shade100,
        foregroundColor: ColorConstants.primaryColor,
      ),
      child: Text('৳${amount.toInt()}'),
    );
  }
}

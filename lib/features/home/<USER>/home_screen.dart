import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/providers/theme_provider.dart';
import 'package:irecharge_pro/data/models/product_model.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Home screen of the application
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {

  @override
  Widget build(BuildContext context) {
    ref.watch(themeModeProvider); // Keep watching theme mode for changes

    return Scaffold(
      appBar: AppBar(
        // Remove the centered title and use a custom title widget
        title: null,
        automaticallyImplyLeading: false, // Remove back button
        leadingWidth: 0, // Remove leading space
        titleSpacing: 0, // Remove default title spacing
        // Custom title bar with app name on the left
        flexibleSpace: Padding(
          padding: const EdgeInsets.only(top: 40, left: 16, right: 8),
          child: Row(
            children: [
              // App name on the left
              Text(
                AppConstants.appName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorConstants.primaryColor,
                ),
              ),
              const Spacer(), // Push actions to the right

              // Action buttons directly in the row
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {
                  context.push(RouteNames.notifications);
                },
              ),
              IconButton(
                icon: const Icon(Icons.settings_outlined),
                onPressed: () {
                  context.push(RouteNames.settings);
                },
                tooltip: AppLocalizations.of(context).translate('settings'),
              ),
              IconButton(
                icon: const Icon(Icons.person_outline),
                onPressed: () {
                  context.push(RouteNames.profile);
                },
              ),
            ],
          ),
        ),
        actions: [], // Empty since we moved the actions to the flexibleSpace
      ),
      body: const _DashboardPage(),
    );
  }
}

/// Dashboard page (Home tab)
class _DashboardPage extends StatelessWidget {
  const _DashboardPage();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Balance card - smaller version
          Card(
            color: ColorConstants.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Wallet Balance',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    '৳ 5,000.00',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildActionButton(
                        icon: Icons.add,
                        label: 'Add',
                        onTap: () {
                          context.push(RouteNames.addMoney);
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.send,
                        label: 'Send',
                        onTap: () {
                          context.push(RouteNames.sendMoney);
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.account_balance,
                        label: 'Bank',
                        onTap: () {
                          context.push(RouteNames.bankTransfer);
                        },
                      ),
                      _buildActionButton(
                        icon: Icons.history,
                        label: 'History',
                        onTap: () {
                          context.push(RouteNames.walletHistory);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Quick actions
          const Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 4,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildQuickAction(
                icon: Icons.phone_android,
                label: 'Recharge',
                onTap: () {
                  context.push(RouteNames.recharge);
                },
              ),
              _buildQuickAction(
                icon: Icons.account_balance_wallet,
                label: 'Mobile Banking',
                onTap: () {
                  context.push(RouteNames.mobileBanking);
                },
              ),
              _buildQuickAction(
                icon: Icons.airplane_ticket,
                label: 'Tickets',
                onTap: () {
                  context.push(RouteNames.tickets);
                },
              ),
              _buildQuickAction(
                icon: Icons.receipt,
                label: 'Billpay',
                onTap: () {
                  context.push(RouteNames.bills);
                },
              ),
              _buildQuickAction(
                icon: Icons.shopping_bag,
                label: 'Shop',
                onTap: () {
                  context.push(RouteNames.shop);
                },
              ),
              _buildQuickAction(
                icon: Icons.card_giftcard,
                label: 'Offers',
                onTap: () {
                  context.push(RouteNames.offers);
                },
              ),

              _buildQuickAction(
                icon: Icons.people,
                label: 'Agent',
                onTap: () {
                  context.push(RouteNames.agentCustomers);
                },
              ),
              _buildQuickAction(
                icon: Icons.monetization_on,
                label: 'Commissions',
                onTap: () {
                  context.push(RouteNames.agentCommissions);
                },
              ),
              _buildQuickAction(
                icon: Icons.support_agent,
                label: 'Support',
                onTap: () {
                  context.push(RouteNames.support);
                },
              ),
              _buildQuickAction(
                icon: Icons.settings,
                label: AppLocalizations.of(context).translate('settings'),
                onTap: () {
                  context.push(RouteNames.settings);
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Featured Products
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4,
                      height: 20,
                      decoration: BoxDecoration(
                        color: ColorConstants.primaryColor,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Featured Products',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () {
                    context.push(RouteNames.shop);
                  },
                  icon: const Icon(Icons.arrow_forward, size: 16),
                  label: const Text('View All'),
                  style: TextButton.styleFrom(
                    foregroundColor: ColorConstants.primaryColor,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Featured products horizontal list
          SizedBox(
            height: 160, // Increased height for better visibility
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              itemCount: DemoProducts.getFeatured().length,
              itemBuilder: (context, index) {
                final product = DemoProducts.getFeatured()[index];
                return _buildProductCard(context, product);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 18,
            ),
          ),
          const SizedBox(height: 3),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: ColorConstants.primaryLightColor.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: ColorConstants.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build product card for featured products
  Widget _buildProductCard(BuildContext context, Product product) {
    final discountPercentage = product.discountPercentage;

    return Container(
      width: 120, // Increased width for better visibility
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Card(
        clipBehavior: Clip.antiAlias,
        elevation: 3, // Increased elevation for better shadow
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: BorderSide(color: Colors.grey.shade200, width: 0.5),
        ),
        child: InkWell(
          onTap: () {
            context.push(RouteNames.shop);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product image
              Stack(
                children: [
                  AspectRatio(
                    aspectRatio: 1,
                    child: Container(
                      color: Colors.grey.shade50,
                      child: Center(
                        child: Icon(
                          Icons.image,
                          size: 32, // Larger icon
                          color: Colors.grey.shade300,
                        ),
                      ),
                    ),
                  ),
                  if (discountPercentage != null)
                    Positioned(
                      top: 6,
                      left: 6,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 3,
                        ),
                        decoration: BoxDecoration(
                          color: ColorConstants.errorColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '-${discountPercentage.toStringAsFixed(0)}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ),
                ],
              ),

              // Product details
              Padding(
                padding: const EdgeInsets.all(6),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Product name
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 11,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 2),

                    // Product price
                    Row(
                      children: [
                        if (product.discountPrice != null) ...[
                          Text(
                            '৳${product.discountPrice!.toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColorConstants.primaryColor,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 3),
                          Text(
                            '৳${product.price.toStringAsFixed(0)}',
                            style: TextStyle(
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey.shade500,
                              fontSize: 9,
                            ),
                          ),
                        ] else
                          Text(
                            '৳${product.price.toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ColorConstants.primaryColor,
                              fontSize: 10,
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 1),

                    // Rating
                    Row(
                      children: [
                        const Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 10,
                        ),
                        const SizedBox(width: 1),
                        Text(
                          '${product.rating}',
                          style: TextStyle(
                            fontSize: 9,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          ' (${product.reviewCount})',
                          style: TextStyle(
                            fontSize: 8,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Provider for HomeScreen
final homeScreenProvider = Provider<HomeScreen>((ref) {
  return const HomeScreen();
});

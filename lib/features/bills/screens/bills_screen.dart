import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Bill provider model
class BillProvider {
  final String id;
  final String name;
  final String logoPath;
  final Color color;
  final List<BillType> supportedBillTypes;

  const BillProvider({
    required this.id,
    required this.name,
    required this.logoPath,
    required this.color,
    required this.supportedBillTypes,
  });
}

/// Bill type enum
enum BillType {
  electricity,
  water,
  gas,
  internet,
  tv,
  telephone,
  education,
  government,
  other,
}

/// Bill payment screen
class BillsScreen extends ConsumerStatefulWidget {
  const BillsScreen({super.key});

  @override
  ConsumerState<BillsScreen> createState() => _BillsScreenState();
}

class _BillsScreenState extends ConsumerState<BillsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Selected bill type
  BillType _selectedBillType = BillType.electricity;

  // Mock bill providers
  final List<BillProvider> _billProviders = [
    BillProvider(
      id: 'desco',
      name: 'DESCO',
      logoPath: 'assets/images/bills/desco.png',
      color: Colors.blue,
      supportedBillTypes: [BillType.electricity],
    ),
    BillProvider(
      id: 'dpdc',
      name: 'DPDC',
      logoPath: 'assets/images/bills/dpdc.png',
      color: Colors.green,
      supportedBillTypes: [BillType.electricity],
    ),
    BillProvider(
      id: 'wasa',
      name: 'WASA',
      logoPath: 'assets/images/bills/wasa.png',
      color: Colors.lightBlue,
      supportedBillTypes: [BillType.water],
    ),
    BillProvider(
      id: 'titas',
      name: 'Titas Gas',
      logoPath: 'assets/images/bills/titas.png',
      color: Colors.orange,
      supportedBillTypes: [BillType.gas],
    ),
    BillProvider(
      id: 'btcl',
      name: 'BTCL',
      logoPath: 'assets/images/bills/btcl.png',
      color: Colors.red,
      supportedBillTypes: [BillType.telephone],
    ),
    BillProvider(
      id: 'grameenphone',
      name: 'Grameenphone',
      logoPath: 'assets/images/bills/gp.png',
      color: const Color(0xFF1976D2),
      supportedBillTypes: [BillType.internet, BillType.telephone],
    ),
    BillProvider(
      id: 'robi',
      name: 'Robi',
      logoPath: 'assets/images/bills/robi.png',
      color: const Color(0xFFE53935),
      supportedBillTypes: [BillType.internet, BillType.telephone],
    ),
    BillProvider(
      id: 'banglalink',
      name: 'Banglalink',
      logoPath: 'assets/images/bills/banglalink.png',
      color: const Color(0xFFFF9800),
      supportedBillTypes: [BillType.internet, BillType.telephone],
    ),
  ];

  // Recent bill payments
  final List<Map<String, dynamic>> _recentPayments = [
    {
      'provider': 'DESCO',
      'account': '*********',
      'amount': 1250.0,
      'date': DateTime.now().subtract(const Duration(days: 5)),
    },
    {
      'provider': 'BTCL',
      'account': '*********',
      'amount': 850.0,
      'date': DateTime.now().subtract(const Duration(days: 12)),
    },
    {
      'provider': 'Titas Gas',
      'account': '*********',
      'amount': 650.0,
      'date': DateTime.now().subtract(const Duration(days: 20)),
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Get filtered bill providers
  List<BillProvider> _getFilteredProviders() {
    return _billProviders.where((provider) {
      return provider.supportedBillTypes.contains(_selectedBillType);
    }).toList();
  }

  /// Get bill type icon
  IconData _getBillTypeIcon(BillType type) {
    switch (type) {
      case BillType.electricity:
        return Icons.electric_bolt;
      case BillType.water:
        return Icons.water_drop;
      case BillType.gas:
        return Icons.local_fire_department;
      case BillType.internet:
        return Icons.wifi;
      case BillType.tv:
        return Icons.tv;
      case BillType.telephone:
        return Icons.phone;
      case BillType.education:
        return Icons.school;
      case BillType.government:
        return Icons.account_balance;
      case BillType.other:
        return Icons.more_horiz;
    }
  }

  /// Get bill type name
  String _getBillTypeName(BillType type) {
    switch (type) {
      case BillType.electricity:
        return 'Electricity';
      case BillType.water:
        return 'Water';
      case BillType.gas:
        return 'Gas';
      case BillType.internet:
        return 'Internet';
      case BillType.tv:
        return 'TV';
      case BillType.telephone:
        return 'Telephone';
      case BillType.education:
        return 'Education';
      case BillType.government:
        return 'Government';
      case BillType.other:
        return 'Other';
    }
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('billpay')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              context.go(RouteNames.history);
              // Navigate to the Bills tab in the history screen
            },
            tooltip: 'Bill Payment History',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Pay Bills'),
            Tab(text: 'History'),
            Tab(text: 'Saved Bills'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Pay Bills tab
          _buildPayBillsTab(),

          // History tab
          _buildHistoryTab(),

          // Saved Bills tab
          _buildSavedBillsTab(),
        ],
      ),
    );
  }

  /// Build Pay Bills tab
  Widget _buildPayBillsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bill types
          const Text(
            'Select Bill Type',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Bill type grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 4,
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: BillType.values.map((type) {
              final isSelected = _selectedBillType == type;
              return InkWell(
                onTap: () {
                  setState(() {
                    _selectedBillType = type;
                  });
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? ColorConstants.primaryColor
                            : ColorConstants.primaryLightColor.withAlpha(26),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getBillTypeIcon(type),
                        color: isSelected
                            ? Colors.white
                            : ColorConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getBillTypeName(type),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 24),

          // Bill providers
          const Text(
            'Select Provider',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Provider grid
          _getFilteredProviders().isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('No providers available for this bill type'),
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    childAspectRatio: 1,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                  ),
                  itemCount: _getFilteredProviders().length,
                  itemBuilder: (context, index) {
                    final provider = _getFilteredProviders()[index];
                    return InkWell(
                      onTap: () {
                        _showBillPaymentForm(provider);
                      },
                      child: Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircleAvatar(
                              radius: 24,
                              backgroundColor: provider.color.withAlpha(30),
                              child: Icon(
                                Icons.business,
                                color: provider.color,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              provider.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),

          const SizedBox(height: 24),

          // Recent payments
          if (_recentPayments.isNotEmpty) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Recent Payments',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    _tabController.animateTo(1); // Switch to History tab
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Recent payments list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _recentPayments.length,
              itemBuilder: (context, index) {
                final payment = _recentPayments[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    leading: CircleAvatar(
                      child: Icon(_getBillTypeIcon(BillType.electricity)),
                    ),
                    title: Text(payment['provider']),
                    subtitle: Text('Account: ${payment['account']}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '৳${payment['amount'].toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _formatDate(payment['date']),
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      // TODO: Show payment details
                    },
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  /// Build History tab
  Widget _buildHistoryTab() {
    return _recentPayments.isEmpty
        ? const Center(
            child: Text('No payment history available'),
          )
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _recentPayments.length,
            itemBuilder: (context, index) {
              final payment = _recentPayments[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            payment['provider'],
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withAlpha(30),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Paid',
                              style: TextStyle(
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text('Account: ${payment['account']}'),
                      const SizedBox(height: 4),
                      Text('Date: ${_formatDate(payment['date'])}'),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Amount'),
                          Text(
                            '৳${payment['amount'].toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Convenience Fee'),
                          const Text(
                            '৳10.00',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Total',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '৳${(payment['amount'] + 10).toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              // TODO: Download receipt
                            },
                            icon: const Icon(Icons.download),
                            label: const Text('Download Receipt'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
  }

  /// Build Saved Bills tab
  Widget _buildSavedBillsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          const Text(
            'No Saved Bills',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Save your bills for quick access',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _tabController.animateTo(0); // Switch to Pay Bills tab
            },
            icon: const Icon(Icons.add),
            label: const Text('Add a Bill'),
          ),
        ],
      ),
    );
  }

  /// Show bill payment form
  void _showBillPaymentForm(BillProvider provider) {
    final accountController = TextEditingController();
    final amountController = TextEditingController();
    final formKey = GlobalKey<FormState>();
    bool isLoading = false;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Provider info
                    Row(
                      children: [
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: provider.color.withAlpha(30),
                          child: Icon(
                            Icons.business,
                            color: provider.color,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              provider.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              _getBillTypeName(_selectedBillType),
                              style: TextStyle(
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Form
                    Form(
                      key: formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextField(
                            controller: accountController,
                            labelText: 'Account Number',
                            hintText: 'Enter your account number',
                            keyboardType: TextInputType.number,
                            prefixIcon: const Icon(Icons.account_circle),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your account number';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          CustomTextField(
                            controller: amountController,
                            labelText: 'Amount',
                            hintText: 'Enter amount',
                            keyboardType: TextInputType.number,
                            prefixIcon: const Icon(Icons.attach_money),
                            validator: Validators.validateAmount,
                          ),
                          const SizedBox(height: 16),
                          Row(
                            children: [
                              Checkbox(
                                value: false,
                                onChanged: (value) {
                                  // TODO: Implement save bill functionality
                                },
                              ),
                              const Text('Save this bill for future payments'),
                            ],
                          ),
                          const SizedBox(height: 24),
                          CustomButton(
                            text: 'Pay Bill',
                            onPressed: () {
                              if (formKey.currentState!.validate()) {
                                setState(() {
                                  isLoading = true;
                                });

                                // Simulate API call
                                Future.delayed(const Duration(seconds: 2), () {
                                  Navigator.pop(context);

                                  // Show success dialog
                                  _showPaymentSuccessDialog(
                                    provider.name,
                                    accountController.text,
                                    double.parse(amountController.text),
                                  );
                                });
                              }
                            },
                            isLoading: isLoading,
                          ),
                          const SizedBox(height: 24),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show payment success dialog
  void _showPaymentSuccessDialog(String provider, String account, double amount) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
            SizedBox(width: 8),
            Text('Payment Successful'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your bill payment was successful.'),
            const SizedBox(height: 16),
            _buildPaymentDetailRow('Provider', provider),
            _buildPaymentDetailRow('Account', account),
            _buildPaymentDetailRow('Amount', '৳${amount.toStringAsFixed(2)}'),
            _buildPaymentDetailRow('Fee', '৳10.00'),
            _buildPaymentDetailRow('Total', '৳${(amount + 10).toStringAsFixed(2)}'),
            _buildPaymentDetailRow('Date', _formatDate(DateTime.now())),
            _buildPaymentDetailRow('Status', 'Paid'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // Add to recent payments
              setState(() {
                _recentPayments.insert(0, {
                  'provider': provider,
                  'account': account,
                  'amount': amount,
                  'date': DateTime.now(),
                });
              });
            },
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // Add to recent payments
              setState(() {
                _recentPayments.insert(0, {
                  'provider': provider,
                  'account': account,
                  'amount': amount,
                  'date': DateTime.now(),
                });
              });

              // Navigate to history tab
              _tabController.animateTo(1);
            },
            child: const Text('View Receipt'),
          ),
        ],
      ),
    );
  }

  /// Build payment detail row
  Widget _buildPaymentDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

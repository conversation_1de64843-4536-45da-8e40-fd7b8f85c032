import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';

/// Support screen
class SupportScreen extends ConsumerStatefulWidget {
  const SupportScreen({super.key});

  @override
  ConsumerState<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends ConsumerState<SupportScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();
  bool _isLoading = false;
  
  // FAQ categories
  final List<String> _categories = [
    'General',
    'Account',
    'Recharge',
    'Payments',
    'Wallet',
    'Security',
  ];
  
  // Selected category
  String _selectedCategory = 'General';
  
  // Mock FAQs
  final Map<String, List<Map<String, String>>> _faqs = {
    'General': [
      {
        'question': 'What is iRecharge Pro?',
        'answer': 'iRecharge Pro is a comprehensive digital services app for Bangladesh that allows you to recharge mobile phones, pay bills, transfer money, and shop online all in one place.',
      },
      {
        'question': 'How do I create an account?',
        'answer': 'To create an account, download the app from the App Store or Google Play, click on "Register", and follow the instructions to set up your account with your phone number, email, and personal details.',
      },
      {
        'question': 'Is iRecharge Pro available on all devices?',
        'answer': 'iRecharge Pro is available for both Android and iOS devices. You can download it from the Google Play Store or Apple App Store.',
      },
    ],
    'Account': [
      {
        'question': 'How do I reset my password?',
        'answer': 'To reset your password, go to the login screen and tap on "Forgot Password". Enter your registered phone number or email, and follow the instructions sent to you to create a new password.',
      },
      {
        'question': 'How do I update my profile information?',
        'answer': 'You can update your profile information by going to the Profile section, tapping on "Edit Profile", and making the necessary changes to your personal information.',
      },
      {
        'question': 'Can I have multiple accounts?',
        'answer': 'No, each user is allowed to have only one account associated with their phone number and email address for security reasons.',
      },
    ],
    'Recharge': [
      {
        'question': 'Which mobile operators are supported?',
        'answer': 'We support all major mobile operators in Bangladesh including Grameenphone, Robi, Airtel, Banglalink, and Teletalk.',
      },
      {
        'question': 'How do I recharge someone else\'s number?',
        'answer': 'You can recharge any mobile number by entering the number in the recharge section, selecting the operator, entering the amount, and confirming the payment.',
      },
      {
        'question': 'Are there any fees for mobile recharge?',
        'answer': 'No, there are no additional fees for mobile recharges. You only pay the recharge amount.',
      },
    ],
    'Payments': [
      {
        'question': 'What types of bills can I pay?',
        'answer': 'You can pay various bills including electricity, water, gas, internet, TV, and more through the app.',
      },
      {
        'question': 'How do I know if my payment was successful?',
        'answer': 'After a successful payment, you will receive a confirmation message and the transaction will appear in your transaction history.',
      },
      {
        'question': 'What payment methods are accepted?',
        'answer': 'We accept various payment methods including credit/debit cards, mobile banking (bKash, Nagad, Rocket), and net banking.',
      },
    ],
    'Wallet': [
      {
        'question': 'How do I add money to my wallet?',
        'answer': 'You can add money to your wallet by going to the Wallet section, tapping on "Add Money", selecting your preferred payment method, and following the instructions.',
      },
      {
        'question': 'Is there a limit to how much I can add to my wallet?',
        'answer': 'Yes, there are daily and monthly limits for adding money to your wallet. The standard limits are ৳10,000 per day and ৳100,000 per month.',
      },
      {
        'question': 'How do I transfer money to someone else?',
        'answer': 'To transfer money, go to the Wallet section, tap on "Send Money", enter the recipient\'s phone number or select from your contacts, enter the amount, and confirm the transfer.',
      },
    ],
    'Security': [
      {
        'question': 'Is my personal information secure?',
        'answer': 'Yes, we use industry-standard encryption and security measures to protect your personal information and transaction data.',
      },
      {
        'question': 'What should I do if I suspect unauthorized activity?',
        'answer': 'If you suspect unauthorized activity, immediately change your password, enable two-factor authentication if not already enabled, and contact our customer support team.',
      },
      {
        'question': 'How can I enable two-factor authentication?',
        'answer': 'You can enable two-factor authentication by going to the Profile section, tapping on "Security", and following the instructions to set up 2FA using your phone number or an authenticator app.',
      },
    ],
  };
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }
  
  /// Submit support ticket
  void _submitTicket() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;
      
      setState(() {
        _isLoading = false;
      });
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Support ticket submitted successfully'),
          backgroundColor: Colors.green,
        ),
      );
      
      // Clear form
      _subjectController.clear();
      _messageController.clear();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('support')),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'FAQs'),
            Tab(text: 'Contact Us'),
            Tab(text: 'Live Chat'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // FAQs tab
          _buildFaqsTab(),
          
          // Contact Us tab
          _buildContactUsTab(),
          
          // Live Chat tab
          _buildLiveChatTab(),
        ],
      ),
    );
  }
  
  /// Build FAQs tab
  Widget _buildFaqsTab() {
    return Column(
      children: [
        // Category selector
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ChoiceChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedCategory = category;
                        });
                      }
                    },
                    backgroundColor: Colors.grey.shade200,
                    selectedColor: ColorConstants.primaryColor.withAlpha(50),
                    labelStyle: TextStyle(
                      color: isSelected ? ColorConstants.primaryColor : Colors.black,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        
        // FAQs list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _faqs[_selectedCategory]?.length ?? 0,
            itemBuilder: (context, index) {
              final faq = _faqs[_selectedCategory]![index];
              return _buildFaqItem(faq['question']!, faq['answer']!);
            },
          ),
        ),
      ],
    );
  }
  
  /// Build FAQ item
  Widget _buildFaqItem(String question, String answer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(answer),
          ),
        ],
      ),
    );
  }
  
  /// Build Contact Us tab
  Widget _buildContactUsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Contact information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Contact Information',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildContactItem(
                    icon: Icons.phone,
                    title: 'Phone',
                    subtitle: '+880 1712345678',
                    onTap: () {
                      // TODO: Make phone call
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Calling support...')),
                      );
                    },
                  ),
                  const Divider(),
                  _buildContactItem(
                    icon: Icons.email,
                    title: 'Email',
                    subtitle: '<EMAIL>',
                    onTap: () {
                      // TODO: Send email
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Opening email client...')),
                      );
                    },
                  ),
                  const Divider(),
                  _buildContactItem(
                    icon: Icons.location_on,
                    title: 'Address',
                    subtitle: '123 Main Street, Dhaka, Bangladesh',
                    onTap: () {
                      // TODO: Open map
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Opening maps...')),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Support ticket form
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Submit a Support Ticket',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _subjectController,
                      labelText: 'Subject',
                      hintText: 'Enter the subject of your inquiry',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a subject';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomTextField(
                      controller: _messageController,
                      labelText: 'Message',
                      hintText: 'Enter your message',
                      maxLines: 5,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a message';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    CustomButton(
                      text: 'Submit Ticket',
                      onPressed: _submitTicket,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Social media
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Follow Us',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildSocialButton(
                        icon: Icons.facebook,
                        label: 'Facebook',
                        color: const Color(0xFF1877F2),
                        onTap: () {
                          // TODO: Open Facebook
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Opening Facebook...')),
                          );
                        },
                      ),
                      _buildSocialButton(
                        icon: Icons.camera_alt,
                        label: 'Instagram',
                        color: const Color(0xFFE1306C),
                        onTap: () {
                          // TODO: Open Instagram
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Opening Instagram...')),
                          );
                        },
                      ),
                      _buildSocialButton(
                        icon: Icons.message,
                        label: 'Twitter',
                        color: const Color(0xFF1DA1F2),
                        onTap: () {
                          // TODO: Open Twitter
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Opening Twitter...')),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build Live Chat tab
  Widget _buildLiveChatTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          const Text(
            'Live Chat Support',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Chat with our support team in real-time',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Starting live chat...')),
              );
            },
            icon: const Icon(Icons.chat),
            label: const Text('Start Chat'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Available: 9:00 AM - 9:00 PM',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build contact item
  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: ColorConstants.primaryColor.withAlpha(30),
              child: Icon(
                icon,
                color: ColorConstants.primaryColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
      ),
    );
  }
  
  /// Build social media button
  Widget _buildSocialButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          CircleAvatar(
            backgroundColor: color,
            child: Icon(
              icon,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Contact model
class Contact {
  final String id;
  final String name;
  final String phoneNumber;
  final String? avatarUrl;
  final bool isFavorite;

  Contact({
    required this.id,
    required this.name,
    required this.phoneNumber,
    this.avatarUrl,
    this.isFavorite = false,
  });
}

/// Send money screen
class SendMoneyScreen extends ConsumerStatefulWidget {
  const SendMoneyScreen({super.key});

  @override
  ConsumerState<SendMoneyScreen> createState() => _SendMoneyScreenState();
}

class _SendMoneyScreenState extends ConsumerState<SendMoneyScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _noteController = TextEditingController();
  bool _isLoading = false;

  // Selected contact
  Contact? _selectedContact;

  // Search query
  String _searchQuery = '';

  // Mock contacts
  final List<Contact> _contacts = [
    Contact(
      id: '1',
      name: 'John Doe',
      phoneNumber: '01712345678',
      isFavorite: true,
    ),
    Contact(
      id: '2',
      name: 'Jane Smith',
      phoneNumber: '01812345678',
      isFavorite: true,
    ),
    Contact(
      id: '3',
      name: 'Alice Johnson',
      phoneNumber: '01912345678',
    ),
    Contact(
      id: '4',
      name: 'Bob Williams',
      phoneNumber: '01612345678',
    ),
    Contact(
      id: '5',
      name: 'Charlie Brown',
      phoneNumber: '01512345678',
      isFavorite: true,
    ),
    Contact(
      id: '6',
      name: 'David Miller',
      phoneNumber: '01312345678',
    ),
    Contact(
      id: '7',
      name: 'Eva Wilson',
      phoneNumber: '01412345678',
    ),
  ];

  @override
  void dispose() {
    _phoneController.dispose();
    _amountController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  /// Get filtered contacts
  List<Contact> get _filteredContacts {
    if (_searchQuery.isEmpty) {
      return _contacts;
    }

    return _contacts.where((contact) {
      return contact.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             contact.phoneNumber.contains(_searchQuery);
    }).toList();
  }

  /// Get favorite contacts
  List<Contact> get _favoriteContacts {
    return _contacts.where((contact) => contact.isFavorite).toList();
  }

  /// Handle contact selection
  void _selectContact(Contact contact) {
    setState(() {
      _selectedContact = contact;
      _phoneController.text = contact.phoneNumber;
    });
  }

  /// Handle send money
  void _handleSendMoney() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final phoneNumber = _phoneController.text;
    final amount = double.tryParse(_amountController.text) ?? 0;
    final note = _noteController.text;

    if (amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid amount'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show confirmation dialog
      _showConfirmationDialog(phoneNumber, amount, note);
    });
  }

  /// Show confirmation dialog
  void _showConfirmationDialog(String phoneNumber, double amount, String note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Transfer'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to send money to:'),
            const SizedBox(height: 16),
            _buildConfirmationRow('Recipient', _selectedContact?.name ?? phoneNumber),
            _buildConfirmationRow('Phone', phoneNumber),
            _buildConfirmationRow('Amount', '৳${amount.toStringAsFixed(2)}'),
            if (note.isNotEmpty) _buildConfirmationRow('Note', note),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // Show success message
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Successfully sent ৳${amount.toStringAsFixed(2)} to $phoneNumber'),
                  backgroundColor: Colors.green,
                ),
              );

              // Navigate back
              context.pop();
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  /// Build confirmation row
  Widget _buildConfirmationRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('send_money')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Current balance card
              Card(
                color: ColorConstants.primaryColor,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Available Balance',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '৳ 5,000.00',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Favorite contacts
              if (_favoriteContacts.isNotEmpty) ...[
                const Text(
                  'Favorites',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 90,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _favoriteContacts.length,
                    itemBuilder: (context, index) {
                      final contact = _favoriteContacts[index];
                      final isSelected = _selectedContact?.id == contact.id;

                      return GestureDetector(
                        onTap: () => _selectContact(contact),
                        child: Container(
                          width: 70,
                          margin: const EdgeInsets.only(right: 16),
                          child: Column(
                            children: [
                              CircleAvatar(
                                radius: 30,
                                backgroundColor: isSelected
                                    ? ColorConstants.primaryColor
                                    : Colors.grey.shade200,
                                child: Text(
                                  contact.name.substring(0, 1),
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: isSelected
                                        ? Colors.white
                                        : ColorConstants.primaryColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                contact.name.split(' ')[0],
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 24),
              ],

              // Phone number input
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _phoneController,
                      labelText: 'Phone Number',
                      hintText: 'Enter phone number',
                      keyboardType: TextInputType.phone,
                      prefixIcon: const Icon(Icons.phone_android),
                      validator: Validators.validateBangladeshPhone,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    height: 56,
                    width: 56,
                    margin: const EdgeInsets.only(top: 4),
                    child: ElevatedButton(
                      onPressed: () {
                        _showContactsBottomSheet();
                      },
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Icon(Icons.contacts),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Amount input
              CustomTextField(
                controller: _amountController,
                labelText: 'Amount',
                hintText: 'Enter amount',
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(Icons.attach_money),
                validator: Validators.validateAmount,
              ),

              const SizedBox(height: 16),

              // Note input
              CustomTextField(
                controller: _noteController,
                labelText: 'Note (Optional)',
                hintText: 'Add a note',
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // Send money button
              CustomButton(
                text: 'Send Money',
                onPressed: _handleSendMoney,
                isLoading: _isLoading,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show contacts bottom sheet
  void _showContactsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return DraggableScrollableSheet(
              initialChildSize: 0.7,
              minChildSize: 0.5,
              maxChildSize: 0.9,
              expand: false,
              builder: (context, scrollController) {
                return Column(
                  children: [
                    // Header
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // Drag handle
                          Center(
                            child: Container(
                              width: 40,
                              height: 4,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Title
                          const Text(
                            'Select Contact',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          // Search
                          TextField(
                            decoration: InputDecoration(
                              hintText: 'Search contacts',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                            onChanged: (value) {
                              setState(() {
                                _searchQuery = value;
                              });
                            },
                          ),
                        ],
                      ),
                    ),

                    // Contacts list
                    Expanded(
                      child: _filteredContacts.isEmpty
                          ? const Center(
                              child: Text('No contacts found'),
                            )
                          : ListView.builder(
                              controller: scrollController,
                              itemCount: _filteredContacts.length,
                              itemBuilder: (context, index) {
                                final contact = _filteredContacts[index];
                                return ListTile(
                                  leading: CircleAvatar(
                                    child: Text(contact.name.substring(0, 1)),
                                  ),
                                  title: Text(contact.name),
                                  subtitle: Text(contact.phoneNumber),
                                  trailing: contact.isFavorite
                                      ? const Icon(
                                          Icons.star,
                                          color: Colors.amber,
                                        )
                                      : null,
                                  onTap: () {
                                    Navigator.pop(context);
                                    _selectContact(contact);
                                  },
                                );
                              },
                            ),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
}

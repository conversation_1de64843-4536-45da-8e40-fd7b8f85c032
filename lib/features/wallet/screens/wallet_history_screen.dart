import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Transaction type enum
enum TransactionType {
  recharge,
  addMoney,
  sendMoney,
  receiveMoney,
  payment,
  cashback,
}

/// Transaction status enum
enum TransactionStatus {
  success,
  pending,
  failed,
}

/// Transaction model
class Transaction {
  final String id;
  final TransactionType type;
  final double amount;
  final DateTime date;
  final String description;
  final TransactionStatus status;
  final String? recipient;
  final String? reference;

  Transaction({
    required this.id,
    required this.type,
    required this.amount,
    required this.date,
    required this.description,
    required this.status,
    this.recipient,
    this.reference,
  });
}

/// Wallet history screen
class WalletHistoryScreen extends ConsumerStatefulWidget {
  const WalletHistoryScreen({super.key});

  @override
  ConsumerState<WalletHistoryScreen> createState() => _WalletHistoryScreenState();
}

class _WalletHistoryScreenState extends ConsumerState<WalletHistoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  bool _isFilterExpanded = false;

  // Filter options
  final List<String> _filterOptions = ['All', 'Recharge', 'Add Money', 'Send Money', 'Receive Money', 'Payment', 'Cashback'];
  String _selectedFilter = 'All';

  // Amount range
  RangeValues _amountRange = const RangeValues(0, 5000);
  final double _maxAmount = 5000;

  // Date range
  DateTimeRange? _selectedDateRange;

  // Search query
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Sort options
  final List<String> _sortOptions = ['Newest First', 'Oldest First', 'Amount (High to Low)', 'Amount (Low to High)'];
  String _selectedSortOption = 'Newest First';

  // Mock transactions
  final List<Transaction> _transactions = [
    Transaction(
      id: 'TX123456',
      type: TransactionType.recharge,
      amount: 100.0,
      date: DateTime.now().subtract(const Duration(hours: 2)),
      description: 'Mobile Recharge - 01712345678',
      status: TransactionStatus.success,
      recipient: '01712345678',
      reference: 'REF123456',
    ),
    Transaction(
      id: 'TX123457',
      type: TransactionType.addMoney,
      amount: 1000.0,
      date: DateTime.now().subtract(const Duration(days: 1)),
      description: 'Add Money via Credit Card',
      status: TransactionStatus.success,
      reference: 'REF123457',
    ),
    Transaction(
      id: 'TX123458',
      type: TransactionType.sendMoney,
      amount: 500.0,
      date: DateTime.now().subtract(const Duration(days: 2)),
      description: 'Send Money to John Doe',
      status: TransactionStatus.success,
      recipient: 'John Doe',
      reference: 'REF123458',
    ),
    Transaction(
      id: 'TX123459',
      type: TransactionType.receiveMoney,
      amount: 200.0,
      date: DateTime.now().subtract(const Duration(days: 3)),
      description: 'Received Money from Jane Smith',
      status: TransactionStatus.success,
      recipient: 'Jane Smith',
      reference: 'REF123459',
    ),
    Transaction(
      id: 'TX123460',
      type: TransactionType.payment,
      amount: 350.0,
      date: DateTime.now().subtract(const Duration(days: 4)),
      description: 'Electricity Bill Payment',
      status: TransactionStatus.success,
      reference: 'REF123460',
    ),
    Transaction(
      id: 'TX123461',
      type: TransactionType.cashback,
      amount: 25.0,
      date: DateTime.now().subtract(const Duration(days: 4)),
      description: 'Cashback from Recharge',
      status: TransactionStatus.success,
      reference: 'REF123461',
    ),
    Transaction(
      id: 'TX123462',
      type: TransactionType.recharge,
      amount: 50.0,
      date: DateTime.now().subtract(const Duration(days: 5)),
      description: 'Mobile Recharge - 01812345678',
      status: TransactionStatus.failed,
      recipient: '01812345678',
      reference: 'REF123462',
    ),
    Transaction(
      id: 'TX123463',
      type: TransactionType.sendMoney,
      amount: 300.0,
      date: DateTime.now().subtract(const Duration(days: 6)),
      description: 'Send Money to Bob Williams',
      status: TransactionStatus.pending,
      recipient: 'Bob Williams',
      reference: 'REF123463',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Add listener to search controller
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// Get filtered transactions
  List<Transaction> _getFilteredTransactions() {
    List<Transaction> filteredList = [..._transactions];

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredList = filteredList.where((tx) {
        return tx.description.toLowerCase().contains(_searchQuery) ||
               tx.id.toLowerCase().contains(_searchQuery) ||
               (tx.recipient?.toLowerCase().contains(_searchQuery) ?? false) ||
               (tx.reference?.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }

    // Apply type filter
    if (_selectedFilter != 'All') {
      final typeMap = {
        'Recharge': TransactionType.recharge,
        'Add Money': TransactionType.addMoney,
        'Send Money': TransactionType.sendMoney,
        'Receive Money': TransactionType.receiveMoney,
        'Payment': TransactionType.payment,
        'Cashback': TransactionType.cashback,
      };

      filteredList = filteredList.where((tx) => tx.type == typeMap[_selectedFilter]).toList();
    }

    // Apply amount range filter
    filteredList = filteredList.where((tx) {
      return tx.amount >= _amountRange.start && tx.amount <= _amountRange.end;
    }).toList();

    // Apply date range filter
    if (_selectedDateRange != null) {
      filteredList = filteredList.where((tx) {
        return tx.date.isAfter(_selectedDateRange!.start) &&
               tx.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Apply status filter based on tab
    switch (_tabController.index) {
      case 0: // All
        break;
      case 1: // Completed
        filteredList = filteredList.where((tx) => tx.status == TransactionStatus.success).toList();
        break;
      case 2: // Pending/Failed
        filteredList = filteredList.where((tx) =>
          tx.status == TransactionStatus.pending || tx.status == TransactionStatus.failed
        ).toList();
        break;
    }

    // Apply sorting
    switch (_selectedSortOption) {
      case 'Newest First':
        filteredList.sort((a, b) => b.date.compareTo(a.date));
        break;
      case 'Oldest First':
        filteredList.sort((a, b) => a.date.compareTo(b.date));
        break;
      case 'Amount (High to Low)':
        filteredList.sort((a, b) => b.amount.compareTo(a.amount));
        break;
      case 'Amount (Low to High)':
        filteredList.sort((a, b) => a.amount.compareTo(b.amount));
        break;
    }

    return filteredList;
  }

  /// Show date range picker
  Future<void> _selectDateRange() async {
    final initialDateRange = _selectedDateRange ?? DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 7)),
      end: DateTime.now(),
    );

    final newDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: ColorConstants.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      setState(() {
        _selectedDateRange = newDateRange;
      });
    }
  }

  /// Clear all filters
  void _clearFilters() {
    setState(() {
      _selectedFilter = 'All';
      _selectedDateRange = null;
      _amountRange = const RangeValues(0, 5000);
      _searchController.clear();
      _searchQuery = '';
      _selectedSortOption = 'Newest First';
    });
  }

  /// Toggle filter panel
  void _toggleFilterPanel() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
    });
  }

  /// Calculate total amount for transactions
  double _calculateTotal(List<Transaction> transactions) {
    double total = 0;
    for (var tx in transactions) {
      if (tx.type == TransactionType.addMoney ||
          tx.type == TransactionType.receiveMoney ||
          tx.type == TransactionType.cashback) {
        total += tx.amount;
      } else {
        total -= tx.amount;
      }
    }
    return total;
  }

  /// Check if two dates are the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Build date header for transaction list
  Widget _buildDateHeader(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime.now().subtract(const Duration(days: 1));

    String headerText;
    if (_isSameDay(date, now)) {
      headerText = ref.watch(languageProvider) == AppLanguage.english
          ? 'Today'
          : 'আজ';
    } else if (_isSameDay(date, yesterday)) {
      headerText = ref.watch(languageProvider) == AppLanguage.english
          ? 'Yesterday'
          : 'গতকাল';
    } else {
      headerText = _formatDate(date);
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        headerText,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
          color: Colors.grey,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredTransactions = _getFilteredTransactions();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Transaction History'
              : 'লেনদেনের ইতিহাস',
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        bottom: TabBar(
          controller: _tabController,
          onTap: (_) => setState(() {}), // Refresh when tab changes
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Completed'),
            Tab(text: 'Pending/Failed'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Filters
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      'Filters:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: DropdownButton<String>(
                        value: _selectedFilter,
                        isExpanded: true,
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedFilter = value;
                            });
                          }
                        },
                        items: _filterOptions.map((option) {
                          return DropdownMenuItem<String>(
                            value: option,
                            child: Text(option),
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text(
                      'Date Range:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _selectDateRange,
                        child: Text(
                          _selectedDateRange != null
                              ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                              : 'Select Date Range',
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: _clearFilters,
                      tooltip: 'Clear Filters',
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Transaction summary
          if (filteredTransactions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? '${filteredTransactions.length} transactions found'
                        : '${filteredTransactions.length}টি লেনদেন পাওয়া গেছে',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Total: ৳${_calculateTotal(filteredTransactions).toStringAsFixed(2)}'
                        : 'মোট: ৳${_calculateTotal(filteredTransactions).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

          // Transaction list
          Expanded(
            child: _isLoading
                ? const Center(child: LoadingIndicator())
                : filteredTransactions.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 64,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              ref.watch(languageProvider) == AppLanguage.english
                                  ? 'No transactions found'
                                  : 'কোন লেনদেন পাওয়া যায়নি',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextButton.icon(
                              onPressed: _clearFilters,
                              icon: const Icon(Icons.filter_alt_off),
                              label: Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'Clear Filters'
                                    : 'ফিল্টার মুছুন',
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredTransactions.length,
                        itemBuilder: (context, index) {
                          final transaction = filteredTransactions[index];

                          // Add date header if this is the first transaction or if the date is different from the previous one
                          final bool showDateHeader = index == 0 ||
                              !_isSameDay(filteredTransactions[index].date, filteredTransactions[index - 1].date);

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (showDateHeader)
                                _buildDateHeader(transaction.date),
                              _buildTransactionItem(transaction),
                            ],
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  /// Build transaction item
  Widget _buildTransactionItem(Transaction transaction) {
    final isCredit = transaction.type == TransactionType.addMoney ||
                     transaction.type == TransactionType.receiveMoney ||
                     transaction.type == TransactionType.cashback;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionColor(transaction.type).withAlpha(30),
          child: Icon(
            _getTransactionIcon(transaction.type),
            color: _getTransactionColor(transaction.type),
          ),
        ),
        title: Text(transaction.description),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_formatDateTime(transaction.date)),
            if (transaction.reference != null)
              Text(
                'Ref: ${transaction.reference}',
                style: const TextStyle(fontSize: 12),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${isCredit ? '+' : '-'}৳${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isCredit ? Colors.green : Colors.red,
              ),
            ),
            _buildStatusChip(transaction.status),
          ],
        ),
        onTap: () => _showTransactionDetails(transaction),
      ),
    );
  }

  /// Build status chip
  Widget _buildStatusChip(TransactionStatus status) {
    Color color;
    String label;

    switch (status) {
      case TransactionStatus.success:
        color = Colors.green;
        label = 'Success';
        break;
      case TransactionStatus.pending:
        color = Colors.orange;
        label = 'Pending';
        break;
      case TransactionStatus.failed:
        color = Colors.red;
        label = 'Failed';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Get transaction icon
  IconData _getTransactionIcon(TransactionType type) {
    switch (type) {
      case TransactionType.recharge:
        return Icons.phone_android;
      case TransactionType.addMoney:
        return Icons.account_balance_wallet;
      case TransactionType.sendMoney:
        return Icons.send;
      case TransactionType.receiveMoney:
        return Icons.call_received;
      case TransactionType.payment:
        return Icons.payment;
      case TransactionType.cashback:
        return Icons.redeem;
    }
  }

  /// Get transaction color
  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.recharge:
        return Colors.blue;
      case TransactionType.addMoney:
        return Colors.green;
      case TransactionType.sendMoney:
        return Colors.red;
      case TransactionType.receiveMoney:
        return Colors.green;
      case TransactionType.payment:
        return Colors.purple;
      case TransactionType.cashback:
        return Colors.amber;
    }
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Format date and time
  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Show transaction details
  void _showTransactionDetails(Transaction transaction) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Center(
                child: Text(
                  'Transaction Details',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              const SizedBox(height: 16),
              const Divider(),
              _buildDetailRow('Transaction ID', transaction.id),
              _buildDetailRow('Type', transaction.type.toString().split('.').last),
              _buildDetailRow('Amount', '৳${transaction.amount.toStringAsFixed(2)}'),
              _buildDetailRow('Date & Time', _formatDateTime(transaction.date)),
              _buildDetailRow('Description', transaction.description),
              _buildDetailRow('Status', transaction.status.toString().split('.').last),
              if (transaction.recipient != null)
                _buildDetailRow('Recipient', transaction.recipient!),
              if (transaction.reference != null)
                _buildDetailRow('Reference', transaction.reference!),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Receipt downloaded')),
                    );
                  },
                  child: const Text('Download Receipt'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

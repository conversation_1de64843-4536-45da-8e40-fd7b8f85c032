import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/utils/validators.dart';
import 'package:irecharge_pro/core/widgets/custom_button.dart';
import 'package:irecharge_pro/core/widgets/custom_text_field.dart';
import 'package:irecharge_pro/data/models/bank_model.dart';
import 'package:irecharge_pro/data/providers/bank_transfer_provider.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Bank Transfer Screen
class BankTransferScreen extends ConsumerStatefulWidget {
  const BankTransferScreen({super.key});

  @override
  ConsumerState<BankTransferScreen> createState() => _BankTransferScreenState();
}

class _BankTransferScreenState extends ConsumerState<BankTransferScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountNumberController = TextEditingController();
  final _accountNameController = TextEditingController();
  final _branchNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _referenceController = TextEditingController();
  final _noteController = TextEditingController();

  BankAccountType _selectedAccountType = BankAccountType.savings;
  bool _isLoading = false;
  bool _isBankSelectionVisible = false;
  bool _showRecentTransfers = false;

  @override
  void dispose() {
    _accountNumberController.dispose();
    _accountNameController.dispose();
    _branchNameController.dispose();
    _amountController.dispose();
    _referenceController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  /// Toggle bank selection visibility
  void _toggleBankSelection() {
    setState(() {
      _isBankSelectionVisible = !_isBankSelectionVisible;
    });
  }

  /// Select bank
  void _selectBank(Bank bank) {
    ref.read(selectedBankProvider.notifier).state = bank;
    setState(() {
      _isBankSelectionVisible = false;
    });
  }

  /// Handle bank transfer
  void _handleBankTransfer() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final selectedBank = ref.read(selectedBankProvider);
    if (selectedBank == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a bank'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    final accountNumber = _accountNumberController.text;
    final accountName = _accountNameController.text;
    final branchName = _branchNameController.text;
    final amount = double.tryParse(_amountController.text) ?? 0;
    final reference = _referenceController.text;
    final note = _noteController.text;

    if (amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid amount'),
          backgroundColor: ColorConstants.errorColor,
        ),
      );
      return;
    }

    // Show loading state
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Show confirmation dialog
      _showConfirmationDialog(
        selectedBank,
        accountNumber,
        accountName,
        branchName,
        _selectedAccountType,
        amount,
        reference,
        note,
      );
    });
  }

  /// Show confirmation dialog
  void _showConfirmationDialog(
    Bank bank,
    String accountNumber,
    String accountName,
    String branchName,
    BankAccountType accountType,
    double amount,
    String reference,
    String note,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Transfer'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to transfer money to:'),
            const SizedBox(height: 16),
            _buildConfirmationRow('Bank', bank.name),
            _buildConfirmationRow('Account Number', accountNumber),
            _buildConfirmationRow('Account Name', accountName),
            if (branchName.isNotEmpty) _buildConfirmationRow('Branch', branchName),
            _buildConfirmationRow('Account Type', accountType.name),
            _buildConfirmationRow('Amount', '৳${amount.toStringAsFixed(2)}'),
            if (reference.isNotEmpty) _buildConfirmationRow('Reference', reference),
            if (note.isNotEmpty) _buildConfirmationRow('Note', note),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // Create new bank transfer
              final transfer = BankTransfer(
                id: ref.read(bankTransfersProvider.notifier).generateId(),
                bankId: bank.id,
                accountNumber: accountNumber,
                accountName: accountName,
                branchName: branchName.isNotEmpty ? branchName : null,
                accountType: accountType,
                amount: amount,
                date: DateTime.now(),
                reference: reference.isNotEmpty ? reference : null,
                note: note.isNotEmpty ? note : null,
                status: BankTransferStatus.pending,
                transactionId: 'TX${DateTime.now().millisecondsSinceEpoch}',
              );

              // Add to provider
              ref.read(bankTransfersProvider.notifier).addBankTransfer(transfer);

              // Show PIN verification dialog
              _showPinVerificationDialog(transfer);
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  /// Show PIN verification dialog
  void _showPinVerificationDialog(BankTransfer transfer) {
    final pinController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Enter PIN'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please enter your 4-digit PIN to confirm the transfer'),
            const SizedBox(height: 16),
            TextField(
              controller: pinController,
              keyboardType: TextInputType.number,
              maxLength: 4,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'PIN',
                border: OutlineInputBorder(),
                counterText: '',
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              pinController.dispose();

              // Cancel the transfer
              ref.read(bankTransfersProvider.notifier).updateStatus(
                transfer.id,
                BankTransferStatus.cancelled,
              );
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (pinController.text.length != 4) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a 4-digit PIN'),
                    backgroundColor: ColorConstants.errorColor,
                  ),
                );
                return;
              }

              Navigator.pop(context);
              pinController.dispose();

              // Process the transfer
              _processTransfer(transfer);
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  /// Process the transfer
  void _processTransfer(BankTransfer transfer) {
    setState(() {
      _isLoading = true;
    });

    // Update status to processing
    ref.read(bankTransfersProvider.notifier).updateStatus(
      transfer.id,
      BankTransferStatus.processing,
    );

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // Update status to completed
      ref.read(bankTransfersProvider.notifier).updateStatus(
        transfer.id,
        BankTransferStatus.completed,
      );

      // Show success dialog
      _showSuccessDialog(transfer);
    });
  }

  /// Show success dialog
  void _showSuccessDialog(BankTransfer transfer) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Transfer Successful'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'You have successfully transferred ${transfer.formattedAmount} to ${transfer.accountName}',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            _buildConfirmationRow('Transaction ID', transfer.transactionId ?? ''),
            _buildConfirmationRow('Date & Time', '${transfer.formattedDate} ${transfer.formattedTime}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // Navigate to history screen
              context.go(RouteNames.history);
            },
            child: const Text('View History'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // Clear form
              _accountNumberController.clear();
              _accountNameController.clear();
              _amountController.clear();
              _referenceController.clear();
              _noteController.clear();
              ref.read(selectedBankProvider.notifier).state = null;
              setState(() {
                _selectedAccountType = BankAccountType.savings;
              });
            },
            child: const Text('New Transfer'),
          ),
        ],
      ),
    );
  }

  /// Build confirmation row
  Widget _buildConfirmationRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// Build recent transfers
  Widget _buildRecentTransfers() {
    final recentTransfers = ref.watch(bankTransfersProvider).take(3).toList();

    if (recentTransfers.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'No recent transfers',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Column(
      children: recentTransfers.map((transfer) {
        final bank = transfer.bank;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 1,
          child: InkWell(
            onTap: () {
              // Fill form with transfer details
              ref.read(selectedBankProvider.notifier).state = bank;
              _accountNumberController.text = transfer.accountNumber;
              _accountNameController.text = transfer.accountName;
              if (transfer.branchName != null) {
                _branchNameController.text = transfer.branchName!;
              }
              setState(() {
                _selectedAccountType = transfer.accountType;
              });
            },
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  // Bank logo
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: bank?.color.withAlpha(25) ?? Colors.grey.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        Icons.account_balance,
                        color: bank?.color ?? Colors.grey,
                        size: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Transfer details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          transfer.accountName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          '${bank?.name ?? 'Unknown Bank'} • ${transfer.accountNumber}',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Amount
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        transfer.formattedAmount,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        transfer.formattedDate,
                        style: const TextStyle(
                          color: Colors.grey,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  // Reuse icon
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.replay_circle_filled,
                    color: ColorConstants.primaryColor,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final selectedBank = ref.watch(selectedBankProvider);
    final banks = ref.watch(banksProvider);
    final popularBanks = ref.watch(popularBanksProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.translate('banking.bankTransfer')),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.go(RouteNames.home);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              context.go(RouteNames.history);
            },
            tooltip: 'Transfer History',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Main form
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Current balance card
                  Card(
                    color: ColorConstants.primaryColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text(
                                'Available Balance',
                                style: TextStyle(
                                  color: Colors.white70,
                                  fontSize: 16,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withAlpha(50),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Row(
                                  children: [
                                    Icon(
                                      Icons.refresh,
                                      color: Colors.white,
                                      size: 16,
                                    ),
                                    SizedBox(width: 4),
                                    Text(
                                      'Refresh',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                '৳ 5,000.00',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 32,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(width: 8),
                              Padding(
                                padding: EdgeInsets.only(bottom: 4),
                                child: Text(
                                  'BDT',
                                  style: TextStyle(
                                    color: Colors.white70,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Bank selection
                  const Text(
                    'Select Bank',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _toggleBankSelection,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          if (selectedBank != null) ...[
                            Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: selectedBank.color.withAlpha(25),
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.account_balance,
                                  color: selectedBank.color,
                                  size: 24,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                selectedBank.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ] else
                            const Expanded(
                              child: Text(
                                'Select a bank',
                                style: TextStyle(
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          const Icon(Icons.arrow_drop_down),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Account number
                  CustomTextField(
                    controller: _accountNumberController,
                    labelText: 'Account Number',
                    hintText: 'Enter account number',
                    keyboardType: TextInputType.number,
                    validator: (value) => Validators.validateRequired(value, 'Account number'),
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Account name
                  CustomTextField(
                    controller: _accountNameController,
                    labelText: 'Account Name',
                    hintText: 'Enter account name',
                    validator: (value) => Validators.validateRequired(value, 'Account name'),
                  ),

                  const SizedBox(height: 16),

                  // Branch name
                  CustomTextField(
                    controller: _branchNameController,
                    labelText: 'Branch Name (Optional)',
                    hintText: 'Enter branch name',
                    prefixIcon: const Icon(Icons.location_on_outlined),
                  ),

                  const SizedBox(height: 16),

                  // Account type
                  const Text(
                    'Account Type',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: BankAccountType.values.map((type) {
                      final isSelected = _selectedAccountType == type;
                      return ChoiceChip(
                        label: Text(type.name),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedAccountType = type;
                            });
                          }
                        },
                        backgroundColor: Colors.grey.shade200,
                        selectedColor: ColorConstants.primaryColor.withAlpha(50),
                        labelStyle: TextStyle(
                          color: isSelected ? ColorConstants.primaryColor : Colors.black,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 16),

                  // Amount
                  CustomTextField(
                    controller: _amountController,
                    labelText: 'Amount',
                    hintText: 'Enter amount',
                    keyboardType: TextInputType.number,
                    validator: Validators.validateAmount,
                    prefixIcon: const Icon(Icons.currency_exchange),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Reference (optional)
                  CustomTextField(
                    controller: _referenceController,
                    labelText: 'Reference (Optional)',
                    hintText: 'Enter reference',
                  ),

                  const SizedBox(height: 16),

                  // Note (optional)
                  CustomTextField(
                    controller: _noteController,
                    labelText: 'Note (Optional)',
                    hintText: 'Enter note',
                    maxLines: 3,
                  ),

                  const SizedBox(height: 24),

                  // Recent transfers section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Recent Transfers',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _showRecentTransfers = !_showRecentTransfers;
                          });
                        },
                        child: Text(
                          _showRecentTransfers ? 'Hide' : 'Show',
                          style: const TextStyle(
                            color: ColorConstants.primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),

                  if (_showRecentTransfers) ...[
                    const SizedBox(height: 8),
                    _buildRecentTransfers(),
                    const SizedBox(height: 16),
                  ],

                  // Transfer button
                  CustomButton(
                    text: 'Transfer',
                    onPressed: _handleBankTransfer,
                    isLoading: _isLoading,
                  ),
                ],
              ),
            ),
          ),

          // Bank selection overlay
          if (_isBankSelectionVisible)
            Positioned.fill(
              child: GestureDetector(
                onTap: _toggleBankSelection,
                child: Container(
                  color: Colors.black54,
                  child: Column(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {}, // Prevent tap through
                          child: Container(
                            margin: const EdgeInsets.only(top: 100),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(16),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Header
                                Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    children: [
                                      const Text(
                                        'Select Bank',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const Spacer(),
                                      IconButton(
                                        icon: const Icon(Icons.close),
                                        onPressed: _toggleBankSelection,
                                      ),
                                    ],
                                  ),
                                ),

                                // Search field
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16),
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText: 'Search banks',
                                      prefixIcon: const Icon(Icons.search),
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: EdgeInsets.zero,
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Popular banks
                                const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  child: Text(
                                    'Popular Banks',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 8),

                                SizedBox(
                                  height: 100,
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    itemCount: popularBanks.length,
                                    itemBuilder: (context, index) {
                                      final bank = popularBanks[index];
                                      return Padding(
                                        padding: const EdgeInsets.only(right: 16),
                                        child: InkWell(
                                          onTap: () => _selectBank(bank),
                                          child: Column(
                                            children: [
                                              Container(
                                                width: 60,
                                                height: 60,
                                                decoration: BoxDecoration(
                                                  color: bank.color.withAlpha(25),
                                                  shape: BoxShape.circle,
                                                ),
                                                child: Center(
                                                  child: Icon(
                                                    Icons.account_balance,
                                                    color: bank.color,
                                                    size: 30,
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                bank.name,
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // All banks
                                const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  child: Text(
                                    'All Banks',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 8),

                                Expanded(
                                  child: ListView.builder(
                                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                    itemCount: banks.length,
                                    itemBuilder: (context, index) {
                                      final bank = banks[index];
                                      return ListTile(
                                        leading: Container(
                                          width: 40,
                                          height: 40,
                                          decoration: BoxDecoration(
                                            color: bank.color.withAlpha(25),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Center(
                                            child: Icon(
                                              Icons.account_balance,
                                              color: bank.color,
                                              size: 24,
                                            ),
                                          ),
                                        ),
                                        title: Text(bank.name),
                                        onTap: () => _selectBank(bank),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

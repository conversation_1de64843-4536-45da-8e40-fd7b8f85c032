import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/services/share_service.dart';
import 'package:irecharge_pro/core/widgets/loading_indicator.dart';
import 'package:irecharge_pro/data/models/bank_model.dart';
import 'package:irecharge_pro/data/models/bill_payment_transaction_model.dart';
import 'package:irecharge_pro/data/models/mobile_banking_transaction_model.dart';
import 'package:irecharge_pro/data/models/mobile_operator_model.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';
import 'package:irecharge_pro/data/models/shop_transaction_model.dart';
import 'package:irecharge_pro/data/models/unified_transaction_model.dart';
import 'package:irecharge_pro/data/providers/bank_transfer_provider.dart';
import 'package:irecharge_pro/data/providers/bill_payment_provider.dart';
import 'package:irecharge_pro/data/providers/mobile_banking_provider.dart';
import 'package:irecharge_pro/data/providers/recharge_providers.dart';
import 'package:irecharge_pro/data/providers/shop_transaction_provider.dart';
import 'package:irecharge_pro/data/providers/unified_transaction_provider.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Unified History Screen that shows history from all services
class UnifiedHistoryScreen extends ConsumerStatefulWidget {
  const UnifiedHistoryScreen({super.key});

  @override
  ConsumerState<UnifiedHistoryScreen> createState() => _UnifiedHistoryScreenState();
}

class _UnifiedHistoryScreenState extends ConsumerState<UnifiedHistoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isFilterExpanded = false;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // Date range
  DateTimeRange? _selectedDateRange;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);

    // Add listener to search controller
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text.toLowerCase();
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// Toggle filter panel
  void _toggleFilterPanel() {
    setState(() {
      _isFilterExpanded = !_isFilterExpanded;
    });
  }

  /// Clear filters
  void _clearFilters() {
    setState(() {
      _searchController.clear();
      _selectedDateRange = null;
    });
  }

  /// Show date range picker
  Future<void> _selectDateRange() async {
    final initialDateRange = _selectedDateRange ?? DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 7)),
      end: DateTime.now(),
    );

    final newDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: ColorConstants.primaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      setState(() {
        _selectedDateRange = newDateRange;
      });
    }
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Transaction History'
              : 'লেনদেনের ইতিহাস',
        ),
        actions: [
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _toggleFilterPanel,
            tooltip: _isFilterExpanded ? 'Hide Filters' : 'Show Filters',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'All'
                  : 'সব',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Recharge'
                  : 'রিচার্জ',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Mobile Banking'
                  : 'মোবাইল ব্যাংকিং',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Bills'
                  : 'বিল',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Bank Transfer'
                  : 'ব্যাংক ট্রান্সফার',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Shop'
                  : 'শপ',
            ),
            Tab(
              text: ref.watch(languageProvider) == AppLanguage.english
                  ? 'Wallet'
                  : 'ওয়ালেট',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Filter panel
          if (_isFilterExpanded)
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.grey.shade100,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Search field
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Search transactions...'
                          : 'লেনদেন অনুসন্ধান করুন...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Date range filter
                  Row(
                    children: [
                      const Text(
                        'Date Range:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _selectDateRange,
                          child: Text(
                            _selectedDateRange != null
                                ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                                : 'Select Date Range',
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: _clearFilters,
                        tooltip: 'Clear Filters',
                      ),
                    ],
                  ),
                ],
              ),
            ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // All transactions tab
                _buildAllTransactionsTab(),

                // Recharge tab
                _buildRechargeTab(),

                // Mobile Banking tab
                _buildMobileBankingTab(),

                // Bills tab
                _buildBillsTab(),

                // Bank Transfer tab
                _buildBankTransferTab(),

                // Shop tab
                _buildShopTab(),

                // Wallet tab
                _buildWalletTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build All Transactions tab
  Widget _buildAllTransactionsTab() {
    // For now, we'll show a placeholder with improved design
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'All Transactions'
                : 'সমস্ত লেনদেন',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'View your complete transaction history across all services'
                : 'সমস্ত পরিষেবা জুড়ে আপনার সম্পূর্ণ লেনদেনের ইতিহাস দেখুন',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Switch to specific tabs
              _tabController.animateTo(1); // Switch to Recharge tab
            },
            icon: const Icon(Icons.filter_alt),
            label: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'View by Category'
                  : 'ক্যাটাগরি অনুযায়ী দেখুন',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// Build unified transaction card
  Widget _buildUnifiedTransactionCard(UnifiedTransaction transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 1,
      child: InkWell(
        onTap: () => _showTransactionDetails(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Transaction icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: transaction.iconColor.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    transaction.icon,
                    color: transaction.iconColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Transaction details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      transaction.subtitle,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

              // Amount and time
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    transaction.formattedAmount,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    transaction.formattedTime,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),

              // Status indicator
              const SizedBox(width: 8),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: transaction.statusColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show transaction details
  void _showTransactionDetails(UnifiedTransaction transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Transaction Details'
                          : 'লেনদেনের বিবরণ',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const Divider(),

                // Transaction icon and title
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: transaction.iconColor.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(
                          transaction.icon,
                          color: transaction.iconColor,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            transaction.subtitle,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Amount
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: transaction.statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Amount'
                            : 'পরিমাণ',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        transaction.formattedAmount,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: transaction.statusColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          transaction.statusText,
                          style: TextStyle(
                            color: transaction.statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Transaction details
                _buildTransactionDetailsSection(transaction),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.share,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Share'
                          : 'শেয়ার',
                      onTap: () => _shareTransaction(transaction),
                    ),
                    _buildActionButton(
                      icon: Icons.copy,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Copy ID'
                          : 'আইডি কপি',
                      onTap: () => _copyTransactionId(transaction.id),
                    ),
                    _buildActionButton(
                      icon: Icons.help_outline,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Support'
                          : 'সাপোর্ট',
                      onTap: () => _contactSupport(transaction),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build transaction details section based on category
  Widget _buildTransactionDetailsSection(UnifiedTransaction transaction) {
    switch (transaction.category) {
      case TransactionCategory.recharge:
        final rechargeTransaction = transaction.originalTransaction as RechargeTransaction;
        final operator = MobileOperator.getOperator(rechargeTransaction.operatorId);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Transaction ID', rechargeTransaction.id),
            _buildDetailRow('Phone Number', rechargeTransaction.phoneNumber),
            _buildDetailRow('Operator', operator?.name ?? 'Unknown'),
            if (rechargeTransaction.packageId != null)
              _buildDetailRow('Package', rechargeTransaction.packageId!),
            _buildDetailRow('Payment Method', _getPaymentMethodText(rechargeTransaction.paymentMethod)),
            _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
            if (rechargeTransaction.transactionReference != null)
              _buildDetailRow('Reference', rechargeTransaction.transactionReference!),
          ],
        );

      case TransactionCategory.mobileBanking:
        final mbTransaction = transaction.originalTransaction as MobileBankingTransaction;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Transaction ID', mbTransaction.id),
            _buildDetailRow('Phone Number', mbTransaction.phoneNumber),
            _buildDetailRow('Operator', mbTransaction.operator?.name ?? 'Unknown'),
            _buildDetailRow('Type', mbTransaction.typeText),
            if (mbTransaction.fee != null && mbTransaction.fee! > 0)
              _buildDetailRow('Fee', mbTransaction.formattedFee),
            if (mbTransaction.fee != null && mbTransaction.fee! > 0)
              _buildDetailRow('Total', mbTransaction.formattedTotalAmount),
            _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
            if (mbTransaction.recipientName != null)
              _buildDetailRow('Recipient', mbTransaction.recipientName!),
            if (mbTransaction.transactionReference != null)
              _buildDetailRow('Reference', mbTransaction.transactionReference!),
          ],
        );

      case TransactionCategory.billPayment:
        final billTransaction = transaction.originalTransaction as BillPaymentTransaction;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Transaction ID', billTransaction.id),
            _buildDetailRow('Provider', billTransaction.provider),
            _buildDetailRow('Account Number', billTransaction.accountNumber),
            _buildDetailRow('Bill Type', billTransaction.typeText),
            if (billTransaction.billNumber != null)
              _buildDetailRow('Bill Number', billTransaction.billNumber!),
            if (billTransaction.billMonth != null)
              _buildDetailRow('Bill Month', billTransaction.billMonth!),
            if (billTransaction.customerName != null)
              _buildDetailRow('Customer Name', billTransaction.customerName!),
            if (billTransaction.fee != null && billTransaction.fee! > 0)
              _buildDetailRow('Fee', billTransaction.formattedFee),
            if (billTransaction.fee != null && billTransaction.fee! > 0)
              _buildDetailRow('Total', billTransaction.formattedTotalAmount),
            _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
            if (billTransaction.transactionReference != null)
              _buildDetailRow('Reference', billTransaction.transactionReference!),
          ],
        );

      case TransactionCategory.bankTransfer:
        final transfer = transaction.originalTransaction as BankTransfer;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Transaction ID', transfer.id),
            _buildDetailRow('Bank', transfer.bank?.name ?? 'Unknown'),
            _buildDetailRow('Account Number', transfer.accountNumber),
            _buildDetailRow('Account Name', transfer.accountName),
            if (transfer.branchName != null)
              _buildDetailRow('Branch', transfer.branchName!),
            _buildDetailRow('Account Type', _getAccountTypeText(transfer.accountType)),
            _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
            if (transfer.reference != null)
              _buildDetailRow('Reference', transfer.reference!),
            if (transfer.note != null)
              _buildDetailRow('Note', transfer.note!),
          ],
        );

      case TransactionCategory.wallet:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Transaction ID', transaction.id),
            _buildDetailRow('Type', transaction.title),
            _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
          ],
        );
    }
  }

  /// Build detail row
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// Build action button
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: ColorConstants.primaryColor.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: ColorConstants.primaryColor,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Share transaction
  void _shareTransaction(UnifiedTransaction transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.text_format),
            title: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Share as Text'
                  : 'টেক্সট হিসাবে শেয়ার করুন',
            ),
            onTap: () {
              Navigator.pop(context);
              ShareService.shareTransactionAsText(transaction);
            },
          ),
          ListTile(
            leading: const Icon(Icons.image),
            title: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Share as Image'
                  : 'ছবি হিসাবে শেয়ার করুন',
            ),
            onTap: () {
              Navigator.pop(context);
              ShareService.shareTransactionAsImage(context, transaction);
            },
          ),
        ],
      ),
    );
  }

  /// Copy transaction ID
  void _copyTransactionId(String id) {
    Clipboard.setData(ClipboardData(text: id));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Transaction ID copied to clipboard'
              : 'লেনদেন আইডি ক্লিপবোর্ডে কপি করা হয়েছে',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Contact support
  void _contactSupport(dynamic transactionId) {
    // In a real app, this would navigate to a support screen or open a chat
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Support feature coming soon'
              : 'সাপোর্ট ফিচার শীঘ্রই আসছে',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Get payment method text
  String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.mobileBanking:
        return 'Mobile Banking';
      case PaymentMethod.internetBanking:
        return 'Internet Banking';
    }
  }

  /// Get account type text
  String _getAccountTypeText(BankAccountType type) {
    switch (type) {
      case BankAccountType.savings:
        return 'Savings';
      case BankAccountType.current:
        return 'Current';
      case BankAccountType.fixed:
        return 'Fixed Deposit';
      case BankAccountType.other:
        return 'Other';
    }
  }

  /// Build Recharge tab
  Widget _buildRechargeTab() {
    final transactionsAsyncValue = ref.watch(rechargeTransactionsProvider);

    return transactionsAsyncValue.when(
      data: (transactions) {
        if (transactions.isEmpty) {
          return Center(
            child: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No recharge history found'
                  : 'কোন রিচার্জ ইতিহাস পাওয়া যায়নি',
            ),
          );
        }

        // Apply search filter
        var filteredTransactions = transactions;
        if (_searchQuery.isNotEmpty) {
          filteredTransactions = transactions.where((tx) {
            return tx.phoneNumber.contains(_searchQuery) ||
                   tx.id.toLowerCase().contains(_searchQuery) ||
                   (tx.transactionReference?.toLowerCase().contains(_searchQuery) ?? false);
          }).toList();
        }

        // Apply date filter
        if (_selectedDateRange != null) {
          filteredTransactions = filteredTransactions.where((tx) {
            return tx.timestamp.isAfter(_selectedDateRange!.start) &&
                   tx.timestamp.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
          }).toList();
        }

        // Sort transactions by timestamp (newest first)
        filteredTransactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        if (filteredTransactions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.search_off,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'No matching recharge transactions found'
                      : 'কোন মিলযুক্ত রিচার্জ লেনদেন পাওয়া যায়নি',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                if (_searchQuery.isNotEmpty || _selectedDateRange != null)
                  TextButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.filter_alt_off),
                    label: Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Clear Filters'
                          : 'ফিল্টার মুছুন',
                    ),
                  ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredTransactions.length,
          itemBuilder: (context, index) {
            final transaction = filteredTransactions[index];
            return _buildRechargeTransactionCard(context, transaction);
          },
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Failed to load recharge history'
                  : 'রিচার্জ ইতিহাস লোড করতে ব্যর্থ হয়েছে',
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => ref.refresh(rechargeTransactionsProvider),
              child: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Retry'
                    : 'পুনরায় চেষ্টা করুন',
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build Mobile Banking tab
  Widget _buildMobileBankingTab() {
    // Get mobile banking transactions from provider
    final transactions = ref.watch(mobileBankingTransactionsProvider);

    // If no transactions, show empty state
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No Mobile Banking History'
                  : 'কোন মোবাইল ব্যাংকিং ইতিহাস নেই',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Your mobile banking transaction history will appear here'
                  : 'আপনার মোবাইল ব্যাংকিং লেনদেনের ইতিহাস এখানে দেখা যাবে',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    // Group transactions by date
    final Map<String, List<MobileBankingTransaction>> groupedTransactions = {};
    for (final transaction in transactions) {
      final dateKey = transaction.formattedDate;
      if (!groupedTransactions.containsKey(dateKey)) {
        groupedTransactions[dateKey] = [];
      }
      groupedTransactions[dateKey]!.add(transaction);
    }

    // Show transactions grouped by date
    return Column(
      children: [
        // Search and filter bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Search field
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Search transactions'
                        : 'লেনদেন অনুসন্ধান করুন',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onChanged: (value) {
                    // TODO: Implement search functionality
                  },
                ),
              ),
              const SizedBox(width: 8),

              // Filter button
              IconButton(
                onPressed: () {
                  _showMobileBankingFilterDialog();
                },
                icon: const Icon(Icons.filter_list),
                tooltip: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Filter'
                    : 'ফিল্টার',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.grey.shade200,
                ),
              ),
            ],
          ),
        ),

        // Transactions list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: groupedTransactions.length,
            itemBuilder: (context, index) {
              final dateKey = groupedTransactions.keys.elementAt(index);
              final transactions = groupedTransactions[dateKey]!;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date header
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      dateKey,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),

                  // Transactions for this date
                  ...transactions.map((transaction) => _buildMobileBankingTransactionCard(transaction)),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  /// Show mobile banking filter dialog
  void _showMobileBankingFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Filter Transactions'
                      : 'লেনদেন ফিল্টার করুন',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),

            // Filter by status
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Status'
                  : 'স্ট্যাটাস',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: MobileBankingStatus.values.map((status) {
                return FilterChip(
                  label: Text(_getMobileBankingStatusText(status)),
                  selected: false, // TODO: Implement filter state
                  onSelected: (selected) {
                    // TODO: Implement filter functionality
                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Filter by type
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Transaction Type'
                  : 'লেনদেনের ধরন',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: MobileBankingType.values.map((type) {
                return FilterChip(
                  label: Text(_getMobileBankingTypeText(type)),
                  selected: false, // TODO: Implement filter state
                  onSelected: (selected) {
                    // TODO: Implement filter functionality
                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Filter by operator
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Operator'
                  : 'অপারেটর',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('bKash'),
                  selected: false, // TODO: Implement filter state
                  onSelected: (selected) {
                    // TODO: Implement filter functionality
                    Navigator.pop(context);
                  },
                ),
                FilterChip(
                  label: const Text('Nagad'),
                  selected: false, // TODO: Implement filter state
                  onSelected: (selected) {
                    // TODO: Implement filter functionality
                    Navigator.pop(context);
                  },
                ),
                FilterChip(
                  label: const Text('Rocket'),
                  selected: false, // TODO: Implement filter state
                  onSelected: (selected) {
                    // TODO: Implement filter functionality
                    Navigator.pop(context);
                  },
                ),
                FilterChip(
                  label: const Text('Upay'),
                  selected: false, // TODO: Implement filter state
                  onSelected: (selected) {
                    // TODO: Implement filter functionality
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Apply and Reset buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // TODO: Reset filters
                    Navigator.pop(context);
                  },
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Reset'
                        : 'রিসেট',
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Apply filters
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Apply'
                        : 'প্রয়োগ করুন',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build mobile banking transaction card
  Widget _buildMobileBankingTransactionCard(MobileBankingTransaction transaction) {
    final operator = transaction.operator;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 1,
      child: InkWell(
        onTap: () => _showMobileBankingTransactionDetails(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Operator logo
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: operator?.color.withAlpha(25) ?? Colors.grey.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    Icons.account_balance_wallet,
                    color: operator?.color ?? Colors.grey,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Transaction details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.typeText,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '${operator?.name ?? 'Unknown'} • ${transaction.phoneNumber}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

              // Amount and time
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    transaction.formattedAmount,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    transaction.formattedTime,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),

              // Status indicator
              const SizedBox(width: 8),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: transaction.statusColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show mobile banking transaction details
  void _showMobileBankingTransactionDetails(MobileBankingTransaction transaction) {
    final operator = transaction.operator;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Transaction Details'
                          : 'লেনদেনের বিবরণ',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const Divider(),

                // Operator logo and type
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: operator?.color.withAlpha(25) ?? Colors.grey.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(
                          Icons.account_balance_wallet,
                          color: operator?.color ?? Colors.grey,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction.typeText,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${operator?.name ?? 'Unknown'} • ${transaction.phoneNumber}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Amount
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: transaction.statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Amount'
                            : 'পরিমাণ',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        transaction.formattedAmount,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: transaction.statusColor,
                        ),
                      ),
                      if (transaction.fee != null && transaction.fee! > 0) ...[
                        const SizedBox(height: 4),
                        Text(
                          '${ref.watch(languageProvider) == AppLanguage.english ? 'Fee' : 'ফি'}: ${transaction.formattedFee}',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${ref.watch(languageProvider) == AppLanguage.english ? 'Total' : 'মোট'}: ${transaction.formattedTotalAmount}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          transaction.statusText,
                          style: TextStyle(
                            color: transaction.statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Transaction details
                _buildDetailRow('Transaction ID', transaction.id),
                _buildDetailRow('Phone Number', transaction.phoneNumber),
                _buildDetailRow('Operator', operator?.name ?? 'Unknown'),
                _buildDetailRow('Type', transaction.typeText),
                _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
                if (transaction.recipientName != null)
                  _buildDetailRow('Recipient', transaction.recipientName!),
                if (transaction.transactionReference != null)
                  _buildDetailRow('Reference', transaction.transactionReference!),
                if (transaction.status == MobileBankingStatus.failed && transaction.failureReason != null)
                  _buildDetailRow('Failure Reason', transaction.failureReason!),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.share,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Share'
                          : 'শেয়ার',
                      onTap: () => _shareMobileBankingTransaction(transaction),
                    ),
                    _buildActionButton(
                      icon: Icons.copy,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Copy ID'
                          : 'আইডি কপি',
                      onTap: () => _copyTransactionId(transaction.id),
                    ),
                    _buildActionButton(
                      icon: Icons.help_outline,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Support'
                          : 'সাপোর্ট',
                      onTap: () => _contactSupport(transaction.id),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Share mobile banking transaction
  void _shareMobileBankingTransaction(MobileBankingTransaction transaction) {

    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.text_format),
            title: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Share as Text'
                  : 'টেক্সট হিসাবে শেয়ার করুন',
            ),
            onTap: () {
              Navigator.pop(context);
              _shareMobileBankingTransactionAsText(transaction);
            },
          ),
          ListTile(
            leading: const Icon(Icons.image),
            title: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Share as Image'
                  : 'ছবি হিসাবে শেয়ার করুন',
            ),
            onTap: () {
              Navigator.pop(context);
              _shareMobileBankingTransactionAsImage(transaction);
            },
          ),
        ],
      ),
    );
  }

  /// Share mobile banking transaction as text
  void _shareMobileBankingTransactionAsText(MobileBankingTransaction transaction) {
    final operator = transaction.operator;

    String shareText = 'iRecharge Pro - Mobile Banking Receipt\n';
    shareText += '--------------------------------\n\n';
    shareText += 'Transaction Type: ${transaction.typeText}\n';
    shareText += 'Transaction ID: ${transaction.id}\n';
    shareText += 'Phone Number: ${transaction.phoneNumber}\n';
    shareText += 'Operator: ${operator?.name ?? 'Unknown'}\n';
    shareText += 'Amount: ${transaction.formattedAmount}\n';
    if (transaction.fee != null && transaction.fee! > 0) {
      shareText += 'Fee: ${transaction.formattedFee}\n';
      shareText += 'Total: ${transaction.formattedTotalAmount}\n';
    }
    shareText += 'Status: ${transaction.statusText}\n';
    shareText += 'Date & Time: ${transaction.formattedDate} ${transaction.formattedTime}\n';
    if (transaction.recipientName != null) {
      shareText += 'Recipient: ${transaction.recipientName}\n';
    }
    if (transaction.transactionReference != null) {
      shareText += 'Reference: ${transaction.transactionReference}\n';
    }
    shareText += '\n--------------------------------\n';
    shareText += 'Thank you for using iRecharge Pro!\n';

    // Show a message that sharing is coming soon
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Share mobile banking transaction as image
  void _shareMobileBankingTransactionAsImage(MobileBankingTransaction transaction) {
    // TODO: Implement share as image functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Share as image functionality coming soon'
              : 'ছবি হিসাবে শেয়ার করার ফাংশন শীঘ্রই আসছে',
        ),
      ),
    );
  }

  /// Get mobile banking status text
  String _getMobileBankingStatusText(MobileBankingStatus status) {
    switch (status) {
      case MobileBankingStatus.pending:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Pending'
            : 'অপেক্ষমান';
      case MobileBankingStatus.processing:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Processing'
            : 'প্রক্রিয়াধীন';
      case MobileBankingStatus.completed:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Completed'
            : 'সম্পন্ন';
      case MobileBankingStatus.failed:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Failed'
            : 'ব্যর্থ';
      case MobileBankingStatus.cancelled:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Cancelled'
            : 'বাতিল';
    }
  }

  /// Get mobile banking type text
  String _getMobileBankingTypeText(MobileBankingType type) {
    switch (type) {
      case MobileBankingType.cashIn:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Cash In'
            : 'ক্যাশ ইন';
      case MobileBankingType.cashOut:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Cash Out'
            : 'ক্যাশ আউট';
      case MobileBankingType.sendMoney:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Send Money'
            : 'সেন্ড মানি';
      case MobileBankingType.payment:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Payment'
            : 'পেমেন্ট';
      case MobileBankingType.other:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Other'
            : 'অন্যান্য';
    }
  }

  /// Build Bills tab
  Widget _buildBillsTab() {
    // Get bill payment transactions from provider
    final transactions = ref.watch(billPaymentTransactionsProvider);

    // If no transactions, show empty state
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No Bill Payment History'
                  : 'কোন বিল পেমেন্ট ইতিহাস নেই',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Your bill payment history will appear here'
                  : 'আপনার বিল পেমেন্ট ইতিহাস এখানে দেখা যাবে',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    // Group transactions by date
    final Map<String, List<BillPaymentTransaction>> groupedTransactions = {};
    for (final transaction in transactions) {
      final dateKey = transaction.formattedDate;
      if (!groupedTransactions.containsKey(dateKey)) {
        groupedTransactions[dateKey] = [];
      }
      groupedTransactions[dateKey]!.add(transaction);
    }

    // Show transactions grouped by date
    return Column(
      children: [
        // Search and filter bar
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Search field
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: ref.watch(languageProvider) == AppLanguage.english
                        ? 'Search transactions'
                        : 'লেনদেন অনুসন্ধান করুন',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onChanged: (value) {
                    // Search functionality will be implemented later
                  },
                ),
              ),
              const SizedBox(width: 8),

              // Filter button
              IconButton(
                onPressed: () {
                  _showBillPaymentFilterDialog();
                },
                icon: const Icon(Icons.filter_list),
                tooltip: ref.watch(languageProvider) == AppLanguage.english
                    ? 'Filter'
                    : 'ফিল্টার',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.grey.shade200,
                ),
              ),
            ],
          ),
        ),

        // Transactions list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: groupedTransactions.length,
            itemBuilder: (context, index) {
              final dateKey = groupedTransactions.keys.elementAt(index);
              final transactions = groupedTransactions[dateKey]!;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date header
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      dateKey,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),

                  // Transactions for this date
                  ...transactions.map((transaction) => _buildBillPaymentTransactionCard(transaction)),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  /// Show bill payment filter dialog
  void _showBillPaymentFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Filter Transactions'
                      : 'লেনদেন ফিল্টার করুন',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),

            // Filter by status
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Status'
                  : 'স্ট্যাটাস',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: BillPaymentStatus.values.map((status) {
                return FilterChip(
                  label: Text(_getBillPaymentStatusText(status)),
                  selected: false, // Filter state will be implemented later
                  onSelected: (selected) {
                    // Filter functionality will be implemented later
                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // Filter by type
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Bill Type'
                  : 'বিলের ধরন',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: BillType.values.map((type) {
                return FilterChip(
                  label: Text(_getBillTypeText(type)),
                  selected: false, // Filter state will be implemented later
                  onSelected: (selected) {
                    // Filter functionality will be implemented later
                    Navigator.pop(context);
                  },
                );
              }).toList(),
            ),
            const SizedBox(height: 24),

            // Apply and Reset buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    // Reset filters will be implemented later
                    Navigator.pop(context);
                  },
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Reset'
                        : 'রিসেট',
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    // Apply filters will be implemented later
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    ref.watch(languageProvider) == AppLanguage.english
                        ? 'Apply'
                        : 'প্রয়োগ করুন',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build bill payment transaction card
  Widget _buildBillPaymentTransactionCard(BillPaymentTransaction transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 1,
      child: InkWell(
        onTap: () => _showBillPaymentTransactionDetails(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Bill type icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: transaction.statusColor.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    transaction.typeIcon,
                    color: transaction.statusColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // Transaction details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.typeText,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      '${transaction.provider} • ${transaction.accountNumber}',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),

              // Amount and time
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    transaction.formattedAmount,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    transaction.formattedTime,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),

              // Status indicator
              const SizedBox(width: 8),
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: transaction.statusColor,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show bill payment transaction details
  void _showBillPaymentTransactionDetails(BillPaymentTransaction transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Transaction Details'
                          : 'লেনদেনের বিবরণ',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const Divider(),

                // Bill type icon and details
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: transaction.statusColor.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(
                          transaction.typeIcon,
                          color: transaction.statusColor,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction.typeText,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${transaction.provider} • ${transaction.accountNumber}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Amount
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: transaction.statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Amount'
                            : 'পরিমাণ',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        transaction.formattedAmount,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: transaction.statusColor,
                        ),
                      ),
                      if (transaction.fee != null && transaction.fee! > 0) ...[
                        const SizedBox(height: 4),
                        Text(
                          '${ref.watch(languageProvider) == AppLanguage.english ? 'Fee' : 'ফি'}: ${transaction.formattedFee}',
                          style: TextStyle(
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${ref.watch(languageProvider) == AppLanguage.english ? 'Total' : 'মোট'}: ${transaction.formattedTotalAmount}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ],
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          transaction.statusText,
                          style: TextStyle(
                            color: transaction.statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Transaction details
                _buildDetailRow('Transaction ID', transaction.id),
                _buildDetailRow('Provider', transaction.provider),
                _buildDetailRow('Account Number', transaction.accountNumber),
                _buildDetailRow('Bill Type', transaction.typeText),
                if (transaction.billNumber != null)
                  _buildDetailRow('Bill Number', transaction.billNumber!),
                if (transaction.billMonth != null)
                  _buildDetailRow('Bill Month', transaction.billMonth!),
                if (transaction.customerName != null)
                  _buildDetailRow('Customer Name', transaction.customerName!),
                _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
                if (transaction.transactionReference != null)
                  _buildDetailRow('Reference', transaction.transactionReference!),
                if (transaction.status == BillPaymentStatus.failed && transaction.failureReason != null)
                  _buildDetailRow('Failure Reason', transaction.failureReason!),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.share,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Share'
                          : 'শেয়ার',
                      onTap: () => _shareBillPaymentTransaction(transaction),
                    ),
                    _buildActionButton(
                      icon: Icons.copy,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Copy ID'
                          : 'আইডি কপি',
                      onTap: () => _copyTransactionId(transaction.id),
                    ),
                    _buildActionButton(
                      icon: Icons.help_outline,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Support'
                          : 'সাপোর্ট',
                      onTap: () => _contactSupport(transaction.id),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Share bill payment transaction
  void _shareBillPaymentTransaction(BillPaymentTransaction transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.text_format),
            title: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Share as Text'
                  : 'টেক্সট হিসাবে শেয়ার করুন',
            ),
            onTap: () {
              Navigator.pop(context);
              _shareBillPaymentTransactionAsText(transaction);
            },
          ),
          ListTile(
            leading: const Icon(Icons.image),
            title: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Share as Image'
                  : 'ছবি হিসাবে শেয়ার করুন',
            ),
            onTap: () {
              Navigator.pop(context);
              _shareBillPaymentTransactionAsImage(transaction);
            },
          ),
        ],
      ),
    );
  }

  /// Share bill payment transaction as text
  void _shareBillPaymentTransactionAsText(BillPaymentTransaction transaction) {
    String shareText = 'iRecharge Pro - Bill Payment Receipt\n';
    shareText += '--------------------------------\n\n';
    shareText += 'Bill Type: ${transaction.typeText}\n';
    shareText += 'Transaction ID: ${transaction.id}\n';
    shareText += 'Provider: ${transaction.provider}\n';
    shareText += 'Account Number: ${transaction.accountNumber}\n';
    if (transaction.billNumber != null) {
      shareText += 'Bill Number: ${transaction.billNumber}\n';
    }
    if (transaction.billMonth != null) {
      shareText += 'Bill Month: ${transaction.billMonth}\n';
    }
    if (transaction.customerName != null) {
      shareText += 'Customer Name: ${transaction.customerName}\n';
    }
    shareText += 'Amount: ${transaction.formattedAmount}\n';
    if (transaction.fee != null && transaction.fee! > 0) {
      shareText += 'Fee: ${transaction.formattedFee}\n';
      shareText += 'Total: ${transaction.formattedTotalAmount}\n';
    }
    shareText += 'Status: ${transaction.statusText}\n';
    shareText += 'Date & Time: ${transaction.formattedDate} ${transaction.formattedTime}\n';
    if (transaction.transactionReference != null) {
      shareText += 'Reference: ${transaction.transactionReference}\n';
    }
    shareText += '\n--------------------------------\n';
    shareText += 'Thank you for using iRecharge Pro!\n';

    // Show a message that sharing is coming soon
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Share bill payment transaction as image
  void _shareBillPaymentTransactionAsImage(BillPaymentTransaction transaction) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Share as image functionality coming soon'
              : 'ছবি হিসাবে শেয়ার করার ফাংশন শীঘ্রই আসছে',
        ),
      ),
    );
  }

  /// Get bill payment status text
  String _getBillPaymentStatusText(BillPaymentStatus status) {
    switch (status) {
      case BillPaymentStatus.pending:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Pending'
            : 'অপেক্ষমান';
      case BillPaymentStatus.processing:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Processing'
            : 'প্রক্রিয়াধীন';
      case BillPaymentStatus.completed:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Completed'
            : 'সম্পন্ন';
      case BillPaymentStatus.failed:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Failed'
            : 'ব্যর্থ';
      case BillPaymentStatus.cancelled:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Cancelled'
            : 'বাতিল';
    }
  }

  /// Get bill type text
  String _getBillTypeText(BillType type) {
    switch (type) {
      case BillType.electricity:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Electricity'
            : 'বিদ্যুৎ';
      case BillType.water:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Water'
            : 'পানি';
      case BillType.gas:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Gas'
            : 'গ্যাস';
      case BillType.internet:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Internet'
            : 'ইন্টারনেট';
      case BillType.telephone:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Telephone'
            : 'টেলিফোন';
      case BillType.tv:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'TV'
            : 'টিভি';
      case BillType.education:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Education'
            : 'শিক্ষা';
      case BillType.tax:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Tax'
            : 'কর';
      case BillType.other:
        return ref.watch(languageProvider) == AppLanguage.english
            ? 'Other'
            : 'অন্যান্য';
    }
  }

  /// Build Bank Transfer tab
  Widget _buildBankTransferTab() {
    final bankTransfers = ref.watch(bankTransfersProvider);

    // Apply search filter
    var filteredTransfers = bankTransfers;
    if (_searchQuery.isNotEmpty) {
      filteredTransfers = bankTransfers.where((transfer) {
        return transfer.accountName.toLowerCase().contains(_searchQuery) ||
               transfer.accountNumber.toLowerCase().contains(_searchQuery) ||
               transfer.id.toLowerCase().contains(_searchQuery) ||
               (transfer.reference?.toLowerCase().contains(_searchQuery) ?? false) ||
               (transfer.note?.toLowerCase().contains(_searchQuery) ?? false);
      }).toList();
    }

    // Apply date filter
    if (_selectedDateRange != null) {
      filteredTransfers = filteredTransfers.where((transfer) {
        return transfer.date.isAfter(_selectedDateRange!.start) &&
               transfer.date.isBefore(_selectedDateRange!.end.add(const Duration(days: 1)));
      }).toList();
    }

    // Sort transfers by date (newest first)
    filteredTransfers.sort((a, b) => b.date.compareTo(a.date));

    if (filteredTransfers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No bank transfers found'
                  : 'কোন ব্যাংক ট্রান্সফার পাওয়া যায়নি',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            if (_searchQuery.isNotEmpty || _selectedDateRange != null)
              TextButton.icon(
                onPressed: _clearFilters,
                icon: const Icon(Icons.filter_alt_off),
                label: Text(
                  ref.watch(languageProvider) == AppLanguage.english
                      ? 'Clear Filters'
                      : 'ফিল্টার মুছুন',
                ),
              ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTransfers.length,
      itemBuilder: (context, index) {
        final transfer = filteredTransfers[index];
        return _buildBankTransferCard(transfer);
      },
    );
  }

  /// Build bank transfer card
  Widget _buildBankTransferCard(BankTransfer transfer) {
    final bank = transfer.bank;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          // Show transfer details
          _showBankTransferDetails(transfer);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with bank and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Bank info
                  Row(
                    children: [
                      // Bank logo
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: bank?.color.withAlpha(25) ?? Colors.grey.withAlpha(25),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Icon(
                            Icons.account_balance,
                            color: bank?.color ?? Colors.grey,
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            bank?.name ?? 'Unknown Bank',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            transfer.accountNumber,
                            style: const TextStyle(
                              color: ColorConstants.textSecondaryColor,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: transfer.statusColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      transfer.statusText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: transfer.statusColor,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Amount and account info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Amount',
                        style: TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        transfer.formattedAmount,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text(
                        'Account Name',
                        style: TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        transfer.accountName,
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),

              // Date and reference
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${transfer.formattedDate} ${transfer.formattedTime}',
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  if (transfer.reference != null)
                    Row(
                      children: [
                        const Icon(
                          Icons.receipt,
                          size: 16,
                          color: ColorConstants.textSecondaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Ref: ${transfer.reference}',
                          style: const TextStyle(
                            color: ColorConstants.textSecondaryColor,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show bank transfer details
  void _showBankTransferDetails(BankTransfer transfer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transfer Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Transfer ID', transfer.id),
              _buildDetailRow('Bank', transfer.bank?.name ?? 'Unknown Bank'),
              _buildDetailRow('Account Number', transfer.accountNumber),
              _buildDetailRow('Account Name', transfer.accountName),
              if (transfer.branchName != null)
                _buildDetailRow('Branch', transfer.branchName!),
              _buildDetailRow('Account Type', transfer.accountType.name),
              _buildDetailRow('Amount', transfer.formattedAmount),
              _buildDetailRow('Date & Time', '${transfer.formattedDate} ${transfer.formattedTime}'),
              _buildDetailRow('Status', transfer.statusText),
              if (transfer.reference != null)
                _buildDetailRow('Reference', transfer.reference!),
              if (transfer.note != null)
                _buildDetailRow('Note', transfer.note!),
              if (transfer.transactionId != null)
                _buildDetailRow('Transaction ID', transfer.transactionId!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }



  /// Build Shop tab
  Widget _buildShopTab() {
    final shopTransactions = ref.watch(shopTransactionsProvider);
    final filteredTransactions = getFilteredShopTransactions(
      shopTransactions,
      const ShopTransactionFilter(),
    );

    // Group transactions by date
    final groupedTransactions = <String, List<ShopTransaction>>{};
    for (final transaction in filteredTransactions) {
      final dateKey = transaction.formattedDate;
      if (!groupedTransactions.containsKey(dateKey)) {
        groupedTransactions[dateKey] = [];
      }
      groupedTransactions[dateKey]!.add(transaction);
    }

    if (filteredTransactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'No Shop Transactions'
                  : 'কোন শপ লেনদেন নেই',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'Your shop purchase history will appear here'
                  : 'আপনার শপ কেনাকাটার ইতিহাস এখানে দেখা যাবে',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                context.push(RouteNames.shop);
              },
              icon: const Icon(Icons.shopping_cart),
              label: Text(
                ref.watch(languageProvider) == AppLanguage.english
                    ? 'Go to Shop'
                    : 'শপে যান',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedTransactions.length,
      itemBuilder: (context, index) {
        final dateKey = groupedTransactions.keys.elementAt(index);
        final transactions = groupedTransactions[dateKey]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                dateKey,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),

            // Transactions for this date
            ...transactions.map((transaction) => _buildShopTransactionCard(transaction)),
          ],
        );
      },
    );
  }

  /// Build shop transaction card
  Widget _buildShopTransactionCard(ShopTransaction transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 1,
      child: InkWell(
        onTap: () => _showShopTransactionDetails(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with product and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Product info
                  Expanded(
                    child: Row(
                      children: [
                        // Product image or icon
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: transaction.statusColor.withAlpha(25),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Icon(
                              Icons.shopping_bag,
                              color: transaction.statusColor,
                              size: 20,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),

                        // Product name and ID
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                transaction.productName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                'Order ID: ${transaction.id}',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: transaction.statusColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      transaction.statusText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: transaction.statusColor,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Amount and quantity
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Amount',
                        style: TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        transaction.formattedFinalAmount,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    'Qty: ${transaction.quantity}',
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),

              // Date and payment method
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${transaction.formattedDate} ${transaction.formattedTime}',
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  if (transaction.paymentMethod != null)
                    Row(
                      children: [
                        const Icon(
                          Icons.payment,
                          size: 16,
                          color: ColorConstants.textSecondaryColor,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          transaction.paymentMethod!,
                          style: const TextStyle(
                            color: ColorConstants.textSecondaryColor,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Show shop transaction details
  void _showShopTransactionDetails(ShopTransaction transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      ref.watch(languageProvider) == AppLanguage.english
                          ? 'Order Details'
                          : 'অর্ডারের বিবরণ',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                const Divider(),

                // Product info
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: transaction.statusColor.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(
                          Icons.shopping_bag,
                          color: transaction.statusColor,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction.productName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Order ID: ${transaction.id}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Amount
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: transaction.statusColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Text(
                        ref.watch(languageProvider) == AppLanguage.english
                            ? 'Amount'
                            : 'পরিমাণ',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        transaction.formattedFinalAmount,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: transaction.statusColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Column(
                            children: [
                              Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'Price'
                                    : 'মূল্য',
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                transaction.formattedPrice,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(width: 24),
                          Column(
                            children: [
                              Text(
                                ref.watch(languageProvider) == AppLanguage.english
                                    ? 'Quantity'
                                    : 'পরিমাণ',
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                '${transaction.quantity}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          if (transaction.discount != null && transaction.discount! > 0) ...[
                            const SizedBox(width: 24),
                            Column(
                              children: [
                                Text(
                                  ref.watch(languageProvider) == AppLanguage.english
                                      ? 'Discount'
                                      : 'ছাড়',
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontSize: 12,
                                  ),
                                ),
                                Text(
                                  transaction.formattedDiscount,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          transaction.statusText,
                          style: TextStyle(
                            color: transaction.statusColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Transaction details
                _buildDetailRow('Order ID', transaction.id),
                _buildDetailRow('Product', transaction.productName),
                _buildDetailRow('Price', transaction.formattedPrice),
                _buildDetailRow('Quantity', transaction.quantity.toString()),
                if (transaction.discount != null && transaction.discount! > 0)
                  _buildDetailRow('Discount', transaction.formattedDiscount),
                if (transaction.shippingCost != null && transaction.shippingCost! > 0)
                  _buildDetailRow('Shipping', transaction.formattedShippingCost),
                _buildDetailRow('Total', transaction.formattedFinalAmount),
                _buildDetailRow('Date & Time', '${transaction.formattedDate} ${transaction.formattedTime}'),
                if (transaction.paymentMethod != null)
                  _buildDetailRow('Payment Method', transaction.paymentMethod!),
                if (transaction.shippingAddress != null)
                  _buildDetailRow('Shipping Address', transaction.shippingAddress!),
                if (transaction.customerName != null)
                  _buildDetailRow('Customer Name', transaction.customerName!),
                if (transaction.customerPhone != null)
                  _buildDetailRow('Customer Phone', transaction.customerPhone!),
                if (transaction.transactionReference != null)
                  _buildDetailRow('Reference', transaction.transactionReference!),
                if (transaction.status == ShopTransactionStatus.cancelled && transaction.failureReason != null)
                  _buildDetailRow('Cancellation Reason', transaction.failureReason!),

                const SizedBox(height: 24),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.share,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Share'
                          : 'শেয়ার',
                      onTap: () => _shareShopTransaction(transaction),
                    ),
                    _buildActionButton(
                      icon: Icons.copy,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Copy ID'
                          : 'আইডি কপি',
                      onTap: () => _copyTransactionId(transaction.id),
                    ),
                    _buildActionButton(
                      icon: Icons.help_outline,
                      label: ref.watch(languageProvider) == AppLanguage.english
                          ? 'Support'
                          : 'সাপোর্ট',
                      onTap: () => _contactSupport(transaction.id),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Share shop transaction
  void _shareShopTransaction(ShopTransaction transaction) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          ref.watch(languageProvider) == AppLanguage.english
              ? 'Share functionality coming soon'
              : 'শেয়ার ফাংশন শীঘ্রই আসছে',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Build Wallet tab
  Widget _buildWalletTab() {
    // For now, we'll show a placeholder with a button to navigate to the wallet history screen
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'Wallet Transaction History'
                : 'ওয়ালেট লেনদেনের ইতিহাস',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            ref.watch(languageProvider) == AppLanguage.english
                ? 'View your wallet transaction history'
                : 'আপনার ওয়ালেট লেনদেনের ইতিহাস দেখুন',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.push(RouteNames.walletHistory);
            },
            icon: const Icon(Icons.open_in_new),
            label: Text(
              ref.watch(languageProvider) == AppLanguage.english
                  ? 'View Wallet History'
                  : 'ওয়ালেট ইতিহাস দেখুন',
            ),
          ),
        ],
      ),
    );
  }

  /// Build recharge transaction card
  Widget _buildRechargeTransactionCard(BuildContext context, RechargeTransaction transaction) {
    final operator = transaction.operator;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
      child: InkWell(
        onTap: () {
          context.push('${RouteNames.rechargeDetails}/${transaction.id}');
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with operator and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Operator info
                  Row(
                    children: [
                      // Operator logo
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: operator?.color.withAlpha(25) ?? Colors.grey.withAlpha(25),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: operator != null
                              ? Icon(
                                  Icons.phone_android,
                                  color: operator.color,
                                  size: 24,
                                )
                              : const Icon(
                                  Icons.phone_android,
                                  color: Colors.grey,
                                  size: 24,
                                ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            operator?.name ?? 'Unknown Operator',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            transaction.phoneNumber,
                            style: const TextStyle(
                              color: ColorConstants.textSecondaryColor,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  // Status badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(transaction.getStatusColor()).withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      transaction.status.toString().split('.').last,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(transaction.getStatusColor()),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Amount and package info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Amount',
                        style: TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        transaction.formattedAmount,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  if (transaction.package != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: ColorConstants.primaryColor.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        transaction.package!.title,
                        style: const TextStyle(
                          fontSize: 12,
                          color: ColorConstants.primaryColor,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),
              const Divider(),
              const SizedBox(height: 8),

              // Transaction date and payment method
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${transaction.formattedDate} ${transaction.formattedTime}',
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      const Icon(
                        Icons.payment,
                        size: 16,
                        color: ColorConstants.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        transaction.formattedPaymentMethod,
                        style: const TextStyle(
                          color: ColorConstants.textSecondaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

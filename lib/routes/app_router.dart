import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/core/navigation/app_shell.dart';
import 'package:irecharge_pro/core/services/auth_service.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/features/auth/screens/forgot_password_screen.dart';
import 'package:irecharge_pro/features/auth/screens/login_screen.dart';
import 'package:irecharge_pro/features/auth/screens/onboarding_screen.dart';
import 'package:irecharge_pro/features/auth/screens/register_screen.dart';
import 'package:irecharge_pro/features/auth/screens/splash_screen.dart';
import 'package:irecharge_pro/features/bills/screens/bills_screen.dart';
import 'package:irecharge_pro/features/history/screens/unified_history_screen.dart';
import 'package:irecharge_pro/features/home/<USER>/home_screen.dart';
import 'package:irecharge_pro/features/mobile_banking/screens/mobile_banking_screen.dart';
import 'package:irecharge_pro/features/offers/screens/offers_screen.dart';
import 'package:irecharge_pro/features/recharge/screens/offer_packages_screen.dart';
import 'package:irecharge_pro/features/recharge/screens/recharge_details_screen.dart';
import 'package:irecharge_pro/features/notifications/screens/notifications_screen.dart';
import 'package:irecharge_pro/features/profile/screens/profile_screen.dart';
import 'package:irecharge_pro/features/recharge/screens/recharge_history_screen.dart';
import 'package:irecharge_pro/features/recharge/screens/recharge_screen.dart';
import 'package:irecharge_pro/features/settings/screens/settings_screen.dart';
import 'package:irecharge_pro/features/shop/screens/product_details_screen.dart';
import 'package:irecharge_pro/features/shop/screens/shop_screen.dart';
import 'package:irecharge_pro/features/support/screens/support_screen.dart';
import 'package:irecharge_pro/features/tickets/screens/tickets_screen.dart';
import 'package:irecharge_pro/features/wallet/screens/add_money_screen.dart';
import 'package:irecharge_pro/features/wallet/screens/bank_transfer_screen.dart';
import 'package:irecharge_pro/features/wallet/screens/send_money_screen.dart';
import 'package:irecharge_pro/features/wallet/screens/wallet_history_screen.dart';
import 'package:irecharge_pro/features/agent/screens/agent_customer_list_screen.dart';
import 'package:irecharge_pro/features/agent/screens/commission_management_screen.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// App router configuration using GoRouter
class AppRouter {
  final AuthService _authService;

  AppRouter({required AuthService authService}) : _authService = authService;

  late final GoRouter router = GoRouter(
    initialLocation: RouteNames.splash,
    debugLogDiagnostics: true,
    redirect: _handleRedirect,
    routes: [
      // Auth routes
      GoRoute(
        path: RouteNames.splash,
        builder: (context, state) => ProviderScope(
          overrides: [
            authServiceProvider.overrideWithValue(_authService),
          ],
          child: const SplashScreen(),
        ),
      ),
      GoRoute(
        path: RouteNames.onboarding,
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: RouteNames.login,
        builder: (context, state) => ProviderScope(
          overrides: [
            authServiceProvider.overrideWithValue(_authService),
          ],
          child: const LoginScreen(),
        ),
      ),
      GoRoute(
        path: RouteNames.register,
        builder: (context, state) => ProviderScope(
          overrides: [
            authServiceProvider.overrideWithValue(_authService),
          ],
          child: const RegisterScreen(),
        ),
      ),
      GoRoute(
        path: RouteNames.forgotPassword,
        builder: (context, state) => ProviderScope(
          overrides: [
            authServiceProvider.overrideWithValue(_authService),
          ],
          child: const ForgotPasswordScreen(),
        ),
      ),

      // Main routes with shell
      GoRoute(
        path: RouteNames.home,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0,
            child: const HomeScreen(),
          ),
        ),
      ),

      // Recharge routes
      GoRoute(
        path: RouteNames.recharge,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 1,
            child: const RechargeScreen(),
          ),
        ),
      ),
      GoRoute(
        path: RouteNames.rechargeHistory,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 1,
            child: const RechargeHistoryScreen(),
          ),
        ),
      ),
      GoRoute(
        path: RouteNames.offerPackages,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 1,
            child: const OfferPackagesScreen(),
          ),
        ),
      ),
      GoRoute(
        path: RouteNames.rechargeDetails,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 1,
            child: const RechargeDetailsScreen(),
          ),
        ),
      ),
      GoRoute(
        path: '${RouteNames.rechargeDetails}/:id',
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 1,
            child: RechargeDetailsScreen(
              transactionId: state.pathParameters['id'],
            ),
          ),
        ),
      ),
      GoRoute(
        path: '/recharge/confirmation',
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 1,
            child: const RechargeDetailsScreen(),
          ),
        ),
      ),

      // Shop routes
      GoRoute(
        path: RouteNames.shop,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 2,
            child: const ShopScreen(),
          ),
        ),
      ),
      GoRoute(
        path: RouteNames.productDetails,
        pageBuilder: (context, state) {
          final productId = state.pathParameters['id'] ?? '';
          return NoTransitionPage(
            child: AppShell(
              currentIndex: 2,
              child: ProductDetailsScreen(productId: productId),
            ),
          );
        },
      ),

      // Wallet routes
      GoRoute(
        path: RouteNames.addMoney,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab since it's related to wallet
            child: const AddMoneyScreen(),
          ),
        ),
      ),

      // Settings routes
      GoRoute(
        path: RouteNames.settings,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const SettingsScreen(),
          ),
        ),
      ),

      // Profile routes
      GoRoute(
        path: RouteNames.profile,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const ProfileScreen(),
          ),
        ),
      ),

      // Support routes
      GoRoute(
        path: RouteNames.support,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const SupportScreen(),
          ),
        ),
      ),

      // Wallet history routes
      GoRoute(
        path: RouteNames.walletHistory,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const WalletHistoryScreen(),
          ),
        ),
      ),

      // Send money routes
      GoRoute(
        path: RouteNames.sendMoney,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const SendMoneyScreen(),
          ),
        ),
      ),

      // Bank transfer routes
      GoRoute(
        path: RouteNames.bankTransfer,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const BankTransferScreen(),
          ),
        ),
      ),

      // Notifications routes
      GoRoute(
        path: RouteNames.notifications,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const NotificationsScreen(),
          ),
        ),
      ),

      // Bills routes
      GoRoute(
        path: RouteNames.bills,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 3,
            child: const BillsScreen(),
          ),
        ),
      ),

      // Tickets routes
      GoRoute(
        path: RouteNames.tickets,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const TicketsScreen(),
          ),
        ),
      ),

      // Offers routes
      GoRoute(
        path: RouteNames.offers,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const OffersScreen(),
          ),
        ),
      ),

      // Mobile Banking routes
      GoRoute(
        path: RouteNames.mobileBanking,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 2,
            child: const MobileBankingScreen(),
          ),
        ),
      ),

      // Agent Customer routes
      GoRoute(
        path: RouteNames.agentCustomers,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const AgentCustomerListScreen(),
          ),
        ),
      ),

      // Agent Commission routes
      GoRoute(
        path: RouteNames.agentCommissions,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 0, // Home tab
            child: const CommissionManagementScreen(),
          ),
        ),
      ),

      // History routes
      GoRoute(
        path: RouteNames.history,
        pageBuilder: (context, state) => NoTransitionPage(
          child: AppShell(
            currentIndex: 3, // History tab
            child: const UnifiedHistoryScreen(),
          ),
        ),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text(
          'Error: Page not found\n${state.error}',
          textAlign: TextAlign.center,
        ),
      ),
    ),
  );

  /// Handle authentication redirects
  Future<String?> _handleRedirect(BuildContext context, GoRouterState state) async {
    final isLoggedIn = await _authService.isLoggedIn();
    final isGoingToLogin = state.matchedLocation == RouteNames.login;
    final isGoingToRegister = state.matchedLocation == RouteNames.register;
    final isGoingToForgotPassword = state.matchedLocation == RouteNames.forgotPassword;
    final isGoingToSplash = state.matchedLocation == RouteNames.splash;
    final isGoingToOnboarding = state.matchedLocation == RouteNames.onboarding;

    // Allow access to splash and onboarding regardless of auth state
    if (isGoingToSplash || isGoingToOnboarding) {
      return null;
    }

    // If not logged in and trying to access protected route, redirect to login
    if (!isLoggedIn &&
        !isGoingToLogin &&
        !isGoingToRegister &&
        !isGoingToForgotPassword) {
      return RouteNames.login;
    }

    // If logged in and trying to access auth routes, redirect to home
    if (isLoggedIn &&
        (isGoingToLogin || isGoingToRegister || isGoingToForgotPassword)) {
      return RouteNames.home;
    }

    // No redirect needed
    return null;
  }
}

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/localization/app_localizations.dart';
import 'package:irecharge_pro/core/providers/language_provider.dart';
import 'package:irecharge_pro/core/providers/theme_provider.dart';
import 'package:irecharge_pro/core/services/storage_service.dart';
import 'package:irecharge_pro/core/utils/logger_util.dart';
import 'package:irecharge_pro/data/providers/providers.dart';
import 'package:irecharge_pro/firebase_options.dart';
import 'package:irecharge_pro/routes/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with options
    // Check if Firebase is already initialized to avoid duplicate app error
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        name: 'irecharge-pro',
        options: DefaultFirebaseOptions.currentPlatform,
      );
    } else {
      Firebase.app('irecharge-pro');
    }

    // Initialize storage services
    final storageService = StorageService();
    await storageService.init();

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Run the app
    runApp(
      ProviderScope(
        overrides: [
          storageServiceProvider.overrideWithValue(storageService),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e, stackTrace) {
    LoggerUtil.e('Error initializing app', e, stackTrace);
    runApp(const ErrorApp());
  }
}

/// Main application widget
class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the auth service from the provider
    final authService = ref.watch(authServiceProvider);

    // Get theme mode from provider
    final themeMode = ref.watch(themeModeProvider);

    // Get language from provider
    final language = ref.watch(languageProvider);

    // Initialize router
    final appRouter = AppRouter(authService: authService);

    return MaterialApp.router(
      title: AppConstants.appName,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: themeMode,
      debugShowCheckedModeBanner: false,
      routerConfig: appRouter.router,
      locale: language.locale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLanguage.values.map((lang) => lang.locale).toList(),
    );
  }
}

/// Error fallback app in case of initialization errors
class ErrorApp extends StatelessWidget {
  const ErrorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.red),
      ),
      home: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 80,
              ),
              const SizedBox(height: 16),
              const Text(
                'Failed to initialize app',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Please restart the application',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  SystemNavigator.pop();
                },
                child: const Text('Close App'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

{"@@locale": "en", "appName": "iRecharge Pro", "common": {"ok": "OK", "cancel": "Cancel", "yes": "Yes", "no": "No", "save": "Save", "delete": "Delete", "edit": "Edit", "next": "Next", "back": "Back", "done": "Done", "retry": "Retry", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "search": "Search", "noResults": "No results found", "noData": "No data available", "networkError": "Network error", "serverError": "Server error", "unexpectedError": "Unexpected error occurred", "tryAgain": "Please try again", "somethingWentWrong": "Something went wrong"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "logout": "Logout", "phone": "Phone Number", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "currentPassword": "Current Password", "newPassword": "New Password", "name": "Full Name", "otp": "OTP", "enterOtp": "Enter OTP", "otpSent": "OTP sent to your phone", "resendOtp": "Resend OTP", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "loginSuccess": "Login successful", "registerSuccess": "Registration successful", "logoutSuccess": "Logout successful", "passwordResetSuccess": "Password reset successful", "passwordChangeSuccess": "Password changed successfully", "invalidCredentials": "Invalid credentials", "accountNotFound": "Account not found", "accountExists": "Account already exists", "invalidOtp": "Invalid OTP", "otpExpired": "OTP expired", "passwordMismatch": "Passwords do not match", "invalidPassword": "Invalid password", "invalidPhone": "Invalid phone number", "invalidEmail": "Invalid email address", "requiredField": "This field is required", "passwordRequirements": "Password must be at least 8 characters with uppercase, lowercase, and numbers", "biometricLogin": "Login with Biometrics", "biometricPrompt": "Authenticate to access your account"}, "home": {"welcome": "Welcome", "recentTransactions": "Recent Transactions", "viewAll": "View All", "quickActions": "Quick Actions", "offers": "Offers", "notifications": "Notifications", "balance": "Balance", "points": "Points", "rewards": "Rewards"}, "recharge": {"mobileRecharge": "Mobile Recharge", "operator": "Operator", "selectOperator": "Select Operator", "amount": "Amount", "enterAmount": "Enter Amount", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter Phone Number", "rechargeHistory": "Recharge History", "rechargeDetails": "Recharge Details", "rechargeSuccess": "Recharge Successful", "rechargeFailed": "Recharge Failed", "offerPackages": "Offer Packages", "data": "Data", "minutes": "Minutes", "sms": "SMS", "validity": "Validity", "days": "Days", "activate": "Activate", "activatePackage": "Activate Package", "packageActivated": "Package Activated", "packageActivationFailed": "Package Activation Failed", "grameenphone": "Grameenphone", "robi": "<PERSON><PERSON>", "airtel": "Airtel", "banglalink": "Banglalink", "teletalk": "Teletalk"}, "banking": {"mobileBanking": "Mobile Banking", "sendMoney": "Send Money", "cashOut": "Cash Out", "payBill": "Pay Bill", "addMoney": "Add Money", "bankTransfer": "Bank Transfer", "wallet": "Wallet", "selectWallet": "Select Wallet", "recipientNumber": "Recipient Number", "enterRecipientNumber": "Enter Recipient Number", "transactionType": "Transaction Type", "personal": "Personal", "agent": "Agent", "merchant": "Merchant", "reference": "Reference", "enterReference": "Enter Reference (Optional)", "transactionPin": "Transaction PIN", "enterTransactionPin": "Enter Transaction PIN", "transactionSuccess": "Transaction Successful", "transactionFailed": "Transaction Failed", "transactionHistory": "Transaction History", "transactionDetails": "Transaction Details", "bkash": "bKash", "nagad": "Nagad", "rocket": "Rocket", "upay": "Upay", "mcash": "mCash", "okWallet": "OK Wallet", "tap": "Tap", "sureCash": "SureCash", "islamiBank": "Islami Bank mCash", "cellFin": "CellFin"}, "bills": {"utilityBills": "Utility Bills", "electricity": "Electricity", "gas": "Gas", "water": "Water", "internet": "Internet & Cable TV", "examFees": "<PERSON><PERSON>", "selectBillType": "Select Bill Type", "billType": "<PERSON>", "accountNumber": "Account Number", "enterAccountNumber": "Enter Account Number", "billAmount": "<PERSON>", "dueDate": "Due Date", "paymentDate": "Payment Date", "billStatus": "Bill Status", "paid": "Paid", "unpaid": "Unpaid", "partial": "Partial", "fetchBill": "<PERSON>tch Bill", "payBill": "Pay Bill", "billPaymentSuccess": "Bill Payment Successful", "billPaymentFailed": "Bill Payment Failed", "billHistory": "Bill <PERSON>", "billDetails": "<PERSON>", "noBillFound": "No bill found for this account", "invalidAccountNumber": "Invalid account number"}, "ecommerce": {"shop": "Shop", "products": "Products", "categories": "Categories", "cart": "<PERSON><PERSON>", "checkout": "Checkout", "orders": "Orders", "orderHistory": "Order History", "orderDetails": "Order Details", "orderSuccess": "Order Placed Successfully", "orderFailed": "Order Failed", "addToCart": "Add to Cart", "buyNow": "Buy Now", "quantity": "Quantity", "price": "Price", "totalPrice": "Total Price", "discount": "Discount", "subtotal": "Subtotal", "shipping": "Shipping", "tax": "Tax", "total": "Total", "emptyCart": "Your cart is empty", "continueShipping": "Continue Shopping", "proceedToCheckout": "Proceed to Checkout", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery", "onlinePayment": "Online Payment", "placeOrder": "Place Order", "orderStatus": "Order Status", "pending": "Pending", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "returnPolicy": "Return Policy", "reviews": "Reviews", "writeReview": "Write a Review", "rating": "Rating", "comment": "Comment"}, "wallet": {"myWallet": "My Wallet", "balance": "Balance", "addMoney": "Add Money", "withdraw": "Withdraw", "transfer": "Transfer", "transactions": "Transactions", "transactionHistory": "Transaction History", "transactionDetails": "Transaction Details", "transactionId": "Transaction ID", "transactionType": "Transaction Type", "transactionStatus": "Transaction Status", "transactionDate": "Transaction Date", "transactionAmount": "Transaction Amount", "transactionFee": "Transaction Fee", "source": "Source", "destination": "Destination", "description": "Description", "addMoneySuccess": "Money Added Successfully", "addMoneyFailed": "Failed to Add Money", "withdrawSuccess": "<PERSON><PERSON><PERSON> Successful", "withdrawFailed": "Withdrawal Failed", "transferSuccess": "Transfer Successful", "transferFailed": "Transfer Failed", "insufficientBalance": "Insufficient Balance", "selectSource": "Select Source", "enterAmount": "Enter Amount", "minAmount": "Minimum amount is", "maxAmount": "Maximum amount is"}, "profile": {"myProfile": "My Profile", "editProfile": "Edit Profile", "personalInfo": "Personal Information", "name": "Name", "phone": "Phone", "email": "Email", "address": "Address", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "changePassword": "Change Password", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "notifications": "Notifications", "security": "Security", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "helpSupport": "Help & Support", "contactUs": "Contact Us", "faq": "FAQ", "about": "About", "version": "Version", "logout": "Logout", "deleteAccount": "Delete Account", "deleteAccountConfirmation": "Are you sure you want to delete your account? This action cannot be undone.", "profileUpdateSuccess": "Profile updated successfully", "profileUpdateFailed": "Failed to update profile"}, "settings": {"appSettings": "App Settings", "language": "Language", "english": "English", "bangla": "Bangla", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "notifications": "Notifications", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "security": "Security", "biometricAuthentication": "Biometric Authentication", "pinAuthentication": "PIN Authentication", "changePin": "Change PIN", "autoLock": "Auto Lock", "immediately": "Immediately", "after1Minute": "After 1 minute", "after5Minutes": "After 5 minutes", "after15Minutes": "After 15 minutes", "after30Minutes": "After 30 minutes", "never": "Never", "dataUsage": "Data Usage", "clearCache": "<PERSON>ache", "cacheCleared": "<PERSON><PERSON> cleared successfully", "about": "About", "version": "Version", "buildNumber": "Build Number", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "licenses": "Licenses", "contactUs": "Contact Us", "feedback": "Send Feedback", "rateApp": "Rate App"}}
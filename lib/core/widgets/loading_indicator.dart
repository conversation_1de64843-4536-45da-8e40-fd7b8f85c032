import 'package:flutter/material.dart';
import 'package:irecharge_pro/core/constants/color_constants.dart';
import 'package:lottie/lottie.dart';
import 'package:irecharge_pro/core/constants/asset_constants.dart';

/// Loading indicator widget
class LoadingIndicator extends StatelessWidget {
  final double size;
  final Color color;
  final bool useAnimation;
  final String? message;

  const LoadingIndicator({
    super.key,
    this.size = 48,
    this.color = ColorConstants.primaryColor,
    this.useAnimation = true,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (useAnimation)
            Lottie.asset(
              AssetConstants.loadingAnimation,
              width: size * 2,
              height: size * 2,
              errorBuilder: (context, error, stackTrace) {
                return SizedBox(
                  width: size,
                  height: size,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                    strokeWidth: 3,
                  ),
                );
              },
            )
          else
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(color),
                strokeWidth: 3,
              ),
            ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: TextStyle(
                color: ColorConstants.textSecondaryColor,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Full screen loading indicator with optional background
class FullScreenLoading extends StatelessWidget {
  final bool useAnimation;
  final String? message;
  final bool showBackground;

  const FullScreenLoading({
    super.key,
    this.useAnimation = true,
    this.message,
    this.showBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: showBackground
          ? Colors.black.withAlpha(76) // 0.3 opacity = 76/255 alpha
          : Colors.transparent,
      child: LoadingIndicator(
        useAnimation: useAnimation,
        message: message,
      ),
    );
  }
}

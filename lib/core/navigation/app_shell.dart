import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:irecharge_pro/routes/route_names.dart';

/// Navigation shell that contains the bottom navigation bar
class AppShell extends ConsumerStatefulWidget {
  final Widget child;
  final int currentIndex;

  const AppShell({
    super.key,
    required this.child,
    required this.currentIndex,
  }) : assert(currentIndex >= -10 && currentIndex <= 10, 
              'currentIndex must be reasonable: $currentIndex');

  @override
  ConsumerState<AppShell> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  @override
  Widget build(BuildContext context) {
    // Ensure currentIndex is within valid range with extra safety
    int safeCurrentIndex = widget.currentIndex;
    
    // Debug logging to track the issue
    if (safeCurrentIndex < 0 || safeCurrentIndex > 3) {
      debugPrint('WARNING: Invalid currentIndex received: $safeCurrentIndex');
    }
    
    // Apply bounds with fallback to 0 for any invalid values
    safeCurrentIndex = safeCurrentIndex.clamp(0, 3);
    
    // Additional safety check
    if (safeCurrentIndex < 0 || safeCurrentIndex > 3) {
      safeCurrentIndex = 0; // Ultimate fallback
      debugPrint('FALLBACK: Using currentIndex 0 as ultimate fallback');
    }
    
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: safeCurrentIndex,
        onTap: (index) {
          // Additional bounds checking on tap
          if (index >= 0 && index <= 3) {
            _navigateToTab(index);
          }
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.phone_android_outlined),
            activeIcon: Icon(Icons.phone_android),
            label: 'Recharge',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.shopping_bag_outlined),
            activeIcon: Icon(Icons.shopping_bag),
            label: 'Shop',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history_outlined),
            activeIcon: Icon(Icons.history),
            label: 'History',
          ),
        ],
      ),
    );
  }

  /// Navigate to the selected tab
  void _navigateToTab(int index) {
    switch (index) {
      case 0:
        context.go(RouteNames.home);
        break;
      case 1:
        context.go(RouteNames.recharge);
        break;
      case 2:
        context.go(RouteNames.shop);
        break;
      case 3:
        context.go(RouteNames.history);
        break;
    }
  }
}

/// Provider for the current navigation index
final navigationIndexProvider = StateProvider<int>((ref) => 0);

/// Validation utility functions
class Validators {
  /// Validates a phone number (Bangladesh format)
  /// 
  /// Valid formats:
  /// - 01XXXXXXXXX (11 digits)
  /// - +8801XXXXXXXXX (14 digits)
  static String? validateBangladeshPhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    
    // Remove any spaces
    final phone = value.replaceAll(' ', '');
    
    // Check for Bangladesh phone format
    final RegExp regExp11Digits = RegExp(r'^01[0-9]{9}$');
    final RegExp regExp14Digits = RegExp(r'^\+8801[0-9]{9}$');
    
    if (!regExp11Digits.hasMatch(phone) && !regExp14Digits.hasMatch(phone)) {
      return 'Enter a valid Bangladesh phone number';
    }
    
    return null;
  }

  /// Validates an email address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final RegExp emailRegExp = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegExp.hasMatch(value)) {
      return 'Enter a valid email address';
    }
    
    return null;
  }

  /// Validates a password
  /// 
  /// Requirements:
  /// - At least 8 characters
  /// - Contains at least one uppercase letter
  /// - Contains at least one lowercase letter
  /// - Contains at least one number
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    
    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    
    if (!value.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    
    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    
    return null;
  }

  /// Validates a PIN (4 or 6 digits)
  static String? validatePin(String? value) {
    if (value == null || value.isEmpty) {
      return 'PIN is required';
    }
    
    final RegExp regExp4Digits = RegExp(r'^[0-9]{4}$');
    final RegExp regExp6Digits = RegExp(r'^[0-9]{6}$');
    
    if (!regExp4Digits.hasMatch(value) && !regExp6Digits.hasMatch(value)) {
      return 'PIN must be 4 or 6 digits';
    }
    
    return null;
  }

  /// Validates a required field
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validates an amount (numeric and greater than zero)
  static String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Amount is required';
    }
    
    final double? amount = double.tryParse(value);
    if (amount == null) {
      return 'Enter a valid amount';
    }
    
    if (amount <= 0) {
      return 'Amount must be greater than zero';
    }
    
    return null;
  }
}

import 'package:connectivity_plus/connectivity_plus.dart';

/// Utility for checking network connectivity
class ConnectivityUtil {
  static final Connectivity _connectivity = Connectivity();
  
  /// Check if the device is connected to the internet
  static Future<bool> isConnected() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }
  
  /// Stream of connectivity changes
  static Stream<ConnectivityResult> get onConnectivityChanged => 
      _connectivity.onConnectivityChanged;
}

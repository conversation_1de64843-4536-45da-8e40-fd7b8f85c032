import 'package:flutter/material.dart';
import 'package:irecharge_pro/data/models/bill_payment_transaction_model.dart';
import 'package:irecharge_pro/data/models/mobile_banking_transaction_model.dart';
import 'package:irecharge_pro/data/models/mobile_operator_model.dart';
import 'package:irecharge_pro/data/models/recharge_transaction_model.dart';
import 'package:irecharge_pro/data/models/bank_model.dart';
import 'package:irecharge_pro/data/models/unified_transaction_model.dart';

/// Service for sharing transactions
class ShareService {
  /// Share a transaction as text
  static Future<void> shareTransactionAsText(UnifiedTransaction transaction) async {
    String shareText = '';

    // Common header
    shareText += 'iRecharge Pro - Transaction Receipt\n';
    shareText += '--------------------------------\n\n';

    // Transaction details based on category
    switch (transaction.category) {
      case TransactionCategory.recharge:
        shareText += _buildRechargeShareText(transaction.originalTransaction as RechargeTransaction);
        break;
      case TransactionCategory.mobileBanking:
        shareText += _buildMobileBankingShareText(transaction.originalTransaction as MobileBankingTransaction);
        break;
      case TransactionCategory.billPayment:
        shareText += _buildBillPaymentShareText(transaction.originalTransaction as BillPaymentTransaction);
        break;
      case TransactionCategory.bankTransfer:
        shareText += _buildBankTransferShareText(transaction.originalTransaction as BankTransfer);
        break;
      case TransactionCategory.wallet:
        shareText += 'Wallet Transaction\n';
        shareText += 'ID: ${transaction.id}\n';
        shareText += 'Amount: ${transaction.formattedAmount}\n';
        shareText += 'Date: ${transaction.formattedDate} ${transaction.formattedTime}\n';
        shareText += 'Status: ${transaction.statusText}\n';
        break;
    }

    // Footer
    shareText += '\n--------------------------------\n';
    shareText += 'Thank you for using iRecharge Pro!\n';

    // Share functionality will be implemented later
    // For now, just return the text
  }

  /// Share a transaction as image
  static Future<void> shareTransactionAsImage(BuildContext context, UnifiedTransaction transaction) async {
    // For now, just share as text since screenshot package has compatibility issues
    await shareTransactionAsText(transaction);

    // Show a message that image sharing is coming soon
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Share as image functionality coming soon'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }





  /// Build recharge share text
  static String _buildRechargeShareText(RechargeTransaction transaction) {
    final operator = MobileOperator.getOperator(transaction.operatorId);

    String text = 'Mobile Recharge\n\n';
    text += 'Transaction ID: ${transaction.id}\n';
    text += 'Phone Number: ${transaction.phoneNumber}\n';
    text += 'Operator: ${operator?.name ?? 'Unknown'}\n';
    if (transaction.packageId != null) {
      text += 'Package: ${transaction.packageId}\n';
    }
    text += 'Amount: ৳${transaction.amount.toStringAsFixed(2)}\n';
    text += 'Payment Method: ${_getPaymentMethodText(transaction.paymentMethod)}\n';
    text += 'Status: ${_getRechargeStatusText(transaction.status)}\n';
    text += 'Date: ${_formatDate(transaction.timestamp)} ${_formatTime(transaction.timestamp)}\n';
    if (transaction.transactionReference != null) {
      text += 'Reference: ${transaction.transactionReference}\n';
    }

    return text;
  }

  /// Build mobile banking share text
  static String _buildMobileBankingShareText(MobileBankingTransaction transaction) {
    String text = 'Mobile Banking - ${transaction.typeText}\n\n';
    text += 'Transaction ID: ${transaction.id}\n';
    text += 'Phone Number: ${transaction.phoneNumber}\n';
    text += 'Operator: ${transaction.operator?.name ?? 'Unknown'}\n';
    text += 'Type: ${transaction.typeText}\n';
    text += 'Amount: ${transaction.formattedAmount}\n';
    if (transaction.fee != null && transaction.fee! > 0) {
      text += 'Fee: ${transaction.formattedFee}\n';
      text += 'Total: ${transaction.formattedTotalAmount}\n';
    }
    text += 'Status: ${transaction.statusText}\n';
    text += 'Date: ${_formatDate(transaction.timestamp)} ${_formatTime(transaction.timestamp)}\n';
    if (transaction.recipientName != null) {
      text += 'Recipient: ${transaction.recipientName}\n';
    }
    if (transaction.transactionReference != null) {
      text += 'Reference: ${transaction.transactionReference}\n';
    }

    return text;
  }

  /// Build bill payment share text
  static String _buildBillPaymentShareText(BillPaymentTransaction transaction) {
    String text = 'Bill Payment - ${transaction.typeText}\n\n';
    text += 'Transaction ID: ${transaction.id}\n';
    text += 'Provider: ${transaction.provider}\n';
    text += 'Account Number: ${transaction.accountNumber}\n';
    text += 'Bill Type: ${transaction.typeText}\n';
    if (transaction.billNumber != null) {
      text += 'Bill Number: ${transaction.billNumber}\n';
    }
    if (transaction.billMonth != null) {
      text += 'Bill Month: ${transaction.billMonth}\n';
    }
    if (transaction.customerName != null) {
      text += 'Customer Name: ${transaction.customerName}\n';
    }
    text += 'Amount: ${transaction.formattedAmount}\n';
    if (transaction.fee != null && transaction.fee! > 0) {
      text += 'Fee: ${transaction.formattedFee}\n';
      text += 'Total: ${transaction.formattedTotalAmount}\n';
    }
    text += 'Status: ${transaction.statusText}\n';
    text += 'Date: ${_formatDate(transaction.timestamp)} ${_formatTime(transaction.timestamp)}\n';
    if (transaction.transactionReference != null) {
      text += 'Reference: ${transaction.transactionReference}\n';
    }

    return text;
  }

  /// Build bank transfer share text
  static String _buildBankTransferShareText(BankTransfer transfer) {
    String text = 'Bank Transfer\n\n';
    text += 'Transaction ID: ${transfer.id}\n';
    text += 'Bank: ${transfer.bank?.name ?? 'Unknown'}\n';
    text += 'Account Number: ${transfer.accountNumber}\n';
    text += 'Account Name: ${transfer.accountName}\n';
    if (transfer.branchName != null) {
      text += 'Branch: ${transfer.branchName}\n';
    }
    text += 'Account Type: ${_getAccountTypeText(transfer.accountType)}\n';
    text += 'Amount: ৳${transfer.amount.toStringAsFixed(2)}\n';
    text += 'Status: ${_getBankTransferStatusText(transfer.status)}\n';
    text += 'Date: ${_formatDate(transfer.date)} ${_formatTime(transfer.date)}\n';
    if (transfer.reference != null) {
      text += 'Reference: ${transfer.reference}\n';
    }
    if (transfer.note != null) {
      text += 'Note: ${transfer.note}\n';
    }

    return text;
  }

  /// Format date
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Format time
  static String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Get payment method text
  static String _getPaymentMethodText(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.card:
        return 'Card';
      case PaymentMethod.mobileBanking:
        return 'Mobile Banking';
      case PaymentMethod.internetBanking:
        return 'Internet Banking';
    }
  }

  /// Get recharge status text
  static String _getRechargeStatusText(RechargeStatus status) {
    switch (status) {
      case RechargeStatus.pending:
        return 'Pending';
      case RechargeStatus.processing:
        return 'Processing';
      case RechargeStatus.completed:
        return 'Completed';
      case RechargeStatus.failed:
        return 'Failed';
      case RechargeStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get bank transfer status text
  static String _getBankTransferStatusText(BankTransferStatus status) {
    switch (status) {
      case BankTransferStatus.pending:
        return 'Pending';
      case BankTransferStatus.processing:
        return 'Processing';
      case BankTransferStatus.completed:
        return 'Completed';
      case BankTransferStatus.failed:
        return 'Failed';
      case BankTransferStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get account type text
  static String _getAccountTypeText(BankAccountType type) {
    switch (type) {
      case BankAccountType.savings:
        return 'Savings';
      case BankAccountType.current:
        return 'Current';
      case BankAccountType.fixed:
        return 'Fixed Deposit';
      case BankAccountType.other:
        return 'Other';
    }
  }
}


import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:irecharge_pro/core/services/api_service.dart';
import 'package:irecharge_pro/core/services/storage_service.dart';
import 'package:irecharge_pro/core/utils/logger_util.dart';
import 'package:local_auth/local_auth.dart';
import 'dart:convert';

/// Service for handling authentication
class AuthService {
  final ApiService _apiService;
  final StorageService _storageService;
  final LocalAuthentication _localAuth = LocalAuthentication();

  // Demo mode flag
  bool _isDemoMode = true;

  // Demo user credentials
  static const Map<String, String> _demoCredentials = {
    'phone': '01712345678',
    'password': 'Password123',
  };

  // Demo user data
  static final Map<String, dynamic> _demoUserData = {
    'id': '1',
    'name': 'Demo User',
    'phone': '01712345678',
    'email': '<EMAIL>',
    'balance': 5000.0,
    'created_at': '2023-01-01T00:00:00.000Z',
  };

  AuthService({
    required ApiService apiService,
    required StorageService storageService,
  })  : _apiService = apiService,
        _storageService = storageService;

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _storageService.getSecureString(AppConstants.tokenKey);
    return token != null;
  }

  /// Login with phone and password
  Future<bool> login(String phone, String password) async {
    // Check for demo credentials
    if (_isDemoMode &&
        phone == _demoCredentials['phone'] &&
        password == _demoCredentials['password']) {

      LoggerUtil.i('Demo login successful');

      // Generate a demo token
      final demoToken = base64Encode(utf8.encode('demo_token_${DateTime.now().millisecondsSinceEpoch}'));

      // Save demo token and user data
      await _storageService.setSecureString(AppConstants.tokenKey, demoToken);
      await _storageService.setObject(AppConstants.userKey, _demoUserData);

      // Set token for API requests
      _apiService.setAuthToken(demoToken);

      return true;
    }

    // Try regular login if not in demo mode or credentials don't match
    if (!_isDemoMode) {
      try {
        final response = await _apiService.post(
          '/auth/login',
          data: {
            'phone': phone,
            'password': password,
          },
        );

        if (response.statusCode == 200) {
          final token = response.data['token'] as String;
          final userData = response.data['user'] as Map<String, dynamic>;

          // Save token and user data
          await _storageService.setSecureString(AppConstants.tokenKey, token);
          await _storageService.setObject(AppConstants.userKey, userData);

          // Set token for API requests
          _apiService.setAuthToken(token);

          return true;
        }

        return false;
      } catch (e) {
        LoggerUtil.e('Login error', e);
        return false;
      }
    }

    return false;
  }

  /// Register a new user
  Future<bool> register(Map<String, dynamic> userData) async {
    // In demo mode, always return success
    if (_isDemoMode) {
      LoggerUtil.i('Demo registration successful');
      return true;
    }

    try {
      final response = await _apiService.post(
        '/auth/register',
        data: userData,
      );

      if (response.statusCode == 201) {
        return true;
      }

      return false;
    } catch (e) {
      LoggerUtil.e('Registration error', e);
      return false;
    }
  }

  /// Logout the current user
  Future<void> logout() async {
    if (!_isDemoMode) {
      try {
        // Call logout API
        await _apiService.post('/auth/logout');
      } catch (e) {
        LoggerUtil.e('Logout API error', e);
      }
    } else {
      LoggerUtil.i('Demo logout successful');
    }

    // Clear local storage
    await _storageService.removeSecure(AppConstants.tokenKey);
    await _storageService.remove(AppConstants.userKey);

    // Remove token from API requests
    _apiService.removeAuthToken();
  }

  /// Request password reset
  Future<bool> requestPasswordReset(String phone) async {
    try {
      final response = await _apiService.post(
        '/auth/password/reset-request',
        data: {'phone': phone},
      );

      return response.statusCode == 200;
    } catch (e) {
      LoggerUtil.e('Password reset request error', e);
      return false;
    }
  }

  /// Verify OTP for password reset
  Future<bool> verifyOtp(String phone, String otp) async {
    try {
      final response = await _apiService.post(
        '/auth/otp/verify',
        data: {
          'phone': phone,
          'otp': otp,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      LoggerUtil.e('OTP verification error', e);
      return false;
    }
  }

  /// Reset password
  Future<bool> resetPassword(String phone, String otp, String newPassword) async {
    try {
      final response = await _apiService.post(
        '/auth/password/reset',
        data: {
          'phone': phone,
          'otp': otp,
          'password': newPassword,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      LoggerUtil.e('Password reset error', e);
      return false;
    }
  }

  /// Change password
  Future<bool> changePassword(String currentPassword, String newPassword) async {
    try {
      final response = await _apiService.post(
        '/auth/password/change',
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      LoggerUtil.e('Change password error', e);
      return false;
    }
  }

  /// Get current user data
  Future<Map<String, dynamic>?> getCurrentUser() async {
    return _storageService.getObject(AppConstants.userKey);
  }

  /// Update user profile
  Future<bool> updateProfile(Map<String, dynamic> userData) async {
    try {
      final response = await _apiService.put(
        '/user/profile',
        data: userData,
      );

      if (response.statusCode == 200) {
        // Update local user data
        final updatedUser = response.data['user'] as Map<String, dynamic>;
        await _storageService.setObject(AppConstants.userKey, updatedUser);
        return true;
      }

      return false;
    } catch (e) {
      LoggerUtil.e('Update profile error', e);
      return false;
    }
  }

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return canCheckBiometrics && isDeviceSupported;
    } catch (e) {
      LoggerUtil.e('Biometric availability check error', e);
      return false;
    }
  }

  /// Authenticate with biometrics
  Future<bool> authenticateWithBiometrics() async {
    try {
      return await _localAuth.authenticate(
        localizedReason: 'Authenticate to access the app',
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
    } catch (e) {
      LoggerUtil.e('Biometric authentication error', e);
      return false;
    }
  }
}

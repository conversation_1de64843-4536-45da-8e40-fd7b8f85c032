import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:irecharge_pro/core/constants/app_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling local storage operations
class StorageService {
  late final SharedPreferences _prefs;
  late final FlutterSecureStorage _secureStorage;
  
  // Singleton instance
  static final StorageService _instance = StorageService._internal();
  
  factory StorageService() => _instance;
  
  StorageService._internal();
  
  /// Initialize storage services
  Future<void> init() async {
    // Initialize Hive
    await Hive.initFlutter();
    
    // Open Hive boxes
    await Hive.openBox(AppConstants.settingsBox);
    await Hive.openBox(AppConstants.userBox);
    await Hive.openBox(AppConstants.transactionsBox);
    
    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
    
    // Initialize FlutterSecureStorage
    _secureStorage = const FlutterSecureStorage();
  }
  
  // SharedPreferences methods
  
  /// Save a string value
  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }
  
  /// Get a string value
  String? getString(String key) {
    return _prefs.getString(key);
  }
  
  /// Save a boolean value
  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }
  
  /// Get a boolean value
  bool? getBool(String key) {
    return _prefs.getBool(key);
  }
  
  /// Save an integer value
  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }
  
  /// Get an integer value
  int? getInt(String key) {
    return _prefs.getInt(key);
  }
  
  /// Save a double value
  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }
  
  /// Get a double value
  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }
  
  /// Save a list of strings
  Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }
  
  /// Get a list of strings
  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }
  
  /// Remove a value
  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }
  
  /// Clear all values
  Future<bool> clear() async {
    return await _prefs.clear();
  }
  
  // Secure Storage methods
  
  /// Save a secure string value
  Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }
  
  /// Get a secure string value
  Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }
  
  /// Remove a secure value
  Future<void> removeSecure(String key) async {
    await _secureStorage.delete(key: key);
  }
  
  /// Clear all secure values
  Future<void> clearSecure() async {
    await _secureStorage.deleteAll();
  }
  
  // Hive methods
  
  /// Save a value to a Hive box
  Future<void> setHiveValue(String boxName, String key, dynamic value) async {
    final box = Hive.box(boxName);
    await box.put(key, value);
  }
  
  /// Get a value from a Hive box
  dynamic getHiveValue(String boxName, String key) {
    final box = Hive.box(boxName);
    return box.get(key);
  }
  
  /// Remove a value from a Hive box
  Future<void> removeHiveValue(String boxName, String key) async {
    final box = Hive.box(boxName);
    await box.delete(key);
  }
  
  /// Clear a Hive box
  Future<void> clearHiveBox(String boxName) async {
    final box = Hive.box(boxName);
    await box.clear();
  }
  
  // JSON object storage
  
  /// Save an object as JSON
  Future<bool> setObject(String key, Map<String, dynamic> value) async {
    return await _prefs.setString(key, jsonEncode(value));
  }
  
  /// Get an object from JSON
  Map<String, dynamic>? getObject(String key) {
    final String? jsonString = _prefs.getString(key);
    if (jsonString == null) return null;
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }
}

import 'package:flutter/material.dart';

/// Color constants used throughout the app
class ColorConstants {
  // Primary colors
  static const Color primaryColor = Color(0xFF1976D2); // Blue
  static const Color primaryLightColor = Color(0xFF42A5F5);
  static const Color primaryDarkColor = Color(0xFF0D47A1);
  
  // Secondary colors
  static const Color secondaryColor = Color(0xFF00C853); // Green
  static const Color secondaryLightColor = Color(0xFF5EFC82);
  static const Color secondaryDarkColor = Color(0xFF009624);
  
  // Accent colors
  static const Color accentColor = Color(0xFFFFA000); // Amber
  
  // Background colors
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardColor = Colors.white;
  
  // Text colors
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color textLightColor = Color(0xFFBDBDBD);
  
  // Status colors
  static const Color successColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFE53935);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color infoColor = Color(0xFF2196F3);
  
  // Operator colors (for Bangladesh telecom operators)
  static const Color grameenphoneColor = Color(0xFF1976D2); // Blue
  static const Color robiColor = Color(0xFFE53935); // Red
  static const Color airtelColor = Color(0xFFE53935); // Red
  static const Color banglalinkColor = Color(0xFFFFA000); // Orange
  static const Color teletalkColor = Color(0xFF4CAF50); // Green
  
  // Mobile banking colors
  static const Color bkashColor = Color(0xFFE91E63); // Pink
  static const Color nagadColor = Color(0xFFFF5722); // Deep Orange
  static const Color rocketColor = Color(0xFF673AB7); // Deep Purple
  static const Color upayColor = Color(0xFF009688); // Teal
}

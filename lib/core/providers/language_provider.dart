import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Supported languages
enum AppLanguage {
  english('en', 'English', 'US'),
  bengali('bn', 'বাংলা', 'BD');

  final String code;
  final String name;
  final String countryCode;

  const AppLanguage(this.code, this.name, this.countryCode);

  Locale get locale => Locale(code, countryCode);

  static AppLanguage fromCode(String code) {
    return AppLanguage.values.firstWhere(
      (language) => language.code == code,
      orElse: () => AppLanguage.english,
    );
  }
}

/// Language state notifier
class LanguageNotifier extends StateNotifier<AppLanguage> {
  LanguageNotifier() : super(AppLanguage.english) {
    _loadLanguage();
  }

  /// Load language from shared preferences
  Future<void> _loadLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final languageCode = prefs.getString('language_code') ?? 'en';
    state = AppLanguage.fromCode(languageCode);
  }

  /// Save language to shared preferences
  Future<void> _saveLanguage(AppLanguage language) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language_code', language.code);
  }

  /// Set language
  Future<void> setLanguage(AppLanguage language) async {
    state = language;
    await _saveLanguage(language);
  }
}

/// Language provider
final languageProvider = StateNotifierProvider<LanguageNotifier, AppLanguage>((ref) {
  return LanguageNotifier();
});

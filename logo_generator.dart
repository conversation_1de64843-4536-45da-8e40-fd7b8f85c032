import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Create a simple logo
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  // Define the size
  const size = Size(512.0, 512.0);
  
  // Draw background
  final bgPaint = Paint()
    ..color = const Color(0xFF1976D2) // Primary blue color
    ..style = PaintingStyle.fill;
  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), bgPaint);
  
  // Draw a wallet icon
  final walletPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.fill;
  
  // Wallet base
  final walletRect = Rect.fromLTWH(
    size.width * 0.2, 
    size.height * 0.3, 
    size.width * 0.6, 
    size.height * 0.4
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(walletRect, Radius.circular(20)),
    walletPaint
  );
  
  // Wallet flap
  final flapRect = Rect.fromLTWH(
    size.width * 0.2, 
    size.height * 0.3, 
    size.width * 0.6, 
    size.height * 0.15
  );
  canvas.drawRRect(
    RRect.fromRectAndRadius(flapRect, Radius.circular(20)),
    Paint()..color = const Color(0xFFE0E0E0)
  );
  
  // Draw a money symbol
  final textPainter = TextPainter(
    text: const TextSpan(
      text: '৳',
      style: TextStyle(
        color: Color(0xFF1976D2),
        fontSize: 180,
        fontWeight: FontWeight.bold,
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  textPainter.layout();
  textPainter.paint(
    canvas, 
    Offset(
      size.width / 2 - textPainter.width / 2,
      size.height / 2 - textPainter.height / 2 + 20
    )
  );
  
  // Draw app name
  final namePainter = TextPainter(
    text: const TextSpan(
      text: 'iRecharge Pro',
      style: TextStyle(
        color: Colors.white,
        fontSize: 60,
        fontWeight: FontWeight.bold,
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  namePainter.layout();
  namePainter.paint(
    canvas, 
    Offset(
      size.width / 2 - namePainter.width / 2,
      size.height * 0.8
    )
  );
  
  // Convert to an image
  final picture = recorder.endRecording();
  final img = await picture.toImage(size.width.toInt(), size.height.toInt());
  final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
  final buffer = byteData!.buffer.asUint8List();
  
  // Save the image
  final file = File('assets/images/logo.png');
  await file.writeAsBytes(buffer);
  
  // Create a white version
  final whiteRecorder = ui.PictureRecorder();
  final whiteCanvas = Canvas(whiteRecorder);
  
  // Draw white background
  final whiteBgPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.fill;
  whiteCanvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), whiteBgPaint);
  
  // Draw a wallet icon
  final blueWalletPaint = Paint()
    ..color = const Color(0xFF1976D2)
    ..style = PaintingStyle.fill;
  
  // Wallet base
  whiteCanvas.drawRRect(
    RRect.fromRectAndRadius(walletRect, Radius.circular(20)),
    blueWalletPaint
  );
  
  // Wallet flap
  whiteCanvas.drawRRect(
    RRect.fromRectAndRadius(flapRect, Radius.circular(20)),
    Paint()..color = const Color(0xFF42A5F5)
  );
  
  // Draw a money symbol
  final whiteTextPainter = TextPainter(
    text: const TextSpan(
      text: '৳',
      style: TextStyle(
        color: Colors.white,
        fontSize: 180,
        fontWeight: FontWeight.bold,
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  whiteTextPainter.layout();
  whiteTextPainter.paint(
    whiteCanvas, 
    Offset(
      size.width / 2 - whiteTextPainter.width / 2,
      size.height / 2 - whiteTextPainter.height / 2 + 20
    )
  );
  
  // Draw app name
  final whiteNamePainter = TextPainter(
    text: const TextSpan(
      text: 'iRecharge Pro',
      style: TextStyle(
        color: const Color(0xFF1976D2),
        fontSize: 60,
        fontWeight: FontWeight.bold,
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  whiteNamePainter.layout();
  whiteNamePainter.paint(
    whiteCanvas, 
    Offset(
      size.width / 2 - whiteNamePainter.width / 2,
      size.height * 0.8
    )
  );
  
  // Convert to an image
  final whitePicture = whiteRecorder.endRecording();
  final whiteImg = await whitePicture.toImage(size.width.toInt(), size.height.toInt());
  final whiteByteData = await whiteImg.toByteData(format: ui.ImageByteFormat.png);
  final whiteBuffer = whiteByteData!.buffer.asUint8List();
  
  // Save the white version
  final whiteFile = File('assets/images/logo_white.png');
  await whiteFile.writeAsBytes(whiteBuffer);
  
  print('Logo images generated successfully!');
  exit(0);
}

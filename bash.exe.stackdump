Stack trace:
Frame         Function      Args
0007FFFF7B30  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF7B30, 0007FFFF6A30) msys-2.0.dll+0x1FE8E
0007FFFF7B30  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7E08) msys-2.0.dll+0x67F9
0007FFFF7B30  000210046832 (000210286019, 0007FFFF79E8, 0007FFFF7B30, 000000000000) msys-2.0.dll+0x6832
0007FFFF7B30  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7B30  000210068E24 (0007FFFF7B40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7E10  00021006A225 (0007FFFF7B40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEB6960000 ntdll.dll
7FFEB5AA0000 KERNEL32.DLL
7FFEB3F80000 KERNELBASE.dll
7FFEB4FC0000 USER32.dll
7FFEB3C00000 win32u.dll
7FFEB4F90000 GDI32.dll
7FFEB44A0000 gdi32full.dll
7FFEB3CC0000 msvcp_win.dll
7FFEB4350000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEB5930000 advapi32.dll
7FFEB6310000 msvcrt.dll
7FFEB51E0000 sechost.dll
7FFEB61E0000 RPCRT4.dll
7FFEB3040000 CRYPTBASE.DLL
7FFEB3D70000 bcryptPrimitives.dll
7FFEB5560000 IMM32.DLL

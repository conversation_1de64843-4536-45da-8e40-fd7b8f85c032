name: irecharge_pro
description: "A Flutter app for Bangladesh-based digital services including mobile recharge, banking, bill payments, and e-commerce."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.5.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0
  lottie: ^3.1.0

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Navigation
  go_router: ^13.2.1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # Network and API
  dio: ^5.4.1
  connectivity_plus: ^5.0.2

  # Authentication and Security
  flutter_secure_storage: ^8.1.0
  local_auth: ^2.1.8

  # Utilities
  intl: ^0.20.2
  logger: ^2.0.2+1
  url_launcher: ^6.2.5
  image_picker: ^1.0.7
  permission_handler: ^11.3.0
  flutter_contacts: ^1.1.7+1
  font_awesome_flutter: ^10.7.0
  path_provider: ^2.1.2

  # Firebase
  firebase_core: ^2.27.1
  firebase_auth: ^4.17.9
  firebase_messaging: ^14.7.20

  # Payment Integration
  # Note: Add specific payment gateway packages for Bangladesh when needed

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and code quality
  flutter_lints: ^5.0.0

  # Code generation
  build_runner: ^2.4.8
  riverpod_generator: ^2.3.11
  hive_generator: ^2.0.1

  # Testing
  mockito: ^5.4.4

  # App icons and splash screen
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for the application
  assets:
    - assets/images/
    - assets/images/products/
    - assets/images/bills/
    - assets/images/tickets/
    - assets/images/operators/
    - assets/images/banking/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
    - assets/lang/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
